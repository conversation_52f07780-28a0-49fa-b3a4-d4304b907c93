# 课件上传 - 快速开始指南

## 一句话总结
通过后端代理接口解决前端直接上传到腾讯云COS的CORS跨域问题。

## 问题和解决方案对比

### ❌ 原来的问题
```
前端 ──直接上传──> 腾讯云COS
         ↓
    浏览器CORS预检
         ↓
    被COS拒绝（跨域）
         ↓
    上传失败
```

### ✅ 现在的解决方案
```
前端 ──代理上传──> 后端 ──服务器上传──> 腾讯云COS
         ↓                    ↓
    同源请求            使用SDK认证
    (无CORS问题)       (服务器级别)
         ↓                    ↓
    成功！              成功！
```

## 修改文件一览

| 文件 | 改动 | 影响范围 |
|------|------|---------|
| `UploadProxyController.java` | `/upload` 接口支持分片上传，返回ETag | 后端分片上传 |
| `minio-upload-chunk.ts` | 改为调用代理接口，保存ETag | 前端分片逻辑 |
| `upload-courseware-button/index.tsx` | 获取ETag并传给合并接口 | 前端UI反馈 |

## 核心改动

### 1. 后端：分片上传处理

```java
// 新增处理分片上传
UploadPartRequest request = new UploadPartRequest();
request.setUploadId(uploadId);
request.setPartNumber(partNumber);
// ... 其他配置

UploadPartResult result = cosClient.uploadPart(request);
String etag = result.getETag();  // 获取ETag
```

### 2. 前端：调用代理接口

```typescript
// 调用代理接口而不是直接调用COS
const proxyUrl = '/backend/v1/upload/cos-proxy/upload';
const params = new URLSearchParams({
  upload_id: uploadId,
  part_number: partNumber,
  filename: filename,
  'content-type': 'application/octet-stream'
});

this.client.put(proxyUrl + '?' + params, fileData, {
  headers: { "X-Original-COS-URL": cosUrl }
});
```

### 3. 前端：保存ETag

```typescript
// 收到响应后保存ETag
const etag = response.data.data.etag;
this.partETags.set(partNumber, etag);  // 保存供合并时使用
```

## 完整的上传流程

### 初始化
```
GET /backend/v1/upload/cos-proxy/minio/upload-id?extension=ppt
← uploadId, objectKey
```

### 逐个上传分片
```
PUT /backend/v1/upload/cos-proxy/upload?
    upload_id=xxx&part_number=1&filename=...&content-type=...
Body: 分片数据
← etag
```

### 合并所有分片
```
POST /backend/v1/upload/cos-proxy/minio/merge-file
Body: {
  upload_id: "xxx",
  filename: "path/file.ppt",
  parts: [
    { partNumber: 1, etag: "abc..." },
    { partNumber: 2, etag: "def..." },
    ...
  ]
}
← 完成！
```

## 关键参数说明

| 参数 | 说明 | 示例 |
|------|------|------|
| `upload_id` | 分片上传会话ID | `f0fcb...` |
| `part_number` | 分片号 | `1, 2, 3...` |
| `etag` | 分片哈希值 | `"abc123..."` |
| `filename` | 对象键（存储路径） | `uploads/2024/10/30/uuid/file.ppt` |

## 数据流示意图

```
┌──────┐
│ 文件 │
└──┬───┘
   │ 5MB × N
   ↓
┌─────────────┐
│ 分片1-N的数据 │
└─────┬───────┘
      │
      ├─> 上传分片1 ──> 代理 ──> COS ──> ETag1
      │
      ├─> 上传分片2 ──> 代理 ──> COS ──> ETag2
      │
      └─> 上传分片N ──> 代理 ──> COS ──> ETagN
                           ↓
                    [收集所有ETag]
                           ↓
                      合并分片
                           ↓
                      文件完成 ✓
```

## 编译检查

```bash
# 后端编译
cd playedu-api
mvn compile
# ✅ BUILD SUCCESS

# 前端编译
cd playedu-admin
npx tsc --noEmit
# ✅ 0 errors
```

## 启动应用

```bash
# 终端1：启动后端
cd playedu-api
mvn spring-boot:run
# 等待: Started PlayeduApplication

# 终端2：启动前端
cd playedu-admin
npm start
# 等待: ✓ ready - started server on 0.0.0.0:3000
```

## 测试上传

1. 登录后台: `http://localhost:3000`
2. 进入: 资源 → 课件
3. 点击: 上传课件
4. 选择: Word/Excel/PPT/PDF等文件
5. 观察：上传进度和完成状态

## 常见问题排查

### 问题：ETag为空
**原因**: COS未正确返回或后端未正确提取
**解决**: 检查后端日志输出的ETag值

### 问题：合并失败
**原因**: 某个分片未上传成功或ETag不匹配
**解决**: 
- 检查所有分片是否都成功
- 查看后端日志中的完整ETag列表

### 问题：CORS错误仍然出现
**原因**: 没有走代理接口
**解决**: 
- 确认前端使用的是 `/backend/v1/upload/cos-proxy/upload`
- 检查后端CORS配置

### 问题：文件变慢
**原因**: 分片上传、网络状况
**解决**: 
- 适当增大分片大小（当前5MB）
- 优化网络连接

## 性能优化建议

### 短期
- [ ] 增大分片大小到10-20MB（网络好的情况）
- [ ] 添加断点续传支持
- [ ] 添加上传队列优化

### 长期
- [ ] 实现并发分片上传（目前是顺序）
- [ ] 添加本地缓存机制
- [ ] 添加上传进度持久化

## 监控和日志

### 后端关键日志输出
```
=== COS SDK分片上传代理调试信息 ===
文件名: file.ppt
上传ID: abc123xyz
分片号: 1
开始上传分片到COS...
分片上传成功！
ETag: "abc123def456"
```

### 前端控制台输出
```
分块 1 上传成功，ETag: abc123def456
所有分块上传完成，准备合并
```

## 相关文档

- 📋 [详细解决方案](./COURSEWARE_UPLOAD_SOLUTION.md)
- 🏗️ [系统架构](./UPLOAD_ARCHITECTURE.md)
- 📖 [腾讯云COS文档](https://www.tencentcloud.com/zh/document/product/436/44015)

## 技术栈

- **后端**: Spring Boot + 腾讯云COS SDK
- **前端**: React + TypeScript + Axios
- **存储**: 腾讯云 COS (分块上传)

---

**最后更新**: 2025-10-30  
**状态**: ✅ 完成并测试通过
