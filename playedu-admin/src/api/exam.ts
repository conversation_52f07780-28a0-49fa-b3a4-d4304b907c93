import client from "./internal/httpClient";

/**
 * 获取考试列表
 */
export function examList(page: number, size: number, title?: string) {
  return client.get("/backend/v1/exams", {
    page,
    size,
    title: title || "",
  });
}

/**
 * 获取考试详情
 */
export function examDetail(id: number) {
  return client.get(`/backend/v1/exams/${id}`, {});
}

/**
 * 创建考试
 */
export function examCreate(data: {
  title: string;
  type: string;
  duration: number;
  pass_score: number;
  description?: string;
}) {
  return client.post("/backend/v1/exams", data);
}

/**
 * 修改考试
 */
export function examUpdate(
  id: number,
  data: {
    title: string;
    type: string;
    duration: number;
    pass_score: number;
    description?: string;
  }
) {
  return client.put(`/backend/v1/exams/${id}`, data);
}

/**
 * 删除考试
 */
export function examDelete(id: number) {
  return client.delete(`/backend/v1/exams/${id}`, {});
}

/**
 * 获取试题列表
 */
export function questionList(
  examId: number,
  page: number,
  size: number,
  title?: string,
  type?: string
) {
  return client.get(`/backend/v1/exams/${examId}/questions`, {
    page,
    size,
    title: title || "",
    type: type || "",
  });
}

/**
 * 获取试题详情
 */
export function questionDetail(id: number) {
  return client.get(`/backend/v1/exams/questions/${id}`, {});
}

/**
 * 创建试题
 */
export function questionCreate(
  examId: number,
  data: {
    title: string;
    type: string;
    difficulty: string;
    score: number;
    analysis?: string;
    answer?: string;
    options?: Array<{
      content: string;
      is_correct: number;
    }>;
  }
) {
  return client.post(`/backend/v1/exams/${examId}/questions`, data);
}

/**
 * 更新试题
 */
export function questionUpdate(id: number, data: any) {
  return client.put(`/backend/v1/exams/questions/${id}`, data);
}

/**
 * 删除试题
 */
export function questionDelete(id: number) {
  return client.delete(`/backend/v1/exams/questions/${id}`, {});
}

/**
 * 获取试题选项
 */
export function questionOptions(id: number) {
  return client.get(`/backend/v1/exams/questions/${id}/options`, {});
}

/**
 * 导入试题
 */
export function questionImport(examId: number, formData: FormData) {
  return client.post(`/backend/v1/exams/${examId}/import`, formData);
}

/**
 * 获取导入日志
 */
export function importLogs(examId: number, page: number, size: number) {
  return client.get(`/backend/v1/exams/${examId}/import-logs`, {
    page,
    size,
  });
}
