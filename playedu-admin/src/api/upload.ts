import client from "./internal/httpClient";

export function minioUploadId(extension: string) {
  return client.post("/backend/v1/upload/cos-proxy/minio/upload-id", {
    extension,
  });
}
export function minioPreSignUrl(
  uploadId: string,
  filename: string,
  partNumber: number
) {
  return client.post("/backend/v1/upload/cos-proxy/minio/pre-sign-url", {
    uploadId,
    objectKey: filename,  // 改为objectKey，这样后端才能正确接收
    partNumber,
  });
}

export function minioMergeVideo(
  filename: string,
  uploadId: string,
  categoryIds: string,
  originalFilename: string,
  extension: string,
  size: number,
  duration: number,
  poster: string
) {
  return client.post("/backend/v1/upload/minio/merge-file", {
    filename,
    upload_id: uploadId,
    category_ids: categoryIds,
    original_filename: originalFilename,
    size,
    duration,
    extension,
    poster,
  });
}
