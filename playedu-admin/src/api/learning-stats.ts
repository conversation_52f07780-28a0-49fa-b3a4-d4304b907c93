import client, { ApiResponse } from "./internal/httpClient";

/**
 * 获取课程维度统计数据
 */
export function getCourseStats(params?: object): Promise<ApiResponse> {
  return client.get("/backend/v1/learning-statistics/course", {
    page: 1,
    size: 1000,
    ...params,
  });
}

/**
 * 获取人员维度统计数据
 */
export function getUserStats(params?: object): Promise<ApiResponse> {
  return client.get("/backend/v1/learning-statistics/user", {
    page: 1,
    size: 1000,
    ...params,
  });
}

/**
 * 导出统计数据
 */
export function exportData(type: string, params?: object): Promise<ApiResponse> {
  return client.get("/backend/v1/learning-statistics/export", {
    type,
    ...params,
  });
}

/**
 * 获取课程详情学员列表
 */
export function getCourseDetail(courseId: number, params?: object): Promise<ApiResponse> {
  return client.get(`/backend/v1/learning-statistics/course/${courseId}/users`, {
    page: 1,
    size: 1000,
    ...params,
  });
}

/**
 * 获取学员详情课程列表
 */
export function getUserDetail(userId: number, params?: object): Promise<ApiResponse> {
  return client.get(`/backend/v1/learning-statistics/user/${userId}/courses`, {
    page: 1,
    size: 1000,
    ...params,
  });
}

// ==================== 以下为兼容旧版本 ====================

// 学习统计概览数据 - 使用仪表板数据
export function getLearningStatisticsOverview(params: object): Promise<ApiResponse> {
  return client.get("/backend/v1/dashboard/index", params);
}

// 课程维度统计列表 - 使用课程用户记录接口
export function getCourseLearningStats(
  page: number,
  size: number,
  params: object
): Promise<ApiResponse> {
  return client.get("/backend/v1/course/index", { page, size, ...params })
    .then((response: ApiResponse) => {
      const courses = (response.data as any).data || [];
      const coursePromises = courses.map((course: any) => {
        return client.get(`/backend/v1/course/${course.id}/user/index`, {
          page: 1,
          size: 1000
        }).then((userResponse: ApiResponse) => {
          const userRecords = (userResponse.data as any).data || [];
          const userCourseRecords = (userResponse.data as any).user_course_records || {};
          const totalUsers = userRecords.length;
          const completedUsers = Object.values(userCourseRecords).filter((record: any) =>
            record && record.is_finished === 1
          ).length;
          const completionRate = totalUsers > 0 ? Math.round((completedUsers / totalUsers) * 100) : 0;
          return {
            ...course,
            total_users: totalUsers,
            completed_users: completedUsers,
            completion_rate: completionRate,
            avg_score: 0,
            passing_rate: 0,
            avg_learning_time: '0分钟'
          };
        });
      });
      return Promise.all(coursePromises).then(coursesWithStats => ({
        ...response.data,
        data: coursesWithStats
      }));
    });
}

// 用户维度统计列表 - 使用用户列表和课程记录
export function getUserLearningStats(
  page: number,
  size: number,
  params: object
): Promise<ApiResponse> {
  return client.get("/backend/v1/user/index", { page, size, ...params })
    .then((response: ApiResponse) => {
      const users = (response.data as any).data || [];
      const userPromises = users.map((user: any) => {
        return client.get(`/backend/v1/user/${user.id}/all-courses`).then((courseResponse: ApiResponse) => {
          const userCourseRecords = (courseResponse.data as any).user_course_records || {};
          const userCourseHourCount = (courseResponse.data as any).user_course_hour_count || {};
          const totalCourses = Object.keys(userCourseRecords).length;
          const completedCourses = Object.values(userCourseRecords).filter((record: any) =>
            record && record.is_finished === 1
          ).length;
          const completionRate = totalCourses > 0 ? Math.round((completedCourses / totalCourses) * 100) : 0;
          return {
            ...user,
            total_courses: totalCourses,
            completed_courses: completedCourses,
            completion_rate: completionRate,
            avg_score: 0,
            total_learning_time: '0分钟',
            avg_learning_time: '0分钟'
          };
        });
      });
      return Promise.all(userPromises).then(usersWithStats => ({
        ...response.data,
        data: usersWithStats
      }));
    });
}

// 导出学习统计数据 - 使用现有数据导出
export function exportLearningStatistics(params: object): Promise<any[]> {
  return client.get("/backend/v1/course/index", { page: 1, size: 10000, ...params })
    .then((response: ApiResponse) => {
      const courses = (response.data as any).data || [];
      const headers = ['课程名称', '课程分类', '学员总数', '完成人数', '完成率', '平均分', '及格率', '平均学习时长'];
      const rows = courses.map((course: any) => [
        course.title,
        course.category_name || '未分类',
        course.total_users || 0,
        course.completed_users || 0,
        `${course.completion_rate || 0}%`,
        course.avg_score || 0,
        `${course.passing_rate || 0}%`,
        course.avg_learning_time || '0分钟'
      ]);
      return [headers, ...rows];
    });
}

// 获取课程学习详情 - 使用课程用户记录接口
export function getCourseLearnStats(courseId: number, params: object): Promise<ApiResponse> {
  return client.get(`/backend/v1/course/${courseId}/user/index`, params);
}

// 获取用户学习详情 - 使用用户学习统计接口
export function getUserLearnStats(userId: number, params: object): Promise<ApiResponse> {
  return client.get(`/backend/v1/user/${userId}/learn-stats`, params);
}