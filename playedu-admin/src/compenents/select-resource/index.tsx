import { useEffect, useState } from "react";
import { Row, Modal,  } from "antd";
import styles from "./index.module.less";
import { UploadVideoSub } from "../../compenents";

interface PropsInterface {
  defaultKeys: any[];
  open: boolean;
  onSelected: (arr: any[], videos: any[]) => void;
  onCancel: () => void;
  title?: string; // 弹窗标题，默认"视频库"
  resourceType?: string; // 资源类型，默认"VIDEO"，可选"VIDEO,PDF,PPT"等
}

type selVideosModel = {
  name: string;
  rid: number;
  type: string;
  duration: number;
};

export const SelectResource = (props: PropsInterface) => {
  const [refresh, setRefresh] = useState(true);
  const [selectKeys, setSelectKeys] = useState<number[]>([]);
  const [selectVideos, setSelectVideos] = useState<selVideosModel[]>([]);

  useEffect(() => {
    setRefresh(!refresh);
  }, [props.open]);

  const modalTitle = props.title || "视频库";
  const resourceType = props.resourceType || "VIDEO";
  const label = resourceType === "VIDEO" ? "视频" : resourceType.includes("VIDEO") ? "视频/课件" : "课件";

  return (
    <>
      {props.open ? (
        <Modal
          title={modalTitle}
          centered
          closable={false}
          onCancel={() => {
            setSelectKeys([]);
            setSelectVideos([]);
            props.onCancel();
          }}
          open={true}
          width={800}
          maskClosable={false}
          onOk={() => {
            props.onSelected(selectKeys, selectVideos);
            setSelectKeys([]);
            setSelectVideos([]);
          }}
        >
          <Row>
            <div className="float-left mt-24">
              <UploadVideoSub
                label={label}
                defaultCheckedList={props.defaultKeys}
                open={refresh}
                resourceType={resourceType}
                onSelected={(arr: any[], videos: any[]) => {
                  setSelectKeys(arr);
                  setSelectVideos(videos);
                }}
              />
            </div>
          </Row>
        </Modal>
      ) : null}
    </>
  );
};
