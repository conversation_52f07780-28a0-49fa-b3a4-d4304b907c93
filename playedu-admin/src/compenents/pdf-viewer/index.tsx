import { useEffect, useRef, useState } from "react";
import * as pdfjsLib from "pdfjs-dist";
import styles from "./index.module.less";
import { ArrowLeftOutlined, ArrowRightOutlined } from "@ant-design/icons";
import { message } from "antd";

// 配置 PDF.js worker - 使用 public 目录下的文件
pdfjsLib.GlobalWorkerOptions.workerSrc = '/pdf.worker.min.mjs';

interface PdfViewerProps {
  url: string;
  onProgressChange?: (current: number, total: number, viewedPages: Set<number>) => void;
}

export const PdfViewer: React.FC<PdfViewerProps> = ({ url, onProgressChange }) => {
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const containerRef = useRef<HTMLDivElement>(null);
  const renderTaskRef = useRef<any>(null); // 保存当前渲染任务
  const renderingRef = useRef(false); // 渲染标志，防止并发
  const onProgressChangeRef = useRef(onProgressChange); // 保存回调引用
  const [pdf, setPdf] = useState<any>(null);
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(0);
  const [loading, setLoading] = useState(true);
  const [viewedPages, setViewedPages] = useState<Set<number>>(new Set([1]));
  const [scale, setScale] = useState(0); // 初始为0，首次渲染时计算

  // 更新回调引用
  useEffect(() => {
    onProgressChangeRef.current = onProgressChange;
  }, [onProgressChange]);

  // 加载 PDF
  useEffect(() => {
    const loadPdf = async () => {
      try {
        setLoading(true);
        setScale(0); // 重置缩放，让新PDF重新计算
        const loadingTask = pdfjsLib.getDocument(url);
        const pdfDoc = await loadingTask.promise;
        setPdf(pdfDoc);
        setTotalPages(pdfDoc.numPages);
        setLoading(false);
      } catch (error) {
        console.error("PDF 加载失败:", error);
        message.error("PDF 加载失败");
        setLoading(false);
      }
    };

    if (url) {
      loadPdf();
    }
  }, [url]);

  // 通知父组件进度变化
  useEffect(() => {
    if (totalPages > 0 && viewedPages.size > 0 && onProgressChangeRef.current) {
      onProgressChangeRef.current(currentPage, totalPages, viewedPages);
    }
  }, [viewedPages, currentPage, totalPages]);

  // 渲染当前页
  useEffect(() => {
    if (!pdf || !canvasRef.current || !containerRef.current) return;

    const renderPage = async () => {
      try {
        // 如果正在渲染，取消之前的任务并等待
        if (renderingRef.current && renderTaskRef.current) {
          try {
            renderTaskRef.current.cancel();
            await new Promise(resolve => setTimeout(resolve, 10)); // 短暂延迟确保取消生效
          } catch (e) {
            // 忽略取消错误
          }
        }

        const canvas = canvasRef.current;
        const container = containerRef.current;
        if (!canvas || !container) return;

        // 清空 Canvas
        const context = canvas.getContext("2d");
        if (!context) return;
        context.clearRect(0, 0, canvas.width, canvas.height);

        // 设置渲染标志
        renderingRef.current = true;

        const page = await pdf.getPage(currentPage);
        
        // 只在第一次渲染时计算缩放比例
        let finalScale = scale;
        if (scale === 0) {
          // 获取页面原始尺寸 (scale=1)
          const originalViewport = page.getViewport({ scale: 1.0 });
          
          // 计算合适的缩放比例，让PDF充满容器
          const containerWidth = container.clientWidth - 40; // 减去较小边距
          const containerHeight = container.clientHeight - 60; // 减去顶部工具栏
          
          // 按宽度和高度分别计算缩放比例，取较小值确保完整显示
          const scaleByWidth = containerWidth / originalViewport.width;
          const scaleByHeight = containerHeight / originalViewport.height;
          // 使用较小值，确保完整显示；最小1.5倍，最大2.5倍
          finalScale = Math.max(1.5, Math.min(scaleByWidth, scaleByHeight, 2.5));
          
          console.log('PDF缩放计算:', {
            containerWidth,
            containerHeight,
            pageWidth: originalViewport.width,
            pageHeight: originalViewport.height,
            scaleByWidth,
            scaleByHeight,
            finalScale
          });
          
          // 保存计算出的缩放比例
          setScale(finalScale);
        }
        
        // 使用固定的缩放比例
        const viewport = page.getViewport({ scale: finalScale });

        canvas.height = viewport.height;
        canvas.width = viewport.width;

        const renderContext = {
          canvasContext: context,
          viewport: viewport,
        };

        // 保存渲染任务引用
        renderTaskRef.current = page.render(renderContext);
        
        await renderTaskRef.current.promise;
        
        // 渲染完成后清空引用和标志
        renderTaskRef.current = null;
        renderingRef.current = false;

        // 标记当前页为已浏览
        setViewedPages((prev) => {
          const newSet = new Set(prev);
          newSet.add(currentPage);
          return newSet;
        });
      } catch (error: any) {
        // 清除渲染标志
        renderingRef.current = false;
        renderTaskRef.current = null;
        
        // 忽略取消错误
        if (error.name !== 'RenderingCancelledException') {
          console.error("页面渲染失败:", error);
        }
      }
    };

    renderPage();
    
    // 组件卸载时取消渲染
    return () => {
      if (renderTaskRef.current) {
        try {
          renderTaskRef.current.cancel();
        } catch (e) {
          // 忽略
        }
      }
      renderingRef.current = false;
    };
  }, [pdf, currentPage, scale]);

  // 上一页
  const handlePrevPage = () => {
    if (currentPage > 1) {
      setCurrentPage(currentPage - 1);
    }
  };

  // 下一页
  const handleNextPage = () => {
    if (currentPage < totalPages) {
      setCurrentPage(currentPage + 1);
    }
  };

  // 点击屏幕左右区域翻页
  const handleCanvasClick = (e: React.MouseEvent<HTMLDivElement>) => {
    if (!containerRef.current) return;
    
    const rect = containerRef.current.getBoundingClientRect();
    const clickX = e.clientX - rect.left;
    const containerWidth = rect.width;

    // 左侧 1/3 区域：上一页
    if (clickX < containerWidth / 3) {
      handlePrevPage();
    }
    // 右侧 1/3 区域：下一页
    else if (clickX > (containerWidth * 2) / 3) {
      handleNextPage();
    }
  };

  if (loading) {
    return (
      <div className={styles["pdf-loading"]}>
        <div className={styles["loading-spinner"]}></div>
        <div>PDF 加载中...</div>
      </div>
    );
  }

  return (
    <div className={styles["pdf-viewer"]} ref={containerRef}>
      <div className={styles["pdf-toolbar"]}>
        <button
          className={styles["nav-btn"]}
          onClick={handlePrevPage}
          disabled={currentPage === 1}
        >
          <ArrowLeftOutlined /> 上一页
        </button>
        <span className={styles["page-info"]}>
          第 {currentPage} / {totalPages} 页
          <span className={styles["viewed-info"]}>
            （已浏览 {viewedPages.size} 页）
          </span>
        </span>
        <button
          className={styles["nav-btn"]}
          onClick={handleNextPage}
          disabled={currentPage === totalPages}
        >
          下一页 <ArrowRightOutlined />
        </button>
      </div>

      <div 
        className={styles["pdf-content"]} 
        onClick={handleCanvasClick}
        title="点击左侧上一页，右侧下一页"
      >
        {/* 左侧提示区域 */}
        {currentPage > 1 && (
          <div className={styles["hint-left"]}>
            <ArrowLeftOutlined />
          </div>
        )}
        
        <canvas ref={canvasRef} className={styles["pdf-canvas"]} />
        
        {/* 右侧提示区域 */}
        {currentPage < totalPages && (
          <div className={styles["hint-right"]}>
            <ArrowRightOutlined />
          </div>
        )}
      </div>
    </div>
  );
};

