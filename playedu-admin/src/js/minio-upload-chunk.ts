import axios, { Axios } from "axios";
import { minioPreSignUrl } from "../api/upload";

export class UploadChunk {
  client: Axios;
  file: File;
  progress: number;
  chunkNumber: number;
  isStop: boolean;
  chunkSize: number;
  chunkIndex: number;
  uploadId: string;
  filename: string;
  // 上传状态[0:等待上传,3:上传中,5:上传失败,7:上传成功]
  uploadStatus: number;
  uploadRemark: string;
  // 存储所有分块的ETag信息
  partETags: Map<number, string>;

  onError?: (err: string) => void | undefined;
  onSuccess?: () => void | undefined;
  onRetry?: () => void | undefined;
  onProgress?: (progress: number) => void;

  constructor(file: File, uploadId: string, filename: string) {
    this.client = axios.create({
      timeout: 60000,
      withCredentials: false,
    });
    this.file = file;
    this.progress = 0;
    this.isStop = false;
    this.chunkIndex = 1;
    this.chunkSize = 5 * 1024 * 1024; //分块大小-5mb
    this.chunkNumber = Math.ceil(file.size / this.chunkSize);

    this.uploadId = uploadId;
    this.filename = filename;

    this.uploadStatus = 0;
    this.uploadRemark = "";
    this.partETags = new Map();
  }

  on(event: string, handle: any) {
    if (event === "error") {
      this.onError = handle;
    } else if (event === "success") {
      this.onSuccess = handle;
    } else if (event === "progress") {
      this.onProgress = handle;
    } else if (event === "retry") {
      this.onRetry = handle;
    }
  }

  start() {
    if (this.isStop) {
      return;
    }

    // 检测是否上传完成
    if (this.chunkIndex > this.chunkNumber) {
      this.uploadCompleted();
      return;
    }

    // 进度更新
    this.uploadProgressUpdated();

    let start = (this.chunkIndex - 1) * this.chunkSize;
    const chunkData = this.file.slice(start, start + this.chunkSize);
    const boolname = this.file.name + "-" + this.chunkIndex;
    const tmpFile = new File([chunkData], boolname);

    // 首先获取上传腾讯云COS的预签名URL
    minioPreSignUrl(this.uploadId, this.filename, this.chunkIndex)
      .then((res: any) => {
        const cosUrl = res.data.presignedUrl;
        
        // 使用后端代理接口上传分块 - 通过代理绕过CORS问题
        const proxyUrl = '/backend/v1/upload/cos-proxy/upload';
        
        // 构建代理请求参数
        const params = new URLSearchParams({
          filename: this.filename,
          'content-type': 'application/octet-stream',
          'upload_id': this.uploadId,
          'part_number': this.chunkIndex.toString()
        });
        
        return this.client.put(proxyUrl + '?' + params.toString(), tmpFile, {
          headers: {
            "Content-Type": "application/octet-stream",
            // 将原始COS的预签名URL作为请求头传递给代理
            "X-Original-COS-URL": cosUrl,
          },
        });
      })
      .then((response: any) => {
        // 从代理响应中获取ETag
        if (response.data && response.data.data && response.data.data.etag) {
          const etag = response.data.data.etag;
          const partNumber = this.chunkIndex;
          
          console.log(`分块 ${partNumber} 上传成功，ETag: ${etag}`);
          
          // 保存ETag信息用于后续合并
          this.partETags.set(partNumber, etag);
          
          this.chunkIndex += 1;
          this.start();
        } else {
          throw new Error("代理返回的ETag不正确");
        }
      })
      .catch((e: any) => {
        this.uploadedFail(e);
      });
  }

  isOver() {
    return this.uploadStatus === 5 || this.uploadStatus === 7;
  }

  cancel() {
    this.isStop = true;
    this.onError && this.onError("已取消");
  }

  retry() {
    this.isStop = false;
    this.uploadStatus = 0;
    this.start();
    this.onRetry && this.onRetry();
  }

  uploadProgressUpdated() {
    if (this.uploadStatus === 0) {
      this.uploadStatus = 3;
    }
    this.onProgress &&
      this.onProgress(
        parseInt((this.chunkIndex / this.chunkNumber) * 100 + "")
      );
  }

  uploadCompleted() {
    this.uploadStatus = 7;
    this.onSuccess && this.onSuccess();
  }

  uploadedFail(e: any) {
    console.log("上传失败,错误信息:", e);
    this.uploadStatus = 5;
    
    // 更详细的错误信息
    let errorMsg = "上传失败";
    if (e.response) {
      // 服务器返回了错误响应
      errorMsg = `上传失败: ${e.response.status} ${e.response.statusText}`;
      if (e.response.data) {
        if (e.response.data.message) {
          errorMsg += ` - ${e.response.data.message}`;
        } else if (e.response.data.error) {
          errorMsg += ` - ${e.response.data.error}`;
        }
      }
    } else if (e.request) {
      // 请求已发出但没有收到响应
      errorMsg = "上传失败: 网络连接错误";
    } else if (e.message) {
      // 发生了设置请求时的错误
      errorMsg = `上传失败: ${e.message}`;
    }
    
    this.uploadRemark = errorMsg;
    this.onError && this.onError(errorMsg);
  }

  getUploadStatus(): number {
    return this.uploadStatus;
  }

  getUploadProgress(): number {
    if (this.chunkNumber === 0) {
      return 0;
    }
    return (this.chunkIndex / this.chunkNumber) * 100;
  }

  getUploadRemark(): string {
    return this.uploadRemark;
  }

  // 获取所有分块的ETag信息，用于合并
  getPartETags(): Array<{partNumber: number, etag: string}> {
    const parts: Array<{partNumber: number, etag: string}> = [];
    this.partETags.forEach((etag, partNumber) => {
      parts.push({
        partNumber,
        etag
      });
    });
    // 按分块号排序
    return parts.sort((a, b) => a.partNumber - b.partNumber);
  }
}
