import React, { useState } from "react";
import {
  Form,
  Input,
  InputNumber,
  Select,
  Button,
  Spin,
  message,
} from "antd";
import * as examApi from "../../../api/exam";

interface CreateExamProps {
  onSuccess?: () => void;
  onCancel?: () => void;
}

export const CreateExam: React.FC<CreateExamProps> = ({
  onSuccess,
  onCancel,
}) => {
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);

  const handleSubmit = async (values: any) => {
    setLoading(true);
    try {
      const res = await examApi.examCreate({
        title: values.name,
        type: values.type,
        duration: values.duration,
        pass_score: values.pass_score,
        description: values.description || "",
      });
      if (res && res.code === 0) {
        message.success("创建成功");
        form.resetFields();
        onSuccess?.();
      } else {
        message.error((res as any)?.msg || "创建失败");
      }
    } catch (error: any) {
      message.error(error.message || "创建失败");
    } finally {
      setLoading(false);
    }
  };

  return (
    <Spin spinning={loading}>
      <Form
        form={form}
        layout="vertical"
        onFinish={handleSubmit}
        autoComplete="off"
      >
        <Form.Item
          label="考试名称"
          name="name"
          rules={[
            { required: true, message: "请输入考试名称" },
            { max: 255, message: "考试名称最多255个字符" },
          ]}
        >
          <Input placeholder="请输入考试名称" />
        </Form.Item>

        <Form.Item
          label="考试类型"
          name="type"
          rules={[{ required: true, message: "请选择考试类型" }]}
        >
          <Select placeholder="请选择考试类型">
            <Select.Option value="1">单选题</Select.Option>
            <Select.Option value="2">多选题</Select.Option>
            <Select.Option value="3">混合题型</Select.Option>
          </Select>
        </Form.Item>

        <Form.Item
          label="考试时长（分钟）"
          name="duration"
          rules={[
            { required: true, message: "请输入考试时长" },
            { type: "number", min: 1, max: 480, message: "时长需在1-480分钟之间" },
          ]}
        >
          <InputNumber placeholder="请输入考试时长" style={{ width: "100%" }} />
        </Form.Item>

        <Form.Item
          label="及格分数"
          name="pass_score"
          rules={[
            { required: true, message: "请输入及格分数" },
            { type: "number", min: 0, max: 100, message: "分数需在0-100之间" },
          ]}
        >
          <InputNumber placeholder="请输入及格分数" style={{ width: "100%" }} />
        </Form.Item>

        <Form.Item
          label="考试描述"
          name="description"
        >
          <Input.TextArea
            placeholder="请输入考试描述"
            rows={4}
          />
        </Form.Item>

        <Form.Item>
          <div style={{ display: "flex", gap: "10px", justifyContent: "flex-end" }}>
            <Button onClick={onCancel}>取消</Button>
            <Button type="primary" htmlType="submit" loading={loading}>
              保存
            </Button>
          </div>
        </Form.Item>
      </Form>
    </Spin>
  );
};
