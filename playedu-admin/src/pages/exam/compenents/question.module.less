.question-page {
  .question-header {
    margin-bottom: 24px;
    display: flex;
    gap: 12px;
    align-items: center;

    button {
      height: 36px;
    }
  }

  .question-search {
    margin-bottom: 24px;
    display: flex;
    align-items: center;
    gap: 12px;
    flex-wrap: wrap;
    padding: 16px;
    background: #f5f7fa;
    border-radius: 4px;

    input {
      flex: 0 0 auto;
    }

    :global {
      .ant-select {
        flex: 0 0 auto;
      }
    }
  }

  .question-table {
    background: #fff;
    border-radius: 4px;

    :global {
      .ant-table {
        font-size: 13px;

        .ant-table-thead {
          th {
            background: #fafafa;
            font-weight: 600;
          }
        }

        .ant-table-tbody {
          tr {
            &:hover {
              background: #f5f5f5;
            }
          }
        }
      }
    }
  }
}
