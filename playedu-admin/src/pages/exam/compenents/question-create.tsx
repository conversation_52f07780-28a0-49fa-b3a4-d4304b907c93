import React, { useState } from "react";
import {
  Form,
  Input,
  InputNumber,
  Select,
  Button,
  Spin,
  message,
  Space,
  Divider,
  Row,
  Col,
  Checkbox,
} from "antd";
import { MinusCircleOutlined, PlusOutlined } from "@ant-design/icons";
import * as examApi from "../../../api/exam";

interface CreateQuestionProps {
  examId: number;
  onSuccess?: () => void;
  onCancel?: () => void;
}

export const CreateQuestion: React.FC<CreateQuestionProps> = ({
  examId,
  onSuccess,
  onCancel,
}) => {
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);
  const [questionType, setQuestionType] = useState("single_choice");

  const handleSubmit = async (values: any) => {
    setLoading(true);
    try {
      // 将 checkbox 的 boolean 值转换为 integer
      const options = (values.options || []).map((opt: any) => ({
        content: opt.content,
        is_correct: opt.is_correct ? 1 : 0,
      }));

      // 单选题只能有一个正确答案的校验
      if (values.type === "single_choice") {
        const correctCount = options.filter((opt: any) => opt.is_correct === 1).length;
        if (correctCount !== 1) {
          message.error("单选题必须有且仅有一个正确答案");
          setLoading(false);
          return;
        }
      }

      // 多选题至少有一个正确答案的校验
      if (values.type === "multiple_choice") {
        const correctCount = options.filter((opt: any) => opt.is_correct === 1).length;
        if (correctCount === 0) {
          message.error("多选题至少要有一个正确答案");
          setLoading(false);
          return;
        }
      }

      const res = await examApi.questionCreate(examId, {
        title: values.title,
        type: values.type,
        difficulty: values.difficulty,
        score: values.score,
        analysis: values.analysis || "",
        answer: values.answer || "",
        options: options,
      });
      if (res && res.code === 0) {
        message.success("创建成功");
        form.resetFields();
        onSuccess?.();
      } else {
        message.error((res as any)?.msg || "创建失败");
      }
    } catch (error: any) {
      message.error(error.message || "创建失败");
    } finally {
      setLoading(false);
    }
  };

  return (
    <Spin spinning={loading}>
      <Form
        form={form}
        layout="vertical"
        onFinish={handleSubmit}
        autoComplete="off"
      >
        <Row gutter={32} style={{ maxWidth: "1200px" }}>
          {/* 左侧：基本信息 */}
          <Col span={12}>
            <div style={{ paddingRight: "12px" }}>
              <h3 style={{ marginBottom: "16px" }}>基本信息</h3>

              <Form.Item
                label="题目"
                name="title"
                rules={[
                  { required: true, message: "请输入题目" },
                  { max: 500, message: "题目最多500个字符" },
                ]}
              >
                <Input.TextArea placeholder="请输入题目内容" rows={4} />
              </Form.Item>

              <Form.Item
                label="题型"
                name="type"
                initialValue="single_choice"
                rules={[{ required: true, message: "请选择题型" }]}
              >
                <Select onChange={setQuestionType}>
                  <Select.Option value="single_choice">单选题</Select.Option>
                  <Select.Option value="multiple_choice">多选题</Select.Option>
                </Select>
              </Form.Item>

              <Form.Item
                label="难度"
                name="difficulty"
                initialValue="medium"
                rules={[{ required: true, message: "请选择难度" }]}
              >
                <Select>
                  <Select.Option value="easy">简单</Select.Option>
                  <Select.Option value="medium">中等</Select.Option>
                  <Select.Option value="hard">困难</Select.Option>
                </Select>
              </Form.Item>

              <Form.Item
                label="分数"
                name="score"
                rules={[
                  { required: true, message: "请输入分数" },
                  { type: "number", min: 1, max: 100, message: "分数需在1-100之间" },
                ]}
              >
                <InputNumber placeholder="请输入分数" style={{ width: "100%" }} />
              </Form.Item>

              <Form.Item
                label="解析"
                name="analysis"
              >
                <Input.TextArea placeholder="请输入题目解析（可选）" rows={3} />
              </Form.Item>
            </div>
          </Col>

          {/* 右侧：选项管理 */}
          <Col span={12}>
            {(questionType === "single_choice" || questionType === "multiple_choice") && (
              <div style={{ paddingLeft: "12px", borderLeft: "1px solid #f0f0f0", paddingTop: "0" }}>
                <h3 style={{ marginBottom: "16px" }}>选项管理</h3>
                <Form.List name="options">
                  {(fields, { add, remove }) => (
                    <>
                      {fields.map((field, index) => {
                        const { key: fieldKey, ...restField } = field;
                        return (
                          <div key={fieldKey} style={{ marginBottom: "16px", padding: "12px", background: "#fafafa", borderRadius: "4px" }}>
                            <div style={{ display: "flex", justifyContent: "space-between", alignItems: "center", marginBottom: "8px" }}>
                              <span style={{ fontWeight: "500" }}>选项 {String.fromCharCode(65 + index)}</span>
                              {fields.length > 2 && (
                                <Button
                                  danger
                                  type="text"
                                  size="small"
                                  icon={<MinusCircleOutlined />}
                                  onClick={() => remove(field.name)}
                                >
                                  删除
                                </Button>
                              )}
                            </div>

                            <div style={{ display: "flex", gap: "8px", alignItems: "flex-start" }}>
                              <Form.Item
                                {...restField}
                                name={[field.name, "content"]}
                                rules={[{ required: true, message: "请输入选项内容" }]}
                                style={{ marginBottom: "0", flex: 1 }}
                              >
                                <Input placeholder={`请输入选项${String.fromCharCode(65 + index)}的内容`} />
                              </Form.Item>

                              <Form.Item
                                {...restField}
                                name={[field.name, "is_correct"]}
                                valuePropName="checked"
                                style={{ marginBottom: "0", marginTop: "5px", whiteSpace: "nowrap" }}
                                key={`chk-${fieldKey}`}
                              >
                                <Checkbox>正确答案</Checkbox>
                              </Form.Item>
                            </div>
                          </div>
                        );
                      })}

                      {fields.length < 5 && (
                        <Button
                          type="dashed"
                          block
                          icon={<PlusOutlined />}
                          onClick={() =>
                            add({ content: "", is_correct: 0 })
                          }
                          style={{ marginBottom: "16px" }}
                        >
                          添加选项
                        </Button>
                      )}

                      <div style={{ fontSize: "12px", color: "#999" }}>
                        已添加 {fields.length} 个选项（最多5个）
                      </div>
                    </>
                  )}
                </Form.List>
              </div>
            )}
          </Col>
        </Row>

        {/* 底部按钮 */}
        <Divider />
        <Form.Item>
          <div style={{ display: "flex", gap: "10px", justifyContent: "flex-end" }}>
            <Button onClick={onCancel}>取消</Button>
            <Button type="primary" htmlType="submit" loading={loading}>
              保存
            </Button>
          </div>
        </Form.Item>
      </Form>
    </Spin>
  );
};
