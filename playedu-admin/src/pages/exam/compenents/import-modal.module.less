.import-container {
  .upload-section {
    h3 {
      font-size: 14px;
      font-weight: 600;
      margin-top: 20px;
      margin-bottom: 10px;

      &:first-child {
        margin-top: 0;
      }
    }

    .help-text {
      color: #666;
      font-size: 12px;
      margin-bottom: 10px;
    }

    .file-info {
      margin-top: 10px;
      padding: 8px 12px;
      background: #f5f5f5;
      border-radius: 4px;
      font-size: 12px;
      color: #333;
    }

    .actions {
      display: flex;
      gap: 10px;
      margin-top: 20px;
      justify-content: flex-end;
    }
  }
}

.result-section {
  .statistics {
    display: flex;
    gap: 20px;
    margin-bottom: 20px;

    .stat-item {
      flex: 1;
      padding: 15px;
      background: #fafafa;
      border-radius: 4px;
      text-align: center;

      .stat-label {
        font-size: 12px;
        color: #666;
        margin-bottom: 5px;
      }

      .stat-value {
        font-size: 24px;
        font-weight: bold;
        color: #333;
      }

      &.success {
        background: #f6ffed;
        .stat-value {
          color: #52c41a;
        }
      }

      &.error {
        background: #fff1f0;
        .stat-value {
          color: #ff4d4f;
        }
      }
    }
  }

  .error-section {
    margin-bottom: 20px;

    h4 {
      margin-bottom: 10px;
      font-size: 14px;
    }
  }

  .actions {
    display: flex;
    justify-content: flex-end;
    gap: 10px;
    margin-top: 20px;
  }
}
