import React, { useEffect, useState } from "react";
import {
  Button,
  Modal,
  Table,
  Input,
  Select,
  message,
  Space,
  Popconfirm,
  Tag,
} from "antd";
import {
  PlusOutlined,
  EditOutlined,
  DeleteOutlined,
  UploadOutlined,
} from "@ant-design/icons";
import type { ColumnsType } from "antd/es/table";
import styles from "../index.module.less";
import { ImportModal } from "./import-modal";
import { CreateQuestion } from "./question-create";
import { UpdateQuestion } from "./question-update";
import * as examApi from "../../../api/exam";

interface QuestionData {
  id: React.Key;
  title: string;
  type: string;
  difficulty: string;
  score: number;
  created_by: string;
  created_at: string;
}

interface LocalSearchParamsInterface {
  page?: number;
  size?: number;
  title?: string;
  type?: string;
}

const typeMap: { [key: string]: string } = {
  single_choice: "单选题",
  multiple_choice: "多选题",
  fill_blank: "填空题",
  essay: "论述题",
};

const difficultyMap: { [key: string]: string } = {
  easy: "简单",
  medium: "中等",
  hard: "困难",
};

const difficultyColors: { [key: string]: string } = {
  easy: "green",
  medium: "orange",
  hard: "red",
};

export const QuestionPage: React.FC<{ examId: number }> = ({ examId }) => {
  const [searchParams, setSearchParams] = useState({
    page: 1,
    size: 10,
    title: "",
    type: "",
  });

  const [list, setList] = useState<QuestionData[]>([]);
  const [total, setTotal] = useState(0);
  const [loading, setLoading] = useState(false);
  const [createVisible, setCreateVisible] = useState(false);
  const [editVisible, setEditVisible] = useState(false);
  const [importVisible, setImportVisible] = useState(false);
  const [selectedQuestion, setSelectedQuestion] = useState<QuestionData | null>(
    null
  );
  const [refresh, setRefresh] = useState(false);

  useEffect(() => {
    getList();
  }, [searchParams, refresh]);

  const getList = () => {
    setLoading(true);
    examApi
      .questionList(
        examId,
        searchParams.page,
        searchParams.size,
        searchParams.title,
        searchParams.type
      )
      .then((res: any) => {
        if (res && res.code === 0) {
          const data = res.data;
          const questionList = (data.data || []).map((item: any) => ({
            id: item.id,
            title: item.title,
            type: item.type,
            difficulty: item.difficulty,
            score: item.score,
            created_by: "管理员",
            created_at: item.created_at,
          }));
          setList(questionList);
          setTotal(data.total || 0);
        } else {
          message.error(res?.msg || "获取试题列表失败");
        }
      })
      .catch((err: any) => {
        message.error("获取试题列表出错");
        console.error(err);
      })
      .finally(() => {
        setLoading(false);
      });
  };

  const handleSearch = () => {
    setSearchParams({ ...searchParams, page: 1 });
  };

  const handleReset = () => {
    setSearchParams({
      page: 1,
      size: 10,
      title: "",
      type: "",
    });
  };

  const handleDelete = (record: QuestionData) => {
    examApi
      .questionDelete(record.id as number)
      .then((res: any) => {
        if (res && res.code === 0) {
          message.success("删除成功");
          setRefresh(!refresh);
        } else {
          message.error(res?.msg || "删除失败");
        }
      })
      .catch((err: any) => {
        message.error("删除出错");
        console.error(err);
      });
  };

  const handleEdit = (record: QuestionData) => {
    setSelectedQuestion(record);
    setEditVisible(true);
  };

  const columns: ColumnsType<QuestionData> = [
    {
      title: "题目",
      dataIndex: "title",
      key: "title",
      width: 300,
      render: (text: string) => <span>{text}</span>,
    },
    {
      title: "题型",
      dataIndex: "type",
      key: "type",
      width: 100,
      render: (type: string) => (
        <Tag>{typeMap[type] || type}</Tag>
      ),
    },
    {
      title: "难度",
      dataIndex: "difficulty",
      key: "difficulty",
      width: 100,
      render: (difficulty: string) => (
        <Tag color={difficultyColors[difficulty]}>
          {difficultyMap[difficulty] || difficulty}
        </Tag>
      ),
    },
    {
      title: "分数",
      dataIndex: "score",
      key: "score",
      width: 80,
      render: (score: number) => <span>{score}分</span>,
    },
    {
      title: "创建人",
      dataIndex: "created_by",
      key: "created_by",
      width: 120,
    },
    {
      title: "创建时间",
      dataIndex: "created_at",
      key: "created_at",
      width: 180,
    },
    {
      title: "操作",
      key: "action",
      width: 150,
      fixed: "right" as const,
      render: (_, record: QuestionData) => (
        <Space size="small">
          <Button
            type="link"
            size="small"
            icon={<EditOutlined />}
            onClick={() => handleEdit(record)}
          >
            编辑
          </Button>
          <Popconfirm
            title="删除确认"
            description="确定要删除这道题目吗？"
            onConfirm={() => handleDelete(record)}
            okText="确认"
            cancelText="取消"
          >
            <Button type="link" size="small" danger icon={<DeleteOutlined />}>
              删除
            </Button>
          </Popconfirm>
        </Space>
      ),
    },
  ];

  return (
    <div className={styles["question-page"]}>
      <div className={styles["question-header"]}>
        <Button
          type="primary"
          icon={<PlusOutlined />}
          onClick={() => setCreateVisible(true)}
        >
          添加试题
        </Button>
        <Button
          icon={<UploadOutlined />}
          onClick={() => setImportVisible(true)}
        >
          批量导入
        </Button>
      </div>

      <div className={styles["question-search"]}>
        <Input
          placeholder="请输入题目关键词"
          style={{ width: 250, marginRight: 10 }}
          value={searchParams.title}
          onChange={(e) =>
            setSearchParams({ ...searchParams, title: e.target.value })
          }
          onPressEnter={handleSearch}
        />
        <Select
          placeholder="请选择题型"
          style={{ width: 150, marginRight: 10 }}
          value={searchParams.type}
          onChange={(value) =>
            setSearchParams({ ...searchParams, type: value })
          }
          allowClear
        >
          <Select.Option value="">全部</Select.Option>
          <Select.Option value="single_choice">单选题</Select.Option>
          <Select.Option value="multiple_choice">多选题</Select.Option>
          <Select.Option value="fill_blank">填空题</Select.Option>
          <Select.Option value="essay">论述题</Select.Option>
        </Select>
        <Space>
          <Button type="primary" onClick={handleSearch}>
            查询
          </Button>
          <Button onClick={handleReset}>
            重置
          </Button>
        </Space>
      </div>

      <div className={styles["question-table"]}>
        <Table
          columns={columns}
          dataSource={list}
          loading={loading}
          pagination={{
            current: searchParams.page,
            pageSize: searchParams.size,
            total: total,
            showSizeChanger: true,
            showTotal: (total) => `共 ${total} 条`,
            onChange: (page, pageSize) => {
              setSearchParams({
                ...searchParams,
                page,
                size: pageSize,
              });
            },
          }}
          rowKey={(record) => record.id}
          scroll={{ x: 1400 }}
        />
      </div>

      {/* 添加试题Modal */}
      <Modal
        title="添加试题"
        open={createVisible}
        onCancel={() => setCreateVisible(false)}
        footer={null}
        width={1000}
        destroyOnClose
      >
        <CreateQuestion
          examId={examId}
          onSuccess={() => {
            setCreateVisible(false);
            setRefresh(!refresh);
          }}
          onCancel={() => setCreateVisible(false)}
        />
      </Modal>

      {/* 编辑试题Modal */}
      <Modal
        title="编辑试题"
        open={editVisible}
        onCancel={() => setEditVisible(false)}
        footer={null}
        width={1000}
        destroyOnClose
      >
        {selectedQuestion && (
          <UpdateQuestion
            questionId={selectedQuestion.id as number}
            examId={examId}
            onSuccess={() => {
              setEditVisible(false);
              setRefresh(!refresh);
            }}
            onCancel={() => setEditVisible(false)}
          />
        )}
      </Modal>

      {/* 批量导入Modal */}
      <ImportModal
        visible={importVisible}
        examId={examId}
        onClose={() => setImportVisible(false)}
        onSuccess={() => {
          setRefresh(!refresh);
        }}
      />
    </div>
  );
};
