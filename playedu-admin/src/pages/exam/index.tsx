import { useEffect, useState } from "react";
import {
  Button,
  Modal,
  Table,
  Input,
  message,
  Space,
  Popconfirm,
} from "antd";
import {
  PlusOutlined,
  EditOutlined,
  DeleteOutlined,
  ExclamationCircleFilled,
  UploadOutlined,
  EyeOutlined,
} from "@ant-design/icons";
import type { ColumnsType } from "antd/es/table";
import { dateFormat } from "../../utils/index";
import { useSearchParams, useNavigate } from "react-router-dom";
import styles from "./index.module.less";
import { ImportModal } from "./compenents/import-modal";
import { CreateExam } from "./compenents/create";
import { UpdateExam } from "./compenents/update";
import { QuestionPage } from "./compenents/question";
import * as examApi from "../../api/exam";

interface ExamData {
  id: React.Key;
  name: string;
  type: string;
  duration: number;
  pass_score: number;
  created_at: string;
  created_by: string;
}

interface LocalSearchParamsInterface {
  page?: number;
  size?: number;
  name?: string;
}

const ExamPage = () => {
  const [searchParams, setSearchParams] = useSearchParams({
    page: "1",
    size: "10",
    name: "",
  });

  const page = parseInt(searchParams.get("page") || "1");
  const size = parseInt(searchParams.get("size") || "10");
  const name = searchParams.get("name");

  const navigate = useNavigate();

  const [list, setList] = useState<ExamData[]>([]);
  const [total, setTotal] = useState(0);
  const [loading, setLoading] = useState(false);
  const [nameKeywords, setNameKeywords] = useState(name || "");
  const [createVisible, setCreateVisible] = useState(false);
  const [editVisible, setEditVisible] = useState(false);
  const [selectedExam, setSelectedExam] = useState<ExamData | null>(null);
  const [importVisible, setImportVisible] = useState(false);
  const [selectedExamId, setSelectedExamId] = useState<number | null>(null);
  const [questionVisible, setQuestionVisible] = useState(false);

  // 考试类型映射
  const typeMap: Record<string, string> = {
    "1": "单选题",
    "2": "多选题",
    "3": "混合题型",
  };

  const getExamTypeName = (type: string) => {
    return typeMap[type] || type;
  };

  // 获取考试列表 (模拟数据)
  const getList = () => {
    setLoading(true);
    examApi
      .examList(page, size, nameKeywords)
      .then((res: any) => {
        if (res && res.code === 0) {
          const data = res.data;
          const examList = (data.data || []).map((item: any) => ({
            id: item.id,
            name: item.title,
            type: item.type,
            duration: item.duration,
            pass_score: item.pass_score,
            created_at: dateFormat(item.created_at),
            created_by: "管理员", // TODO: 从API获取创建者信息
          }));
          setList(examList);
          setTotal(data.total || 0);
        } else {
          message.error(res?.msg || "获取考试列表失败");
        }
      })
      .catch((err: any) => {
        message.error("获取考试列表出错");
        console.error(err);
      })
      .finally(() => {
        setLoading(false);
      });
  };

  useEffect(() => {
    getList();
  }, [page, size, nameKeywords]);

  const resetLocalSearchParams = (obj: LocalSearchParamsInterface) => {
    let params = new URLSearchParams();
    params.set("page", obj.page ? obj.page + "" : "1");
    params.set("size", obj.size ? obj.size + "" : "10");
    if (obj.name) {
      params.set("name", obj.name);
    }
    setSearchParams(params);
  };

  const handleSearch = () => {
    resetLocalSearchParams({
      page: 1,
      size: size,
      name: nameKeywords,
    });
  };

  const handleReset = () => {
    setNameKeywords("");
    resetLocalSearchParams({
      page: 1,
      size: 10,
    });
  };

  const handleDelete = (record: ExamData) => {
    examApi
      .examDelete(record.id as number)
      .then((res: any) => {
        if (res && res.code === 0) {
          message.success("删除成功");
          getList();
        } else {
          message.error(res?.msg || "删除失败");
        }
      })
      .catch((err: any) => {
        message.error("删除出错");
        console.error(err);
      });
  };

  const handleEdit = (record: ExamData) => {
    setSelectedExam(record);
    setEditVisible(true);
  };

  const columns: ColumnsType<ExamData> = [
    {
      title: "考试名称",
      dataIndex: "name",
      key: "name",
      width: 200,
      render: (text: string) => <span>{text}</span>,
    },
    {
      title: "考试类型",
      dataIndex: "type",
      key: "type",
      width: 120,
      render: (type: string) => <span>{getExamTypeName(type)}</span>,
    },
    {
      title: "考试时长（分钟）",
      dataIndex: "duration",
      key: "duration",
      width: 150,
      render: (duration: number) => <span>{duration}分钟</span>,
    },
    {
      title: "及格分数",
      dataIndex: "pass_score",
      key: "pass_score",
      width: 120,
      render: (score: number) => <span>{score}分</span>,
    },
    {
      title: "创建人",
      dataIndex: "created_by",
      key: "created_by",
      width: 120,
    },
    {
      title: "创建时间",
      dataIndex: "created_at",
      key: "created_at",
      width: 180,
      render: (created_at: string) => <span>{created_at}</span>,
    },
    {
      title: "操作",
      key: "action",
      width: 200,
      fixed: "right" as const,
      render: (_, record: ExamData) => (
        <Space size="small">
          <Button
            type="link"
            size="small"
            icon={<EditOutlined />}
            onClick={() => handleEdit(record)}
          >
            编辑
          </Button>
          <Button
            type="link"
            size="small"
            icon={<UploadOutlined />}
            onClick={() => {
              setSelectedExamId(record.id as number);
              setImportVisible(true);
            }}
          >
            导入试题
          </Button>
          <Button
            type="link"
            size="small"
            icon={<EyeOutlined />}
            onClick={() => {
              setSelectedExam(record);
              setSelectedExamId(record.id as number);
              setQuestionVisible(true);
            }}
          >
            查看试题
          </Button>
          <Popconfirm
            title="删除确认"
            description="确定要删除这个考试吗？"
            onConfirm={() => handleDelete(record)}
            okText="确认"
            cancelText="取消"
          >
            <Button type="link" size="small" danger icon={<DeleteOutlined />}>
              删除
            </Button>
          </Popconfirm>
        </Space>
      ),
    },
  ];

  return (
    <div className={styles["exam-page"]}>
      <div className={styles["exam-header"]}>
        <Button
          type="primary"
          icon={<PlusOutlined />}
          onClick={() => setCreateVisible(true)}
        >
          新建考试
        </Button>
      </div>

      <div className={styles["exam-search"]}>
        <Input
          placeholder="请输入考试名称"
          style={{ width: 200, marginRight: 10 }}
          value={nameKeywords}
          onChange={(e) => setNameKeywords(e.target.value)}
          onPressEnter={handleSearch}
        />
        <Button type="primary" onClick={handleSearch}>
          查询
        </Button>
        <Button style={{ marginLeft: 10 }} onClick={handleReset}>
          重置
        </Button>
      </div>

      <div className={styles["exam-table"]}>
        <Table
          columns={columns}
          dataSource={list}
          loading={loading}
          pagination={{
            current: page,
            pageSize: size,
            total: total,
            showSizeChanger: true,
            showTotal: (total) => `共 ${total} 条`,
            onChange: (page, pageSize) => {
              resetLocalSearchParams({
                page,
                size: pageSize,
                name: nameKeywords,
              });
            },
          }}
          rowKey={(record) => record.id}
          scroll={{ x: 1200 }}
        />
      </div>

      {/* 创建考试Modal */}
      <Modal
        title="新建考试"
        open={createVisible}
        onCancel={() => setCreateVisible(false)}
        footer={null}
      >
        <CreateExam
          onSuccess={() => {
            setCreateVisible(false);
            getList();
          }}
          onCancel={() => setCreateVisible(false)}
        />
      </Modal>

      {/* 编辑考试Modal */}
      <Modal
        title="编辑考试"
        open={editVisible}
        onCancel={() => setEditVisible(false)}
        footer={null}
      >
        {selectedExam && (
          <UpdateExam
            examId={selectedExam.id as number}
            onSuccess={() => {
              setEditVisible(false);
              getList();
            }}
            onCancel={() => setEditVisible(false)}
          />
        )}
      </Modal>

      {/* 导入试题Modal */}
      <ImportModal
        visible={importVisible}
        examId={selectedExamId || 0}
        onClose={() => setImportVisible(false)}
        onSuccess={() => {
          getList();
        }}
      />

      {/* 试题管理Modal */}
      <Modal
        title={`${selectedExam?.name || ""} - 试题管理`}
        open={questionVisible}
        onCancel={() => setQuestionVisible(false)}
        footer={null}
        width={1200}
        destroyOnClose
      >
        {selectedExamId && (
          <QuestionPage examId={selectedExamId} />
        )}
      </Modal>
    </div>
  );
};

export default ExamPage;
