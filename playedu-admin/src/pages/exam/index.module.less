.exam-page {
  padding: 20px;

  .exam-header {
    margin-bottom: 20px;
    display: flex;
    gap: 10px;
  }

  .exam-search {
    margin-bottom: 20px;
    display: flex;
    align-items: center;
    gap: 10px;
    flex-wrap: wrap;
  }

  .exam-table {
    background: #fff;
    border-radius: 4px;
  }
}

.question-page {
  .question-header {
    margin-bottom: 20px;
    display: flex;
    gap: 10px;
  }

  .question-search {
    margin-bottom: 20px;
    display: flex;
    align-items: center;
    gap: 10px;
    flex-wrap: wrap;
  }

  .question-table {
    background: #fff;
    border-radius: 4px;
  }

  .exam-detail-page {
    padding: 20px;

    .exam-detail-header {
      margin-bottom: 20px;
      display: flex;
      align-items: center;
      gap: 10px;

      h1 {
        margin: 0;
        font-size: 24px;
        font-weight: 600;
      }
    }
  }
}
