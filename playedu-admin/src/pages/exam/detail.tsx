import React, { useEffect, useState } from "react";
import { useParams, useNavigate } from "react-router-dom";
import { <PERSON><PERSON>, Card, Spin, Tabs, message } from "antd";
import { ArrowLeftOutlined } from "@ant-design/icons";
import styles from "./index.module.less";
import { QuestionPage } from "./compenents/question";

interface ExamDetail {
  id: number;
  name: string;
  type: string;
  duration: number;
  pass_score: number;
  description?: string;
}

export const ExamDetailPage = () => {
  const { examId } = useParams<{ examId: string }>();
  const navigate = useNavigate();
  const [exam, setExam] = useState<ExamDetail | null>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    loadExamData();
  }, [examId]);

  const loadExamData = async () => {
    setLoading(true);
    try {
      // TODO: 调用获取考试详情API
      // const res = await exam.detail(examId);
      // setExam(res.data);

      // 模拟数据
      const mockData: ExamDetail = {
        id: parseInt(examId || "0"),
        name: "JavaScript基础考试",
        type: "1",
        duration: 60,
        pass_score: 60,
        description: "这是一个JavaScript基础知识考试",
      };
      setExam(mockData);
    } catch (error: any) {
      message.error(error.message || "加载数据失败");
    } finally {
      setLoading(false);
    }
  };

  if (loading) {
    return <Spin />;
  }

  return (
    <div className={styles["exam-detail-page"]}>
      <div className={styles["exam-detail-header"]}>
        <Button
          type="text"
          icon={<ArrowLeftOutlined />}
          onClick={() => navigate("/exam")}
        >
          返回
        </Button>
        <h1>{exam?.name}</h1>
      </div>

      <Card
        title="考试信息"
        style={{ marginBottom: "20px" }}
      >
        <div style={{ display: "grid", gridTemplateColumns: "repeat(2, 1fr)", gap: "20px" }}>
          <div>
            <label style={{ fontWeight: "bold" }}>考试类型：</label>
            <span>{exam?.type}</span>
          </div>
          <div>
            <label style={{ fontWeight: "bold" }}>考试时长：</label>
            <span>{exam?.duration}分钟</span>
          </div>
          <div>
            <label style={{ fontWeight: "bold" }}>及格分数：</label>
            <span>{exam?.pass_score}分</span>
          </div>
          <div>
            <label style={{ fontWeight: "bold" }}>考试描述：</label>
            <span>{exam?.description || "-"}</span>
          </div>
        </div>
      </Card>

      <Card title="试题管理">
        <Tabs
          defaultActiveKey="1"
          items={[
            {
              key: "1",
              label: "试题列表",
              children: exam ? (
                <QuestionPage examId={exam.id} />
              ) : null,
            },
          ]}
        />
      </Card>
    </div>
  );
};

export default ExamDetailPage;
