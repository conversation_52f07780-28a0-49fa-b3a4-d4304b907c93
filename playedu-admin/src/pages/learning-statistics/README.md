# 学习统计分析页面

## 功能概述

学习统计分析页面提供了对课程和学员学习情况的全面统计分析功能，支持课程维度和人员维度的数据分析。

## 主要功能

### 1. 统计概览
- 总课程数：显示所有课程的数量
- 总学员数：显示参与学习的学员总数
- 平均完成率：所有课程的平均完成率
- 平均及格率：所有课程的平均及格率

### 2. 筛选条件
- 时间范围：支持按日期范围筛选
- 部门选择：支持多选部门进行筛选
- 课程选择：支持多选课程进行筛选
- 搜索功能：支持按课程名称或学员姓名搜索

### 3. 课程维度分析
- **数据表格**：展示课程名称、分类、学员数、完成率、平均分、及格率等信息
- **图表展示**：使用柱状图展示各课程的完成率与及格率对比
- **详情弹窗**：点击详情可查看具体课程的学员学习情况

### 4. 人员维度分析
- **数据表格**：展示学员姓名、部门、职位、课程数、完成率、平均分等信息
- **图表展示**：使用柱状图展示各学员的完成率与平均分对比
- **详情弹窗**：点击详情可查看具体学员的课程学习情况

### 5. 数据导出
支持将当前筛选条件下的数据导出为Excel文件

## 技术实现

### 前端技术栈
- React 18 + TypeScript
- Ant Design 组件库
- ECharts/Recharts 图表库
- Less 样式预处理

### 页面结构
```
learning-statistics/
├── index.tsx                    # 主页面组件
├── index.module.less           # 主页面样式
├── components/
│   ├── StatisticsCard.tsx      # 统计卡片组件
│   ├── StatisticsCard.module.less
│   ├── FilterBar.tsx          # 筛选条件栏组件
│   ├── CourseView.tsx         # 课程维度视图
│   ├── UserView.tsx           # 人员维度视图
│   ├── CourseDetailModal.tsx  # 课程详情弹窗
│   └── UserDetailModal.tsx    # 人员详情弹窗
└── README.md                  # 功能说明文档
```

## 使用说明

1. **访问页面**：在课程中心页面点击"学习进度"按钮即可进入学习统计分析页面
2. **切换维度**：使用顶部的Tab切换课程维度和人员维度
3. **筛选数据**：使用筛选条件栏设置查询条件
4. **查看详情**：点击表格中的"详情"按钮查看详细信息
5. **导出数据**：点击"导出"按钮下载数据文件

## 后续优化

- 接入真实API接口数据
- 增加更多图表类型和数据分析维度
- 支持自定义报表和定时导出功能
- 添加数据权限控制
- 优化移动端显示效果