import React, { useState, useEffect } from 'react';
import { Ta<PERSON>, <PERSON><PERSON>, Row, Col, Table, Spin, message, Input, Tag, Modal, Space } from 'antd';
import { DownloadOutlined, ReloadOutlined } from '@ant-design/icons';
import { learningStats } from '@/api';
import { dateFormat } from '@/utils';
import * as XLSX from 'xlsx';
import styles from './index.module.less';
import type { ColumnsType } from 'antd/es/table';

interface CourseStats {
  id: number;
  title: string;
  total_users: number;
  completed_users: number;
  completion_rate: number;
  avg_score: number;
  passing_users: number;
  passing_rate: number;
}

interface UserStats {
  id: number;
  name: string;
  email: string;
  department_name: string;
  total_courses: number;
  completed_courses: number;
  completion_rate: number;
  avg_score: number;
  passing_courses: number;
  passing_rate: number;
  latest_learn_at: string;
}

const LearningStatisticsPage = () => {
  const [activeTab, setActiveTab] = useState('course');
  const [loading, setLoading] = useState(false);
  const [courseData, setCourseData] = useState<CourseStats[]>([]);
  const [userData, setUserData] = useState<UserStats[]>([]);
  const [searchText, setSearchText] = useState('');

  useEffect(() => {
    loadData();
  }, [activeTab]);

  const loadData = async () => {
    try {
      setLoading(true);
      if (activeTab === 'course') {
        const response = await learningStats.getCourseStats();
        setCourseData(response.data.data || []);
      } else {
        const response = await learningStats.getUserStats();
        setUserData(response.data.data || []);
      }
    } catch (error) {
      message.error('加载数据失败');
    } finally {
      setLoading(false);
    }
  };

  const handleExport = async () => {
    try {
      setLoading(true);
      
      let data: any[] = [];
      let fileName = '';
      let headers: string[] = [];
      
      if (activeTab === 'course') {
        // 课程维度导出
        fileName = `学习统计-课程维度-${new Date().toISOString().split('T')[0]}.xlsx`;
        headers = ['课程名称', '总学员数', '已完成', '未完成', '完成率', '平均分', '及格人数', '及格率'];
        data = filteredCourseData.map(item => [
          item.title,
          item.total_users,
          item.completed_users,
          item.total_users - item.completed_users,
          `${Math.round(item.completion_rate * 100)}%`,
          item.avg_score.toFixed(1),
          item.passing_users,
          `${Math.round(item.passing_rate * 100)}%`
        ]);
      } else {
        // 人员维度导出
        fileName = `学习统计-人员维度-${new Date().toISOString().split('T')[0]}.xlsx`;
        headers = ['学员姓名', '邮箱', '所属部门', '学习课程', '完成课程', '完成率', '平均分', '及格课程', '及格率', '最后学习'];
        data = filteredUserData.map(item => [
          item.name,
          item.email,
          item.department_name,
          item.total_courses,
          item.completed_courses,
          `${Math.round(item.completion_rate * 100)}%`,
          item.avg_score.toFixed(1),
          item.passing_courses,
          `${Math.round(item.passing_rate * 100)}%`,
          item.latest_learn_at ? dateFormat(item.latest_learn_at) : '-'
        ]);
      }
      
      // 创建工作簿
      const workbook = XLSX.utils.book_new();
      
      // 将数据转换为工作表
      const worksheet = XLSX.utils.aoa_to_sheet([headers, ...data]);
      
      // 设置列宽
      const colWidths = headers.map(() => 18);
      worksheet['!cols'] = colWidths.map(width => ({ wch: width }));
      
      // 添加工作表到工作簿
      XLSX.utils.book_append_sheet(workbook, worksheet, activeTab === 'course' ? '课程统计' : '人员统计');
      
      // 下载Excel文件
      XLSX.writeFile(workbook, fileName);
        
      message.success('导出成功');
    } catch (error) {
      console.error('导出失败:', error);
      message.error('导出失败');
    } finally {
      setLoading(false);
    }
  };

  // 课程维度表格列
  const courseColumns: ColumnsType<CourseStats> = [
    {
      title: '课程名称',
      dataIndex: 'title',
      key: 'title',
      width: 200,
      ellipsis: true,
    },
    {
      title: '总学员数',
      dataIndex: 'total_users',
      key: 'total_users',
      width: 100,
      align: 'center',
    },
    {
      title: '已完成',
      dataIndex: 'completed_users',
      key: 'completed_users',
      width: 100,
      align: 'center',
      render: (text, record) => `${text}/${record.total_users}`,
    },
    {
      title: '完成率',
      dataIndex: 'completion_rate',
      key: 'completion_rate',
      width: 100,
      align: 'center',
      render: (rate: number) => {
        const percentage = Math.round(rate * 100);
        return <Tag color={percentage >= 80 ? 'green' : 'red'}>{percentage}%</Tag>;
      },
    },
    {
      title: '平均分',
      dataIndex: 'avg_score',
      key: 'avg_score',
      width: 100,
      align: 'center',
      render: (score: number) => score.toFixed(1),
    },
    {
      title: '及格人数',
      dataIndex: 'passing_users',
      key: 'passing_users',
      width: 100,
      align: 'center',
      render: (text, record) => `${text}/${record.total_users}`,
    },
    {
      title: '及格率',
      dataIndex: 'passing_rate',
      key: 'passing_rate',
      width: 100,
      align: 'center',
      render: (rate: number) => {
        const percentage = Math.round(rate * 100);
        return <Tag color={percentage >= 60 ? 'green' : 'red'}>{percentage}%</Tag>;
      },
    },
  ];

  // 人员维度表格列
  const userColumns: ColumnsType<UserStats> = [
    {
      title: '学员姓名',
      dataIndex: 'name',
      key: 'name',
      width: 150,
      ellipsis: true,
    },
    {
      title: '邮箱',
      dataIndex: 'email',
      key: 'email',
      width: 180,
      ellipsis: true,
    },
    {
      title: '所属部门',
      dataIndex: 'department_name',
      key: 'department_name',
      width: 150,
      ellipsis: true,
    },
    {
      title: '学习课程',
      dataIndex: 'total_courses',
      key: 'total_courses',
      width: 100,
      align: 'center',
      render: (text, record) => `${record.completed_courses}/${text}`,
    },
    {
      title: '完成率',
      dataIndex: 'completion_rate',
      key: 'completion_rate',
      width: 100,
      align: 'center',
      render: (rate: number) => {
        const percentage = Math.round(rate * 100);
        return <Tag color={percentage >= 80 ? 'green' : 'red'}>{percentage}%</Tag>;
      },
    },
    {
      title: '平均分',
      dataIndex: 'avg_score',
      key: 'avg_score',
      width: 100,
      align: 'center',
      render: (score: number) => score.toFixed(1),
    },
    {
      title: '及格课程',
      dataIndex: 'passing_courses',
      key: 'passing_courses',
      width: 100,
      align: 'center',
      render: (text, record) => `${text}/${record.total_courses}`,
    },
    {
      title: '及格率',
      dataIndex: 'passing_rate',
      key: 'passing_rate',
      width: 100,
      align: 'center',
      render: (rate: number) => {
        const percentage = Math.round(rate * 100);
        return <Tag color={percentage >= 60 ? 'green' : 'red'}>{percentage}%</Tag>;
      },
    },
    {
      title: '最后学习',
      dataIndex: 'latest_learn_at',
      key: 'latest_learn_at',
      width: 150,
      align: 'center',
      render: (text: string) => text ? dateFormat(text) : '-',
    },
  ];

  // 筛选后的数据
  const filteredCourseData = courseData.filter(item =>
    item.title.toLowerCase().includes(searchText.toLowerCase())
  );

  const filteredUserData = userData.filter(item =>
    item.name.toLowerCase().includes(searchText.toLowerCase()) ||
    item.email.toLowerCase().includes(searchText.toLowerCase())
  );

  return (
    <div className="playedu-main-body">
      <Row gutter={[24, 24]}>
        <Col span={24}>
          {/* 页面标题 */}
          <div className={styles['page-header']}>
            <div className={styles['title']}>学习统计分析</div>
            <Space>
              <Button 
                icon={<ReloadOutlined />}
                onClick={loadData}
                loading={loading}
              >
                刷新
              </Button>
              <Button 
                type="primary"
                icon={<DownloadOutlined />}
                onClick={handleExport}
                loading={loading}
              >
                导出
              </Button>
            </Space>
          </div>
              </Col>

        <Col span={24}>
          <div className={`playedu-main-top`}>
            <Tabs
              activeKey={activeTab}
              onChange={setActiveTab}
              items={[
                {
                  key: 'course',
                  label: '课程维度',
                  children: (
                    <div>
                      <div className={styles['search-bar']}>
                        <Input
                          placeholder="搜索课程名称..."
                          value={searchText}
                          onChange={(e) => setSearchText(e.target.value)}
                          style={{ width: 250 }}
                          allowClear
                        />
                      </div>
                    <Spin spinning={loading}>
                        <Table
                          columns={courseColumns}
                          dataSource={filteredCourseData}
                          rowKey="id"
                          pagination={{ pageSize: 20 }}
                          scroll={{ x: 1200 }}
                        />
                    </Spin>
                    </div>
                  ),
                },
                {
                  key: 'user',
                  label: '人员维度',
                  children: (
                    <div>
                      <div className={styles['search-bar']}>
                        <Input
                          placeholder="搜索学员姓名或邮箱..."
                          value={searchText}
                          onChange={(e) => setSearchText(e.target.value)}
                          style={{ width: 300 }}
                          allowClear
                        />
                      </div>
                    <Spin spinning={loading}>
                        <Table
                          columns={userColumns}
                          dataSource={filteredUserData}
                          rowKey="id"
                          pagination={{ pageSize: 20 }}
                          scroll={{ x: 1400 }}
                        />
                    </Spin>
                    </div>
                  ),
                },
              ]}
            />
          </div>
        </Col>
      </Row>
    </div>
  );
};

export default LearningStatisticsPage;