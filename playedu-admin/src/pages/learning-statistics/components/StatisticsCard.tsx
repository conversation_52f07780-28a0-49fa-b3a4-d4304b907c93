import React from 'react';
import { Card } from 'antd';
import styles from './StatisticsCard.module.less';

interface StatisticsCardProps {
  title: string;
  value: number;
  unit: string;
}

const StatisticsCard: React.FC<StatisticsCardProps> = ({ title, value, unit }) => {
  return (
    <Card className={styles.statisticsCard}>
      <div className={styles.content}>
        <div className={styles.title}>{title}</div>
        <div className={styles.value}>
          <span className={styles.number}>{value}</span>
          {unit && <span className={styles.unit}>{unit}</span>}
        </div>
      </div>
    </Card>
  );
};

export default StatisticsCard;