import React, { useState, useEffect } from 'react';
import { Table, Button, Progress, Tag, Space, Modal, Row, Col, Card, message } from 'antd';
import { <PERSON><PERSON><PERSON>, Bar, XAxis, YAxis, CartesianGrid, <PERSON>ltip, Legend, ResponsiveContainer } from 'recharts';
import { EyeOutlined } from '@ant-design/icons';
import CourseDetailModal from './CourseDetailModal';
import { learningStats } from '@/api';

interface CourseViewProps {
  filters: any;
}

interface CourseData {
  id: number;
  course_name: string;
  course_category: string;
  total_users: number;
  completed_users: number;
  completion_rate: number;
  avg_score: number;
  passing_rate: number;
  avg_learning_time: string;
}

const CourseView: React.FC<CourseViewProps> = ({ filters }) => {
  const [loading, setLoading] = useState(false);
  const [data, setData] = useState<CourseData[]>([]);
  const [selectedCourse, setSelectedCourse] = useState<CourseData | null>(null);
  const [detailModalVisible, setDetailModalVisible] = useState(false);

  // 加载课程统计数据
  const loadCourseStats = async () => {
    try {
      setLoading(true);
      const response = await learningStats.getCourseLearningStats(1, 100, filters);
      if (response.data && response.data.data) {
        const courseData = response.data.data.map((item: any) => ({
          id: item.id,
          course_name: item.title,
          course_category: item.category_name || '未分类',
          total_users: item.total_users || 0,
          completed_users: item.completed_users || 0,
          completion_rate: item.completion_rate || 0,
          avg_score: item.avg_score || 0,
          passing_rate: item.passing_rate || 0,
          avg_learning_time: item.avg_learning_time || '0分钟'
        }));
        setData(courseData);
      }
    } catch (error) {
      message.error('加载课程统计数据失败');
    } finally {
      setLoading(false);
    }
  };

  // 图表数据
  const chartData = data.map(item => ({
    name: item.course_name,
    completion_rate: item.completion_rate,
    passing_rate: item.passing_rate
  }));

  useEffect(() => {
    loadCourseStats();
  }, [filters]);

  const handleViewDetail = (record: CourseData) => {
    setSelectedCourse(record);
    setDetailModalVisible(true);
  };

  const columns = [
    {
      title: '课程名称',
      dataIndex: 'course_name',
      key: 'course_name',
      width: 200,
    },
    {
      title: '课程分类',
      dataIndex: 'course_category',
      key: 'course_category',
      width: 120,
      render: (text: string) => <Tag color="blue">{text}</Tag>,
    },
    {
      title: '学员总数',
      dataIndex: 'total_users',
      key: 'total_users',
      width: 100,
      align: 'center' as const,
    },
    {
      title: '完成人数',
      dataIndex: 'completed_users',
      key: 'completed_users',
      width: 100,
      align: 'center' as const,
    },
    {
      title: '完成率',
      dataIndex: 'completion_rate',
      key: 'completion_rate',
      width: 120,
      render: (rate: number) => (
        <Progress 
          percent={rate} 
          size="small"
          strokeColor={rate >= 80 ? '#52c41a' : rate >= 60 ? '#faad14' : '#ff4d4f'}
        />
      ),
    },
    {
      title: '平均分',
      dataIndex: 'avg_score',
      key: 'avg_score',
      width: 100,
      align: 'center' as const,
      render: (score: number) => (
        <span style={{ 
          color: score >= 80 ? '#52c41a' : score >= 60 ? '#faad14' : '#ff4d4f',
          fontWeight: 'bold'
        }}>
          {score}
        </span>
      ),
    },
    {
      title: '及格率',
      dataIndex: 'passing_rate',
      key: 'passing_rate',
      width: 120,
      render: (rate: number) => (
        <Progress 
          percent={rate} 
          size="small"
          strokeColor={rate >= 80 ? '#52c41a' : rate >= 60 ? '#faad14' : '#ff4d4f'}
        />
      ),
    },
    {
      title: '平均学习时长',
      dataIndex: 'avg_learning_time',
      key: 'avg_learning_time',
      width: 120,
      align: 'center' as const,
    },
    {
      title: '操作',
      key: 'action',
      width: 100,
      fixed: 'right' as const,
      render: (_: any, record: CourseData) => (
        <Space size="small">
          <Button
            type="link"
            size="small"
            icon={<EyeOutlined />}
            onClick={() => handleViewDetail(record)}
          >
            详情
          </Button>
        </Space>
      ),
    },
  ];

  return (
    <div>
      {/* 图表展示 */}
      <Row gutter={16} className="mb-16">
        <Col span={24}>
          <Card title="课程完成率与及格率对比" size="small">
            <ResponsiveContainer width="100%" height={300}>
              <BarChart data={chartData}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="name" />
                <YAxis />
                <Tooltip />
                <Legend />
                <Bar dataKey="completion_rate" fill="#1890ff" name="完成率" />
                <Bar dataKey="passing_rate" fill="#52c41a" name="及格率" />
              </BarChart>
            </ResponsiveContainer>
          </Card>
        </Col>
      </Row>

      {/* 数据表格 */}
      <Table
        rowKey="id"
        loading={loading}
        columns={columns}
        dataSource={data}
        scroll={{ x: 1000 }}
        pagination={{
          total: data.length,
          pageSize: 10,
          showSizeChanger: true,
          showQuickJumper: true,
          showTotal: (total) => `共 ${total} 条记录`,
        }}
      />

      {/* 详情弹窗 */}
      <CourseDetailModal
        visible={detailModalVisible}
        data={selectedCourse}
        onClose={() => setDetailModalVisible(false)}
      />
    </div>
  );
};

export default CourseView;