import React, { useState, useEffect } from 'react';
import { Modal, Table, Progress, Tag, Space, Row, Col, Card } from 'antd';
import { <PERSON><PERSON>hart, Bar, XAxis, YAxis, CartesianGrid, Too<PERSON><PERSON>, Legend, ResponsiveContainer } from 'recharts';

interface UserDetailModalProps {
  visible: boolean;
  data: any;
  onClose: () => void;
}

interface CourseDetailData {
  id: number;
  course_name: string;
  course_category: string;
  progress: number;
  score: number;
  status: string;
  learning_time: string;
  completed_at: string;
}

const UserDetailModal: React.FC<UserDetailModalProps> = ({ visible, data, onClose }) => {
  const [loading, setLoading] = useState(false);
  const [courseData, setCourseData] = useState<CourseDetailData[]>([]);

  // 模拟课程详情数据
  const mockCourseData: CourseDetailData[] = [
    {
      id: 1,
      course_name: '基础编程课程',
      course_category: '技术类',
      progress: 100,
      score: 85,
      status: '已完成',
      learning_time: '2小时30分',
      completed_at: '2024-01-15 14:30:00'
    },
    {
      id: 2,
      course_name: '项目管理基础',
      course_category: '管理类',
      progress: 100,
      score: 92,
      status: '已完成',
      learning_time: '3小时15分',
      completed_at: '2024-01-14 16:20:00'
    },
    {
      id: 3,
      course_name: '沟通技巧培训',
      course_category: '软技能',
      progress: 60,
      score: 78,
      status: '学习中',
      learning_time: '1小时20分',
      completed_at: '-'
    }
  ];

  // 图表数据
  const chartData = courseData.map(item => ({
    name: item.course_name,
    progress: item.progress,
    score: item.score
  }));

  useEffect(() => {
    if (visible && data) {
      setLoading(true);
      setTimeout(() => {
        setCourseData(mockCourseData);
        setLoading(false);
      }, 1000);
    }
  }, [visible, data]);

  const columns = [
    {
      title: '课程名称',
      dataIndex: 'course_name',
      key: 'course_name',
      width: 200,
    },
    {
      title: '课程分类',
      dataIndex: 'course_category',
      key: 'course_category',
      width: 120,
      render: (text: string) => <Tag color="blue">{text}</Tag>,
    },
    {
      title: '学习进度',
      dataIndex: 'progress',
      key: 'progress',
      width: 120,
      render: (progress: number) => (
        <Progress 
          percent={progress} 
          size="small"
          strokeColor={progress >= 80 ? '#52c41a' : progress >= 60 ? '#faad14' : '#ff4d4f'}
        />
      ),
    },
    {
      title: '考试成绩',
      dataIndex: 'score',
      key: 'score',
      width: 100,
      align: 'center' as const,
      render: (score: number) => (
        <span style={{ 
          color: score >= 80 ? '#52c41a' : score >= 60 ? '#faad14' : '#ff4d4f',
          fontWeight: 'bold'
        }}>
          {score}
        </span>
      ),
    },
    {
      title: '学习状态',
      dataIndex: 'status',
      key: 'status',
      width: 100,
      align: 'center' as const,
      render: (status: string) => {
        let color = 'default';
        if (status === '已完成') color = 'green';
        else if (status === '学习中') color = 'orange';
        else if (status === '未开始') color = 'red';
        return <Tag color={color}>{status}</Tag>;
      },
    },
    {
      title: '学习时长',
      dataIndex: 'learning_time',
      key: 'learning_time',
      width: 120,
      align: 'center' as const,
    },
    {
      title: '完成时间',
      dataIndex: 'completed_at',
      key: 'completed_at',
      width: 160,
      align: 'center' as const,
    },
  ];

  return (
    <Modal
      title={`学员详情 - ${data?.user_name || ''}`}
      visible={visible}
      onCancel={onClose}
      width={1200}
      footer={null}
    >
      <div>
        {/* 学员基本信息 */}
        <Row gutter={16} className="mb-16">
          <Col span={6}>
            <Card size="small" title="学员姓名">
              <div style={{ textAlign: 'center', fontSize: '16px', fontWeight: 'bold' }}>
                {data?.user_name}
              </div>
            </Card>
          </Col>
          <Col span={6}>
            <Card size="small" title="所属部门">
              <div style={{ textAlign: 'center', fontSize: '16px' }}>
                <Tag color="blue">{data?.department}</Tag>
              </div>
            </Card>
          </Col>
          <Col span={6}>
            <Card size="small" title="职位">
              <div style={{ textAlign: 'center', fontSize: '16px', fontWeight: 'bold' }}>
                {data?.position}
              </div>
            </Card>
          </Col>
          <Col span={6}>
            <Card size="small" title="完成率">
              <div style={{ textAlign: 'center' }}>
                <Progress 
                  percent={data?.completion_rate} 
                  size="small"
                  strokeColor={data?.completion_rate >= 80 ? '#52c41a' : data?.completion_rate >= 60 ? '#faad14' : '#ff4d4f'}
                />
              </div>
            </Card>
          </Col>
        </Row>

        {/* 图表展示 */}
        <Row gutter={16} className="mb-16">
          <Col span={24}>
            <Card title="课程学习进度与成绩对比" size="small">
              <ResponsiveContainer width="100%" height={300}>
                <BarChart data={chartData}>
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis dataKey="name" />
                  <YAxis />
                  <Tooltip />
                  <Legend />
                  <Bar dataKey="progress" fill="#1890ff" name="学习进度" />
                  <Bar dataKey="score" fill="#52c41a" name="考试成绩" />
                </BarChart>
              </ResponsiveContainer>
            </Card>
          </Col>
        </Row>

        {/* 课程详情表格 */}
        <Table
          rowKey="id"
          loading={loading}
          columns={columns}
          dataSource={courseData}
          scroll={{ x: 1000 }}
          pagination={{
            total: courseData.length,
            pageSize: 10,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total) => `共 ${total} 条记录`,
          }}
        />
      </div>
    </Modal>
  );
};

export default UserDetailModal;