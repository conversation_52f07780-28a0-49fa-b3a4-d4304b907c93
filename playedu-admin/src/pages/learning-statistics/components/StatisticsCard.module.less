.statisticsCard {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border: none;
  border-radius: 12px;
  color: white;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  
  .content {
    text-align: center;
    padding: 20px 0;
  }
  
  .title {
    font-size: 14px;
    font-weight: 500;
    opacity: 0.9;
    margin-bottom: 12px;
  }
  
  .value {
    display: flex;
    align-items: baseline;
    justify-content: center;
  }
  
  .number {
    font-size: 32px;
    font-weight: 700;
    margin-right: 4px;
  }
  
  .unit {
    font-size: 16px;
    opacity: 0.8;
  }
}