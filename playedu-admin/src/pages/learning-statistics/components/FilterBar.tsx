import React from 'react';
import { Row, Col, DatePicker, Input, Select, Button } from 'antd';
import { SearchOutlined, ReloadOutlined } from '@ant-design/icons';
import dayjs from 'dayjs';

const { RangePicker } = DatePicker;
const { Option } = Select;

interface FilterBarProps {
  filters: {
    dep_ids: string[];
    course_ids: string[];
    start_date: any;
    end_date: any;
    search: string;
  };
  onChange: (filters: any) => void;
}

const FilterBar: React.FC<FilterBarProps> = ({ filters, onChange }) => {
  const handleDateChange = (dates: any) => {
    onChange({
      ...filters,
      start_date: dates ? dates[0] : null,
      end_date: dates ? dates[1] : null
    });
  };

  const handleSearch = (value: string) => {
    onChange({
      ...filters,
      search: value
    });
  };

  const handleReset = () => {
    onChange({
      dep_ids: [],
      course_ids: [],
      start_date: null,
      end_date: null,
      search: ''
    });
  };

  return (
    <Row gutter={16} align="middle">
      <Col span={6}>
        <RangePicker
          style={{ width: '100%' }}
          placeholder={['开始日期', '结束日期']}
          value={filters.start_date && filters.end_date ? 
            [dayjs(filters.start_date), dayjs(filters.end_date)] : null}
          onChange={handleDateChange}
        />
      </Col>
      <Col span={4}>
        <Select
          style={{ width: '100%' }}
          placeholder="选择部门"
          mode="multiple"
          value={filters.dep_ids}
          onChange={(value) => onChange({ ...filters, dep_ids: value })}
          allowClear
        >
          {/* 这里需要接入实际的部门数据 */}
          <Option value="1">技术部</Option>
          <Option value="2">市场部</Option>
          <Option value="3">人事部</Option>
        </Select>
      </Col>
      <Col span={4}>
        <Select
          style={{ width: '100%' }}
          placeholder="选择课程"
          mode="multiple"
          value={filters.course_ids}
          onChange={(value) => onChange({ ...filters, course_ids: value })}
          allowClear
        >
          {/* 这里需要接入实际的课程数据 */}
          <Option value="1">基础课程</Option>
          <Option value="2">进阶课程</Option>
        </Select>
      </Col>
      <Col span={6}>
        <Input
          placeholder="搜索课程名称或学员姓名"
          prefix={<SearchOutlined />}
          value={filters.search}
          onChange={(e) => handleSearch(e.target.value)}
          allowClear
        />
      </Col>
      <Col span={4}>
        <Button icon={<ReloadOutlined />} onClick={handleReset}>
          重置
        </Button>
      </Col>
    </Row>
  );
};

export default FilterBar;