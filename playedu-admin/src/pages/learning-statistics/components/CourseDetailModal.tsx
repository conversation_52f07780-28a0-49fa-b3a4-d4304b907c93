import React, { useState, useEffect } from 'react';
import { Modal, Table, Progress, Tag, Space, Row, Col, Card } from 'antd';
import { <PERSON><PERSON>hart, Bar, XAxis, YAxis, CartesianGrid, <PERSON>lt<PERSON>, Legend, ResponsiveContainer } from 'recharts';

interface CourseDetailModalProps {
  visible: boolean;
  data: any;
  onClose: () => void;
}

interface UserDetailData {
  id: number;
  user_name: string;
  department: string;
  position: string;
  progress: number;
  score: number;
  status: string;
  learning_time: string;
  completed_at: string;
}

const CourseDetailModal: React.FC<CourseDetailModalProps> = ({ visible, data, onClose }) => {
  const [loading, setLoading] = useState(false);
  const [userData, setUserData] = useState<UserDetailData[]>([]);

  // 模拟学员详情数据
  const mockUserData: UserDetailData[] = [
    {
      id: 1,
      user_name: '张三',
      department: '技术部',
      position: '前端开发',
      progress: 100,
      score: 85,
      status: '已完成',
      learning_time: '2小时30分',
      completed_at: '2024-01-15 14:30:00'
    },
    {
      id: 2,
      user_name: '李四',
      department: '技术部',
      position: '后端开发',
      progress: 75,
      score: 78,
      status: '学习中',
      learning_time: '1小时45分',
      completed_at: '-'
    },
    {
      id: 3,
      user_name: '王五',
      department: '市场部',
      position: '市场专员',
      progress: 100,
      score: 92,
      status: '已完成',
      learning_time: '3小时10分',
      completed_at: '2024-01-14 16:20:00'
    }
  ];

  // 图表数据
  const chartData = userData.map(item => ({
    name: item.user_name,
    progress: item.progress,
    score: item.score
  }));

  useEffect(() => {
    if (visible && data) {
      setLoading(true);
      setTimeout(() => {
        setUserData(mockUserData);
        setLoading(false);
      }, 1000);
    }
  }, [visible, data]);

  const columns = [
    {
      title: '学员姓名',
      dataIndex: 'user_name',
      key: 'user_name',
      width: 120,
    },
    {
      title: '部门',
      dataIndex: 'department',
      key: 'department',
      width: 120,
      render: (text: string) => <Tag color="blue">{text}</Tag>,
    },
    {
      title: '职位',
      dataIndex: 'position',
      key: 'position',
      width: 120,
    },
    {
      title: '学习进度',
      dataIndex: 'progress',
      key: 'progress',
      width: 120,
      render: (progress: number) => (
        <Progress 
          percent={progress} 
          size="small"
          strokeColor={progress >= 80 ? '#52c41a' : progress >= 60 ? '#faad14' : '#ff4d4f'}
        />
      ),
    },
    {
      title: '考试成绩',
      dataIndex: 'score',
      key: 'score',
      width: 100,
      align: 'center' as const,
      render: (score: number) => (
        <span style={{ 
          color: score >= 80 ? '#52c41a' : score >= 60 ? '#faad14' : '#ff4d4f',
          fontWeight: 'bold'
        }}>
          {score}
        </span>
      ),
    },
    {
      title: '学习状态',
      dataIndex: 'status',
      key: 'status',
      width: 100,
      align: 'center' as const,
      render: (status: string) => {
        let color = 'default';
        if (status === '已完成') color = 'green';
        else if (status === '学习中') color = 'orange';
        else if (status === '未开始') color = 'red';
        return <Tag color={color}>{status}</Tag>;
      },
    },
    {
      title: '学习时长',
      dataIndex: 'learning_time',
      key: 'learning_time',
      width: 120,
      align: 'center' as const,
    },
    {
      title: '完成时间',
      dataIndex: 'completed_at',
      key: 'completed_at',
      width: 160,
      align: 'center' as const,
    },
  ];

  return (
    <Modal
      title={`课程详情 - ${data?.course_name || ''}`}
      visible={visible}
      onCancel={onClose}
      width={1200}
      footer={null}
    >
      <div>
        {/* 课程基本信息 */}
        <Row gutter={16} className="mb-16">
          <Col span={6}>
            <Card size="small" title="课程名称">
              <div style={{ textAlign: 'center', fontSize: '16px', fontWeight: 'bold' }}>
                {data?.course_name}
              </div>
            </Card>
          </Col>
          <Col span={6}>
            <Card size="small" title="课程分类">
              <div style={{ textAlign: 'center', fontSize: '16px' }}>
                <Tag color="blue">{data?.course_category}</Tag>
              </div>
            </Card>
          </Col>
          <Col span={6}>
            <Card size="small" title="学员总数">
              <div style={{ textAlign: 'center', fontSize: '16px', fontWeight: 'bold' }}>
                {data?.total_users} 人
              </div>
            </Card>
          </Col>
          <Col span={6}>
            <Card size="small" title="完成率">
              <div style={{ textAlign: 'center' }}>
                <Progress 
                  percent={data?.completion_rate} 
                  size="small"
                  strokeColor={data?.completion_rate >= 80 ? '#52c41a' : data?.completion_rate >= 60 ? '#faad14' : '#ff4d4f'}
                />
              </div>
            </Card>
          </Col>
        </Row>

        {/* 图表展示 */}
        <Row gutter={16} className="mb-16">
          <Col span={24}>
            <Card title="学员学习进度与成绩对比" size="small">
              <ResponsiveContainer width="100%" height={300}>
                <BarChart data={chartData}>
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis dataKey="name" />
                  <YAxis />
                  <Tooltip />
                  <Legend />
                  <Bar dataKey="progress" fill="#1890ff" name="学习进度" />
                  <Bar dataKey="score" fill="#52c41a" name="考试成绩" />
                </BarChart>
              </ResponsiveContainer>
            </Card>
          </Col>
        </Row>

        {/* 学员详情表格 */}
        <Table
          rowKey="id"
          loading={loading}
          columns={columns}
          dataSource={userData}
          scroll={{ x: 1000 }}
          pagination={{
            total: userData.length,
            pageSize: 10,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total) => `共 ${total} 条记录`,
          }}
        />
      </div>
    </Modal>
  );
};

export default CourseDetailModal;