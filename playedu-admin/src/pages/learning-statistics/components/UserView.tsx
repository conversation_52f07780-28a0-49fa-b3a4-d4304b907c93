import React, { useState, useEffect } from 'react';
import { Table, Button, Progress, Tag, Space, Modal, Row, Col, Card, message } from 'antd';
import { <PERSON><PERSON><PERSON>, Bar, XAxis, YAxis, CartesianGrid, <PERSON>ltip, Legend, ResponsiveContainer } from 'recharts';
import { EyeOutlined } from '@ant-design/icons';
import UserDetailModal from './UserDetailModal';
import { learningStats } from '@/api';

interface UserViewProps {
  filters: any;
}

interface UserData {
  id: number;
  user_name: string;
  department: string;
  position: string;
  total_courses: number;
  completed_courses: number;
  completion_rate: number;
  avg_score: number;
  total_learning_time: string;
  avg_learning_time: string;
}

const UserView: React.FC<UserViewProps> = ({ filters }) => {
  const [loading, setLoading] = useState(false);
  const [data, setData] = useState<UserData[]>([]);
  const [selectedUser, setSelectedUser] = useState<UserData | null>(null);
  const [detailModalVisible, setDetailModalVisible] = useState(false);

  // 加载用户学习统计数据
  const loadUserStats = async () => {
    try {
      setLoading(true);
      const response = await learningStats.getUserLearningStats(1, 100, filters);
      if (response.data && response.data.data) {
        const userData = response.data.data.map((item: any) => ({
          id: item.id,
          user_name: item.name,
          department: item.department_name || '未分配',
          position: item.position || '未知',
          total_courses: item.total_courses || 0,
          completed_courses: item.completed_courses || 0,
          completion_rate: item.completion_rate || 0,
          avg_score: item.avg_score || 0,
          total_learning_time: item.total_learning_time || '0分钟',
          avg_learning_time: item.avg_learning_time || '0分钟'
        }));
        setData(userData);
      }
    } catch (error) {
      message.error('加载用户学习统计数据失败');
    } finally {
      setLoading(false);
    }
  };

  // 图表数据
  const chartData = data.map(item => ({
    name: item.user_name,
    completion_rate: item.completion_rate,
    avg_score: item.avg_score
  }));

  useEffect(() => {
    loadUserStats();
  }, [filters]);

  const handleViewDetail = (record: UserData) => {
    setSelectedUser(record);
    setDetailModalVisible(true);
  };

  const columns = [
    {
      title: '学员姓名',
      dataIndex: 'user_name',
      key: 'user_name',
      width: 120,
    },
    {
      title: '部门',
      dataIndex: 'department',
      key: 'department',
      width: 120,
      render: (text: string) => <Tag color="blue">{text}</Tag>,
    },
    {
      title: '职位',
      dataIndex: 'position',
      key: 'position',
      width: 120,
    },
    {
      title: '课程总数',
      dataIndex: 'total_courses',
      key: 'total_courses',
      width: 100,
      align: 'center' as const,
    },
    {
      title: '完成课程',
      dataIndex: 'completed_courses',
      key: 'completed_courses',
      width: 100,
      align: 'center' as const,
    },
    {
      title: '完成率',
      dataIndex: 'completion_rate',
      key: 'completion_rate',
      width: 120,
      render: (rate: number) => (
        <Progress 
          percent={rate} 
          size="small"
          strokeColor={rate >= 80 ? '#52c41a' : rate >= 60 ? '#faad14' : '#ff4d4f'}
        />
      ),
    },
    {
      title: '平均分',
      dataIndex: 'avg_score',
      key: 'avg_score',
      width: 100,
      align: 'center' as const,
      render: (score: number) => (
        <span style={{ 
          color: score >= 80 ? '#52c41a' : score >= 60 ? '#faad14' : '#ff4d4f',
          fontWeight: 'bold'
        }}>
          {score}
        </span>
      ),
    },
    {
      title: '总学习时长',
      dataIndex: 'total_learning_time',
      key: 'total_learning_time',
      width: 120,
      align: 'center' as const,
    },
    {
      title: '平均学习时长',
      dataIndex: 'avg_learning_time',
      key: 'avg_learning_time',
      width: 120,
      align: 'center' as const,
    },
    {
      title: '操作',
      key: 'action',
      width: 100,
      fixed: 'right' as const,
      render: (_: any, record: UserData) => (
        <Space size="small">
          <Button
            type="link"
            size="small"
            icon={<EyeOutlined />}
            onClick={() => handleViewDetail(record)}
          >
            详情
          </Button>
        </Space>
      ),
    },
  ];

  return (
    <div>
      {/* 图表展示 */}
      <Row gutter={16} className="mb-16">
        <Col span={24}>
          <Card title="学员完成率与平均分对比" size="small">
            <ResponsiveContainer width="100%" height={300}>
              <BarChart data={chartData}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="name" />
                <YAxis />
                <Tooltip />
                <Legend />
                <Bar dataKey="completion_rate" fill="#1890ff" name="完成率" />
                <Bar dataKey="avg_score" fill="#52c41a" name="平均分" />
              </BarChart>
            </ResponsiveContainer>
          </Card>
        </Col>
      </Row>

      {/* 数据表格 */}
      <Table
        rowKey="id"
        loading={loading}
        columns={columns}
        dataSource={data}
        scroll={{ x: 1000 }}
        pagination={{
          total: data.length,
          pageSize: 10,
          showSizeChanger: true,
          showQuickJumper: true,
          showTotal: (total) => `共 ${total} 条记录`,
        }}
      />

      {/* 详情弹窗 */}
      <UserDetailModal
        visible={detailModalVisible}
        data={selectedUser}
        onClose={() => setDetailModalVisible(false)}
      />
    </div>
  );
};

export default UserView;