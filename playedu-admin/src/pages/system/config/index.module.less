.title {
  width: 100%;
  height: 16px;
  font-size: 16px;
  font-weight: 500;
  color: #333333;
  line-height: 16px;
  margin-bottom: 24px;
}
.body {
  width: 100%;
  height: auto;
  box-sizing: border-box;
  display: grid;
  gap: 20px;
  grid-template-columns: repeat(5, minmax(0, 1fr));
  .item {
    width: auto;
    height: 88px;
    background: #f3f4f5;
    box-shadow: 0px 2px 4px 0px rgba(102, 102, 102, 0.05);
    border-radius: 8px;
    display: flex;
    flex-direction: row;
    align-items: center;
    box-sizing: border-box;
    padding-left: 30px;
    cursor: pointer;
    &:hover {
      opacity: 0.8;
    }
    img {
      width: 48px;
      height: 48px;
      margin-right: 20px;
    }
    span {
      height: 16px;
      font-size: 16px;
      font-weight: 400;
      color: #333333;
      line-height: 16px;
    }
  }
}
