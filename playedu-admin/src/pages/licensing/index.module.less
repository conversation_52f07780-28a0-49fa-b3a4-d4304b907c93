.main-title {
  width: 100%;
  height: auto;
  float: left;
  font-weight: 500;
  font-size: 18px;
  color: #333333;
  line-height: 24px;
  text-align: left;
  font-style: normal;
  text-transform: none;
}

.persion {
  width: 158px;
  height: 32px;
  background: rgba(255, 77, 79, 0.1);
  border-radius: 6px 6px 6px 6px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 400;
  font-size: 14px;
  color: #ff4d4f;
}

.content {
  width: 100%;
  height: auto;
  float: left;
  font-weight: 400;
  font-size: 14px;
  color: #666666;
  line-height: 24px;
  text-align: left;
}

.contrast-box1 {
  width: 100%;
  height: 85px;
  float: left;
  background: #fafafa;
  border-radius: 16px;
  box-sizing: border-box;
  -moz-box-sizing: border-box;
  /* Firefox */
  -webkit-box-sizing: border-box;
  /* Safari */
  padding: 15px 100px 15px 100px;
  display: flex;
  align-items: center;
  .name {
    flex: 1;
    height: 25px;
    font-size: 18px;
    font-weight: 600;
    color: rgba(0, 0, 0, 0.88);
    line-height: 25px;
  }
  .ex {
    width: 114px;
    height: 55px;
    background: #ff4d4f;
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 18px;
    font-weight: 600;
    color: #ffffff;
    line-height: 25px;
    margin-right: 307px;
  }
  .ex2 {
    width: 114px;
    height: 55px;
    background: #f1aa2e;
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 18px;
    font-weight: 600;
    color: #ffffff;
    line-height: 25px;
    margin-right: 47px;
  }
}

.contrast-box2 {
  width: 100%;
  float: left;
  height: 65px;
  background: rgba(#f1aa2e, 0.1);
  border-radius: 16px 16px 0px 0px;
  box-sizing: border-box;
  -moz-box-sizing: border-box;
  /* Firefox */
  -webkit-box-sizing: border-box;
  /* Safari */
  padding: 20px 100px 20px 100px;
  display: flex;
  align-items: center;
  .name {
    flex: 1;
    height: 25px;
    font-weight: 600;
    font-size: 18px;
    color: rgba(0, 0, 0, 0.88);
    line-height: 25px;
  }
}

.contrast-box3 {
  width: 100%;
  height: 85px;
  float: left;
  height: 76px;
  border: 1px solid rgba(0, 0, 0, 0.05);
  box-sizing: border-box;
  -moz-box-sizing: border-box;
  /* Firefox */
  -webkit-box-sizing: border-box;
  /* Safari */
  padding: 30px 100px 30px 100px;
  display: flex;
  align-items: center;
  border-top: none;
  position: relative;
  .name {
    flex: 1;
    height: 16px;
    display: flex;
    align-items: center;
    strong {
      width: 8px;
      height: 8px;
      background: #f1aa2e;
      border-radius: 50%;
      margin-right: 10px;
    }
    span {
      font-weight: 400;
      font-size: 16px;
      color: #333333;
      line-height: 16px;
    }
    .sp {
      font-size: 14px;
      font-weight: 400;
      color: rgba(0, 0, 0, 0.45);
      line-height: 16px;
    }
  }
  .ex {
    width: 209px;
    height: auto;
    text-align: center;
    font-weight: 400;
    font-size: 14px;
    color: #ff4d4f;
    line-height: 16px;
    margin-right: 209px;
    display: flex;
    align-items: center;
    justify-content: center;
    .pic {
      width: 18px;
      height: 18px;
    }
  }
  .ex2 {
    width: 209px;
    height: auto;
    text-align: center;
    font-weight: 400;
    font-size: 14px;
    color: #ff9900;
    line-height: 16px;
    display: flex;
    align-items: center;
    justify-content: center;
    .pic {
      width: 18px;
      height: 18px;
    }
  }
}

.icon {
  width: 16px;
  height: 16px;
  margin-right: 5px;
}
