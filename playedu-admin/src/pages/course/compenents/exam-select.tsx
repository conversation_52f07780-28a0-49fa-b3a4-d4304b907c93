import React, { useState, useEffect } from "react";
import {
  Modal,
  Select,
  Button,
  Table,
  Space,
  message,
  Spin,
  Tag,
  Popconfirm,
} from "antd";
import { DeleteOutlined } from "@ant-design/icons";
import * as courseApi from "../../../api/course";
import * as examApi from "../../../api/exam";

interface ExamSelectProps {
  courseId?: number;
  id?: number;
  visible?: boolean;
  open?: boolean;
  onCancel?: () => void;
  onSuccess?: () => void;
}

interface ExamData {
  id: number;
  title: string;
  type: string;
  pass_score: number;
  duration: number;
}

const typeMap: Record<string, string> = {
  single_choice: "单选题",
  multiple_choice: "多选题",
};

export const ExamSelect: React.FC<ExamSelectProps> = ({
  courseId,
  id,
  visible,
  open,
  onCancel,
  onSuccess,
}) => {
  const cid = courseId || id || 0;
  const isVisible = visible || open || false;
  const [loading, setLoading] = useState(false);
  const [exams, setExams] = useState<ExamData[]>([]);
  const [allExams, setAllExams] = useState<ExamData[]>([]);
  const [selectedExamId, setSelectedExamId] = useState<number | undefined>();

  useEffect(() => {
    if (isVisible) {
      loadData();
    }
  }, [isVisible]);

  const loadData = async () => {
    setLoading(true);
    try {
      // 获取所有考试列表
      const allRes = await examApi.examList(1, 1000, "");
      if (allRes && allRes.code === 0) {
        setAllExams((allRes.data as any).data || []);
      }

      // 获取已关联的考试
      const linkedRes = await courseApi.courseExams(cid);
      if (linkedRes && linkedRes.code === 0) {
        setExams((linkedRes.data as any) || []);
      }
    } catch (error: any) {
      message.error(error.message || "加载数据失败");
    } finally {
      setLoading(false);
    }
  };

  const handleAddExam = async () => {
    if (!selectedExamId) {
      message.warning("请选择考试");
      return;
    }

    // 检查是否已添加
    if (exams.some((e: any) => e.id === selectedExamId)) {
      message.warning("该考试已关联");
      return;
    }

    setLoading(true);
    try {
      const res = await courseApi.addExamToCourse(cid, selectedExamId);
      if (res && res.code === 0) {
        message.success("添加成功");
        setSelectedExamId(undefined);
        loadData();
        onSuccess?.();
      } else {
        message.error((res as any)?.msg || "添加失败");
      }
    } catch (error: any) {
      message.error(error.message || "添加失败");
    } finally {
      setLoading(false);
    }
  };

  const handleDelete = async (examId: number) => {
    setLoading(true);
    try {
      const res = await courseApi.removeExamFromCourse(cid, examId);
      if (res && res.code === 0) {
        message.success("删除成功");
        loadData();
        onSuccess?.();
      } else {
        message.error((res as any)?.msg || "删除失败");
      }
    } catch (error: any) {
      message.error(error.message || "删除失败");
    } finally {
      setLoading(false);
    }
  };

  const columns = [
    {
      title: "考试名称",
      dataIndex: "title",
      key: "title",
      width: 200,
    },
    {
      title: "及格分数",
      dataIndex: "pass_score",
      key: "pass_score",
      width: 100,
      render: (score: number) => <span>{score}分</span>,
    },
    {
      title: "时长",
      dataIndex: "duration",
      key: "duration",
      width: 100,
      render: (duration: number) => <span>{duration}分钟</span>,
    },
    {
      title: "操作",
      key: "action",
      width: 100,
      render: (_: any, record: ExamData) => (
        <Popconfirm
          title="删除确认"
          description="确定要删除这个考试关联吗？"
          onConfirm={() => handleDelete(record.id)}
          okText="确认"
          cancelText="取消"
        >
          <Button
            type="link"
            danger
            size="small"
            icon={<DeleteOutlined />}
            loading={loading}
          >
            删除
          </Button>
        </Popconfirm>
      ),
    },
  ];

  // 筛选未关联的考试
  const availableExams = exams.length>=1 ? [] : allExams.filter(
    (exam) => !exams.some((e) => e.id === exam.id)
  );

  return (
    <Modal
      title="测试管理"
      open={isVisible}
      onCancel={onCancel}
      width={800}
      footer={[
        <Button key="cancel" onClick={onCancel}>
          关闭
        </Button>,
      ]}
    >
      <Spin spinning={loading}>
        <div style={{ marginBottom: "20px" }}>
          <div style={{ marginBottom: "10px" }}>
            <label style={{ marginRight: "10px" }}>选择考试：</label>
            <Select
              placeholder="请选择要关联的测试"
              showSearch
              optionFilterProp="children"
              style={{ width: 300 }}
              value={selectedExamId}
              onChange={setSelectedExamId}
            >
              {availableExams.map((exam) => (
                <Select.Option key={exam.id} value={exam.id}>
                  {exam.title}
                </Select.Option>
              ))}
            </Select>
            <Button
              type="primary"
              onClick={handleAddExam}
              style={{ marginLeft: "10px" }}
              loading={loading}
              disabled={exams.length>=1}
            >
              添加
            </Button>
          </div>
        </div>

        <Table
          columns={columns}
          dataSource={exams}
          rowKey="id"
          pagination={false}
          loading={loading}
        />
      </Spin>
    </Modal>
  );
};
