.preview-iframe {
  width: 100%;
  height: 90vh;
  border: none;
}

.preview-loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 90vh;
}

.preview-unsupported {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 90vh;
  
  p {
    font-size: 16px;
    color: #666;
    margin: 8px 0;
    
    a {
      color: #1890ff;
      text-decoration: underline;
      
      &:hover {
        color: #40a9ff;
      }
    }
  }
}

.close-button {
  position: fixed;
  top: 20px;
  right: 30px;
  width: 48px;
  height: 48px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(0, 0, 0, 0.6);
  color: white;
  font-size: 24px;
  border-radius: 50%;
  cursor: pointer;
  z-index: 10000;
  transition: all 0.3s;
  
  &:hover {
    background: rgba(0, 0, 0, 0.8);
    transform: scale(1.1);
  }
}

