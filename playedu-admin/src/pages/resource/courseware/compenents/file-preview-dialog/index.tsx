import React from "react";
import { Modal } from "antd";
import styles from "./index.module.less";
import { PdfViewer } from "../../../../../compenents";

interface PropInterface {
  id: number;
  url: string;
  name: string;
  extension: string;
  open: boolean;
  onCancel: () => void;
}

export const FilePreviewDialog: React.FC<PropInterface> = ({
  id,
  url,
  name,
  extension,
  open,
  onCancel,
}) => {
  // 渲染预览内容
  const renderPreviewContent = () => {
    const ext = extension.toUpperCase();

    // PDF 预览：使用 PDF.js 组件
    if (ext === "PDF") {
      const previewUrl = `/backend/v1/resource/${id}/preview`;
      return <PdfViewer url={previewUrl} />;
    }

    // TXT 预览：使用 embed
    if (ext === "TXT") {
      const previewUrl = `/backend/v1/resource/${id}/preview`;
      return (
        <embed
          src={previewUrl}
          type="text/plain"
          className={styles["preview-iframe"]}
        />
      );
    }

    // PPT, WORD, EXCEL 等使用 Office Online Viewer
    if (["PPT", "PPTX", "DOC", "DOCX", "XLS", "XLSX"].includes(ext)) {
      // 使用微软 Office Online Viewer
      const viewerUrl = `https://view.officeapps.live.com/op/embed.aspx?src=${encodeURIComponent(url)}`;
      return (
        <iframe
          src={viewerUrl}
          className={styles["preview-iframe"]}
          title={`${name}.${extension}`}
        />
      );
    }

    return (
      <div className={styles["preview-unsupported"]}>
        <p>此文件类型不支持在线预览</p>
        <p>
          <a href={url} target="_blank" rel="noopener noreferrer">
            点击下载文件
          </a>
        </p>
      </div>
    );
  };

  return (
    <Modal
      open={open}
      onCancel={onCancel}
      footer={null}
      width="90vw"
      styles={{ 
        body: { height: "90vh", padding: 0 },
        content: { padding: 0 }
      }}
      centered
      destroyOnClose
      closable={true}
      closeIcon={
        <div className={styles["close-button"]}>
          ✕
        </div>
      }
      title={null}
    >
      {renderPreviewContent()}
    </Modal>
  );
};

