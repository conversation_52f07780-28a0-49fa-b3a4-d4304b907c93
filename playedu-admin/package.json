{"name": "playedu-admin-interface", "private": false, "version": "1.6.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc && vite build", "preview": "vite preview"}, "dependencies": {"@ant-design/icons": "^5.2.3", "@reduxjs/toolkit": "^1.9.3", "ahooks": "^3.7.6", "antd": "^5.3.2", "axios": "^1.3.4", "dayjs": "^1.11.10", "echarts": "^5.4.2", "localforage": "^1.10.0", "match-sorter": "^6.3.1", "moment": "^2.29.4", "pdfjs-dist": "^5.4.394", "react": "^18.2.0", "react-dom": "^18.2.0", "react-redux": "^8.0.5", "react-router-dom": "^6.9.0", "recharts": "^3.3.0", "redux": "^4.2.1", "sort-by": "^1.2.0", "xlsx": "^0.18.5"}, "devDependencies": {"@types/react": "^18.2.0", "@types/react-dom": "^18.2.0", "@vitejs/plugin-react-swc": "^3.0.0", "less": "^4.1.3", "rollup-plugin-gzip": "^3.1.0", "typescript": "^4.9.3", "vite": "^4.2.0"}}