lockfileVersion: '6.0'

settings:
  autoInstallPeers: true
  excludeLinksFromLockfile: false

dependencies:
  '@ant-design/icons':
    specifier: ^5.2.3
    version: 5.2.3(react-dom@18.2.0)(react@18.2.0)
  '@reduxjs/toolkit':
    specifier: ^1.9.3
    version: 1.9.3(react-redux@8.0.5)(react@18.2.0)
  ahooks:
    specifier: ^3.7.6
    version: 3.7.6(react@18.2.0)
  antd:
    specifier: ^5.3.2
    version: 5.3.2(moment@2.29.4)(react-dom@18.2.0)(react@18.2.0)
  axios:
    specifier: ^1.3.4
    version: 1.3.4
  dayjs:
    specifier: ^1.11.10
    version: 1.11.10
  echarts:
    specifier: ^5.4.2
    version: 5.4.2
  localforage:
    specifier: ^1.10.0
    version: 1.10.0
  match-sorter:
    specifier: ^6.3.1
    version: 6.3.1
  moment:
    specifier: ^2.29.4
    version: 2.29.4
  react:
    specifier: ^18.2.0
    version: 18.2.0
  react-dom:
    specifier: ^18.2.0
    version: 18.2.0(react@18.2.0)
  react-redux:
    specifier: ^8.0.5
    version: 8.0.5(@types/react-dom@18.2.0)(@types/react@18.2.0)(react-dom@18.2.0)(react@18.2.0)(redux@4.2.1)
  react-router-dom:
    specifier: ^6.9.0
    version: 6.9.0(react-dom@18.2.0)(react@18.2.0)
  redux:
    specifier: ^4.2.1
    version: 4.2.1
  sort-by:
    specifier: ^1.2.0
    version: 1.2.0
  xlsx:
    specifier: ^0.18.5
    version: 0.18.5

devDependencies:
  '@types/react':
    specifier: ^18.2.0
    version: 18.2.0
  '@types/react-dom':
    specifier: ^18.2.0
    version: 18.2.0
  '@vitejs/plugin-react-swc':
    specifier: ^3.0.0
    version: 3.0.0(vite@4.2.0)
  less:
    specifier: ^4.1.3
    version: 4.1.3
  rollup-plugin-gzip:
    specifier: ^3.1.0
    version: 3.1.0(rollup@4.18.0)
  typescript:
    specifier: ^4.9.3
    version: 4.9.3
  vite:
    specifier: ^4.2.0
    version: 4.2.0(less@4.1.3)

packages:

  /@ant-design/colors@7.1.0:
    resolution: {integrity: sha512-MMoDGWn1y9LdQJQSHiCC20x3uZ3CwQnv9QMz6pCmJOrqdgM9YxsoVVY0wtrdXbmfSgnV0KNk6zi09NAhMR2jvg==}
    dependencies:
      '@ctrl/tinycolor': 3.6.1
    dev: false

  /@ant-design/cssinjs@1.21.0(react-dom@18.2.0)(react@18.2.0):
    resolution: {integrity: sha512-gIilraPl+9EoKdYxnupxjHB/Q6IHNRjEXszKbDxZdsgv4sAZ9pjkCq8yanDWNvyfjp4leir2OVAJm0vxwKK8YA==}
    peerDependencies:
      react: '>=16.0.0'
      react-dom: '>=16.0.0'
    dependencies:
      '@babel/runtime': 7.24.7
      '@emotion/hash': 0.8.0
      '@emotion/unitless': 0.7.5
      classnames: 2.5.1
      csstype: 3.1.3
      rc-util: 5.43.0(react-dom@18.2.0)(react@18.2.0)
      react: 18.2.0
      react-dom: 18.2.0(react@18.2.0)
      stylis: 4.3.2
    dev: false

  /@ant-design/icons-svg@4.4.2:
    resolution: {integrity: sha512-vHbT+zJEVzllwP+CM+ul7reTEfBR0vgxFe7+lREAsAA7YGsYpboiq2sQNeQeRvh09GfQgs/GyFEvZpJ9cLXpXA==}
    dev: false

  /@ant-design/icons@5.2.3(react-dom@18.2.0)(react@18.2.0):
    resolution: {integrity: sha512-XB8606F4zTROJ1tReOJ1EK3yc1qzRbH9X66aqA9Usv1cEfVIEl6aAEF1S+L4/wH/1sgAHpbbtO6FJIKmDJPe6w==}
    engines: {node: '>=8'}
    peerDependencies:
      react: '>=16.0.0'
      react-dom: '>=16.0.0'
    dependencies:
      '@ant-design/colors': 7.1.0
      '@ant-design/icons-svg': 4.4.2
      '@babel/runtime': 7.24.7
      classnames: 2.5.1
      lodash.camelcase: 4.3.0
      rc-util: 5.43.0(react-dom@18.2.0)(react@18.2.0)
      react: 18.2.0
      react-dom: 18.2.0(react@18.2.0)
    dev: false

  /@ant-design/react-slick@1.0.2(react@18.2.0):
    resolution: {integrity: sha512-Wj8onxL/T8KQLFFiCA4t8eIRGpRR+UPgOdac2sYzonv+i0n3kXHmvHLLiOYL655DQx2Umii9Y9nNgL7ssu5haQ==}
    peerDependencies:
      react: '>=16.9.0'
    dependencies:
      '@babel/runtime': 7.24.7
      classnames: 2.5.1
      json2mq: 0.2.0
      react: 18.2.0
      resize-observer-polyfill: 1.5.1
      throttle-debounce: 5.0.2
    dev: false

  /@babel/runtime@7.24.7:
    resolution: {integrity: sha512-UwgBRMjJP+xv857DCngvqXI3Iq6J4v0wXmwc6sapg+zyhbwmQX67LUEFrkK5tbyJ30jGuG3ZvWpBiB9LCy1kWw==}
    engines: {node: '>=6.9.0'}
    dependencies:
      regenerator-runtime: 0.14.1
    dev: false

  /@ctrl/tinycolor@3.6.1:
    resolution: {integrity: sha512-SITSV6aIXsuVNV3f3O0f2n/cgyEDWoSqtZMYiAmcsYHydcKrOz3gUxB/iXd/Qf08+IZX4KpgNbvUdMBmWz+kcA==}
    engines: {node: '>=10'}
    dev: false

  /@emotion/hash@0.8.0:
    resolution: {integrity: sha512-kBJtf7PH6aWwZ6fka3zQ0p6SBYzx4fl1LoZXE2RrnYST9Xljm7WfKJrU4g/Xr3Beg72MLrp1AWNUmuYJTL7Cow==}
    dev: false

  /@emotion/unitless@0.7.5:
    resolution: {integrity: sha512-OWORNpfjMsSSUBVrRBVGECkhWcULOAJz9ZW8uK9qgxD+87M7jHRcvh/A96XXNhXTLmKcoYSQtBEX7lHMO7YRwg==}
    dev: false

  /@esbuild/android-arm64@0.17.19:
    resolution: {integrity: sha512-KBMWvEZooR7+kzY0BtbTQn0OAYY7CsiydT63pVEaPtVYF0hXbUaOyZog37DKxK7NF3XacBJOpYT4adIJh+avxA==}
    engines: {node: '>=12'}
    cpu: [arm64]
    os: [android]
    requiresBuild: true
    dev: true
    optional: true

  /@esbuild/android-arm@0.17.19:
    resolution: {integrity: sha512-rIKddzqhmav7MSmoFCmDIb6e2W57geRsM94gV2l38fzhXMwq7hZoClug9USI2pFRGL06f4IOPHHpFNOkWieR8A==}
    engines: {node: '>=12'}
    cpu: [arm]
    os: [android]
    requiresBuild: true
    dev: true
    optional: true

  /@esbuild/android-x64@0.17.19:
    resolution: {integrity: sha512-uUTTc4xGNDT7YSArp/zbtmbhO0uEEK9/ETW29Wk1thYUJBz3IVnvgEiEwEa9IeLyvnpKrWK64Utw2bgUmDveww==}
    engines: {node: '>=12'}
    cpu: [x64]
    os: [android]
    requiresBuild: true
    dev: true
    optional: true

  /@esbuild/darwin-arm64@0.17.19:
    resolution: {integrity: sha512-80wEoCfF/hFKM6WE1FyBHc9SfUblloAWx6FJkFWTWiCoht9Mc0ARGEM47e67W9rI09YoUxJL68WHfDRYEAvOhg==}
    engines: {node: '>=12'}
    cpu: [arm64]
    os: [darwin]
    requiresBuild: true
    dev: true
    optional: true

  /@esbuild/darwin-x64@0.17.19:
    resolution: {integrity: sha512-IJM4JJsLhRYr9xdtLytPLSH9k/oxR3boaUIYiHkAawtwNOXKE8KoU8tMvryogdcT8AU+Bflmh81Xn6Q0vTZbQw==}
    engines: {node: '>=12'}
    cpu: [x64]
    os: [darwin]
    requiresBuild: true
    dev: true
    optional: true

  /@esbuild/freebsd-arm64@0.17.19:
    resolution: {integrity: sha512-pBwbc7DufluUeGdjSU5Si+P3SoMF5DQ/F/UmTSb8HXO80ZEAJmrykPyzo1IfNbAoaqw48YRpv8shwd1NoI0jcQ==}
    engines: {node: '>=12'}
    cpu: [arm64]
    os: [freebsd]
    requiresBuild: true
    dev: true
    optional: true

  /@esbuild/freebsd-x64@0.17.19:
    resolution: {integrity: sha512-4lu+n8Wk0XlajEhbEffdy2xy53dpR06SlzvhGByyg36qJw6Kpfk7cp45DR/62aPH9mtJRmIyrXAS5UWBrJT6TQ==}
    engines: {node: '>=12'}
    cpu: [x64]
    os: [freebsd]
    requiresBuild: true
    dev: true
    optional: true

  /@esbuild/linux-arm64@0.17.19:
    resolution: {integrity: sha512-ct1Tg3WGwd3P+oZYqic+YZF4snNl2bsnMKRkb3ozHmnM0dGWuxcPTTntAF6bOP0Sp4x0PjSF+4uHQ1xvxfRKqg==}
    engines: {node: '>=12'}
    cpu: [arm64]
    os: [linux]
    requiresBuild: true
    dev: true
    optional: true

  /@esbuild/linux-arm@0.17.19:
    resolution: {integrity: sha512-cdmT3KxjlOQ/gZ2cjfrQOtmhG4HJs6hhvm3mWSRDPtZ/lP5oe8FWceS10JaSJC13GBd4eH/haHnqf7hhGNLerA==}
    engines: {node: '>=12'}
    cpu: [arm]
    os: [linux]
    requiresBuild: true
    dev: true
    optional: true

  /@esbuild/linux-ia32@0.17.19:
    resolution: {integrity: sha512-w4IRhSy1VbsNxHRQpeGCHEmibqdTUx61Vc38APcsRbuVgK0OPEnQ0YD39Brymn96mOx48Y2laBQGqgZ0j9w6SQ==}
    engines: {node: '>=12'}
    cpu: [ia32]
    os: [linux]
    requiresBuild: true
    dev: true
    optional: true

  /@esbuild/linux-loong64@0.17.19:
    resolution: {integrity: sha512-2iAngUbBPMq439a+z//gE+9WBldoMp1s5GWsUSgqHLzLJ9WoZLZhpwWuym0u0u/4XmZ3gpHmzV84PonE+9IIdQ==}
    engines: {node: '>=12'}
    cpu: [loong64]
    os: [linux]
    requiresBuild: true
    dev: true
    optional: true

  /@esbuild/linux-mips64el@0.17.19:
    resolution: {integrity: sha512-LKJltc4LVdMKHsrFe4MGNPp0hqDFA1Wpt3jE1gEyM3nKUvOiO//9PheZZHfYRfYl6AwdTH4aTcXSqBerX0ml4A==}
    engines: {node: '>=12'}
    cpu: [mips64el]
    os: [linux]
    requiresBuild: true
    dev: true
    optional: true

  /@esbuild/linux-ppc64@0.17.19:
    resolution: {integrity: sha512-/c/DGybs95WXNS8y3Ti/ytqETiW7EU44MEKuCAcpPto3YjQbyK3IQVKfF6nbghD7EcLUGl0NbiL5Rt5DMhn5tg==}
    engines: {node: '>=12'}
    cpu: [ppc64]
    os: [linux]
    requiresBuild: true
    dev: true
    optional: true

  /@esbuild/linux-riscv64@0.17.19:
    resolution: {integrity: sha512-FC3nUAWhvFoutlhAkgHf8f5HwFWUL6bYdvLc/TTuxKlvLi3+pPzdZiFKSWz/PF30TB1K19SuCxDTI5KcqASJqA==}
    engines: {node: '>=12'}
    cpu: [riscv64]
    os: [linux]
    requiresBuild: true
    dev: true
    optional: true

  /@esbuild/linux-s390x@0.17.19:
    resolution: {integrity: sha512-IbFsFbxMWLuKEbH+7sTkKzL6NJmG2vRyy6K7JJo55w+8xDk7RElYn6xvXtDW8HCfoKBFK69f3pgBJSUSQPr+4Q==}
    engines: {node: '>=12'}
    cpu: [s390x]
    os: [linux]
    requiresBuild: true
    dev: true
    optional: true

  /@esbuild/linux-x64@0.17.19:
    resolution: {integrity: sha512-68ngA9lg2H6zkZcyp22tsVt38mlhWde8l3eJLWkyLrp4HwMUr3c1s/M2t7+kHIhvMjglIBrFpncX1SzMckomGw==}
    engines: {node: '>=12'}
    cpu: [x64]
    os: [linux]
    requiresBuild: true
    dev: true
    optional: true

  /@esbuild/netbsd-x64@0.17.19:
    resolution: {integrity: sha512-CwFq42rXCR8TYIjIfpXCbRX0rp1jo6cPIUPSaWwzbVI4aOfX96OXY8M6KNmtPcg7QjYeDmN+DD0Wp3LaBOLf4Q==}
    engines: {node: '>=12'}
    cpu: [x64]
    os: [netbsd]
    requiresBuild: true
    dev: true
    optional: true

  /@esbuild/openbsd-x64@0.17.19:
    resolution: {integrity: sha512-cnq5brJYrSZ2CF6c35eCmviIN3k3RczmHz8eYaVlNasVqsNY+JKohZU5MKmaOI+KkllCdzOKKdPs762VCPC20g==}
    engines: {node: '>=12'}
    cpu: [x64]
    os: [openbsd]
    requiresBuild: true
    dev: true
    optional: true

  /@esbuild/sunos-x64@0.17.19:
    resolution: {integrity: sha512-vCRT7yP3zX+bKWFeP/zdS6SqdWB8OIpaRq/mbXQxTGHnIxspRtigpkUcDMlSCOejlHowLqII7K2JKevwyRP2rg==}
    engines: {node: '>=12'}
    cpu: [x64]
    os: [sunos]
    requiresBuild: true
    dev: true
    optional: true

  /@esbuild/win32-arm64@0.17.19:
    resolution: {integrity: sha512-yYx+8jwowUstVdorcMdNlzklLYhPxjniHWFKgRqH7IFlUEa0Umu3KuYplf1HUZZ422e3NU9F4LGb+4O0Kdcaag==}
    engines: {node: '>=12'}
    cpu: [arm64]
    os: [win32]
    requiresBuild: true
    dev: true
    optional: true

  /@esbuild/win32-ia32@0.17.19:
    resolution: {integrity: sha512-eggDKanJszUtCdlVs0RB+h35wNlb5v4TWEkq4vZcmVt5u/HiDZrTXe2bWFQUez3RgNHwx/x4sk5++4NSSicKkw==}
    engines: {node: '>=12'}
    cpu: [ia32]
    os: [win32]
    requiresBuild: true
    dev: true
    optional: true

  /@esbuild/win32-x64@0.17.19:
    resolution: {integrity: sha512-lAhycmKnVOuRYNtRtatQR1LPQf2oYCkRGkSFnseDAKPl8lu5SOsK/e1sXe5a0Pc5kHIHe6P2I/ilntNv2xf3cA==}
    engines: {node: '>=12'}
    cpu: [x64]
    os: [win32]
    requiresBuild: true
    dev: true
    optional: true

  /@rc-component/context@1.4.0(react-dom@18.2.0)(react@18.2.0):
    resolution: {integrity: sha512-kFcNxg9oLRMoL3qki0OMxK+7g5mypjgaaJp/pkOis/6rVxma9nJBF/8kCIuTYHUQNr0ii7MxqE33wirPZLJQ2w==}
    peerDependencies:
      react: '>=16.9.0'
      react-dom: '>=16.9.0'
    dependencies:
      '@babel/runtime': 7.24.7
      rc-util: 5.43.0(react-dom@18.2.0)(react@18.2.0)
      react: 18.2.0
      react-dom: 18.2.0(react@18.2.0)
    dev: false

  /@rc-component/mini-decimal@1.1.0:
    resolution: {integrity: sha512-jS4E7T9Li2GuYwI6PyiVXmxTiM6b07rlD9Ge8uGZSCz3WlzcG5ZK7g5bbuKNeZ9pgUuPK/5guV781ujdVpm4HQ==}
    engines: {node: '>=8.x'}
    dependencies:
      '@babel/runtime': 7.24.7
    dev: false

  /@rc-component/mutate-observer@1.1.0(react-dom@18.2.0)(react@18.2.0):
    resolution: {integrity: sha512-QjrOsDXQusNwGZPf4/qRQasg7UFEj06XiCJ8iuiq/Io7CrHrgVi6Uuetw60WAMG1799v+aM8kyc+1L/GBbHSlw==}
    engines: {node: '>=8.x'}
    peerDependencies:
      react: '>=16.9.0'
      react-dom: '>=16.9.0'
    dependencies:
      '@babel/runtime': 7.24.7
      classnames: 2.5.1
      rc-util: 5.43.0(react-dom@18.2.0)(react@18.2.0)
      react: 18.2.0
      react-dom: 18.2.0(react@18.2.0)
    dev: false

  /@rc-component/portal@1.1.2(react-dom@18.2.0)(react@18.2.0):
    resolution: {integrity: sha512-6f813C0IsasTZms08kfA8kPAGxbbkYToa8ALaiDIGGECU4i9hj8Plgbx0sNJDrey3EtHO30hmdaxtT0138xZcg==}
    engines: {node: '>=8.x'}
    peerDependencies:
      react: '>=16.9.0'
      react-dom: '>=16.9.0'
    dependencies:
      '@babel/runtime': 7.24.7
      classnames: 2.5.1
      rc-util: 5.43.0(react-dom@18.2.0)(react@18.2.0)
      react: 18.2.0
      react-dom: 18.2.0(react@18.2.0)
    dev: false

  /@rc-component/tour@1.8.1(react-dom@18.2.0)(react@18.2.0):
    resolution: {integrity: sha512-CsrQnfKgNArxx2j1RNHVLZgVA+rLrEj06lIsl4KSynMqADsqz8eKvVkr0F3p9PA10948M6WEEZt5a/FGAbGR2A==}
    engines: {node: '>=8.x'}
    peerDependencies:
      react: '>=16.9.0'
      react-dom: '>=16.9.0'
    dependencies:
      '@babel/runtime': 7.24.7
      '@rc-component/portal': 1.1.2(react-dom@18.2.0)(react@18.2.0)
      '@rc-component/trigger': 1.18.3(react-dom@18.2.0)(react@18.2.0)
      classnames: 2.5.1
      rc-util: 5.43.0(react-dom@18.2.0)(react@18.2.0)
      react: 18.2.0
      react-dom: 18.2.0(react@18.2.0)
    dev: false

  /@rc-component/trigger@1.18.3(react-dom@18.2.0)(react@18.2.0):
    resolution: {integrity: sha512-Ksr25pXreYe1gX6ayZ1jLrOrl9OAUHUqnuhEx6MeHnNa1zVM5Y2Aj3Q35UrER0ns8D2cJYtmJtVli+i+4eKrvA==}
    engines: {node: '>=8.x'}
    peerDependencies:
      react: '>=16.9.0'
      react-dom: '>=16.9.0'
    dependencies:
      '@babel/runtime': 7.24.7
      '@rc-component/portal': 1.1.2(react-dom@18.2.0)(react@18.2.0)
      classnames: 2.5.1
      rc-motion: 2.9.2(react-dom@18.2.0)(react@18.2.0)
      rc-resize-observer: 1.4.0(react-dom@18.2.0)(react@18.2.0)
      rc-util: 5.43.0(react-dom@18.2.0)(react@18.2.0)
      react: 18.2.0
      react-dom: 18.2.0(react@18.2.0)
    dev: false

  /@reduxjs/toolkit@1.9.3(react-redux@8.0.5)(react@18.2.0):
    resolution: {integrity: sha512-GU2TNBQVofL09VGmuSioNPQIu6Ml0YLf4EJhgj0AvBadRlCGzUWet8372LjvO4fqKZF2vH1xU0htAa7BrK9pZg==}
    peerDependencies:
      react: ^16.9.0 || ^17.0.0 || ^18
      react-redux: ^7.2.1 || ^8.0.2
    peerDependenciesMeta:
      react:
        optional: true
      react-redux:
        optional: true
    dependencies:
      immer: 9.0.21
      react: 18.2.0
      react-redux: 8.0.5(@types/react-dom@18.2.0)(@types/react@18.2.0)(react-dom@18.2.0)(react@18.2.0)(redux@4.2.1)
      redux: 4.2.1
      redux-thunk: 2.4.2(redux@4.2.1)
      reselect: 4.1.8
    dev: false

  /@remix-run/router@1.4.0:
    resolution: {integrity: sha512-BJ9SxXux8zAg991UmT8slpwpsd31K1dHHbD3Ba4VzD+liLQ4WAMSxQp2d2ZPRPfN0jN2NPRowcSSoM7lCaF08Q==}
    engines: {node: '>=14'}
    dev: false

  /@rollup/rollup-android-arm-eabi@4.18.0:
    resolution: {integrity: sha512-Tya6xypR10giZV1XzxmH5wr25VcZSncG0pZIjfePT0OVBvqNEurzValetGNarVrGiq66EBVAFn15iYX4w6FKgQ==}
    cpu: [arm]
    os: [android]
    requiresBuild: true
    dev: true
    optional: true

  /@rollup/rollup-android-arm64@4.18.0:
    resolution: {integrity: sha512-avCea0RAP03lTsDhEyfy+hpfr85KfyTctMADqHVhLAF3MlIkq83CP8UfAHUssgXTYd+6er6PaAhx/QGv4L1EiA==}
    cpu: [arm64]
    os: [android]
    requiresBuild: true
    dev: true
    optional: true

  /@rollup/rollup-darwin-arm64@4.18.0:
    resolution: {integrity: sha512-IWfdwU7KDSm07Ty0PuA/W2JYoZ4iTj3TUQjkVsO/6U+4I1jN5lcR71ZEvRh52sDOERdnNhhHU57UITXz5jC1/w==}
    cpu: [arm64]
    os: [darwin]
    requiresBuild: true
    dev: true
    optional: true

  /@rollup/rollup-darwin-x64@4.18.0:
    resolution: {integrity: sha512-n2LMsUz7Ynu7DoQrSQkBf8iNrjOGyPLrdSg802vk6XT3FtsgX6JbE8IHRvposskFm9SNxzkLYGSq9QdpLYpRNA==}
    cpu: [x64]
    os: [darwin]
    requiresBuild: true
    dev: true
    optional: true

  /@rollup/rollup-linux-arm-gnueabihf@4.18.0:
    resolution: {integrity: sha512-C/zbRYRXFjWvz9Z4haRxcTdnkPt1BtCkz+7RtBSuNmKzMzp3ZxdM28Mpccn6pt28/UWUCTXa+b0Mx1k3g6NOMA==}
    cpu: [arm]
    os: [linux]
    libc: [glibc]
    requiresBuild: true
    dev: true
    optional: true

  /@rollup/rollup-linux-arm-musleabihf@4.18.0:
    resolution: {integrity: sha512-l3m9ewPgjQSXrUMHg93vt0hYCGnrMOcUpTz6FLtbwljo2HluS4zTXFy2571YQbisTnfTKPZ01u/ukJdQTLGh9A==}
    cpu: [arm]
    os: [linux]
    libc: [musl]
    requiresBuild: true
    dev: true
    optional: true

  /@rollup/rollup-linux-arm64-gnu@4.18.0:
    resolution: {integrity: sha512-rJ5D47d8WD7J+7STKdCUAgmQk49xuFrRi9pZkWoRD1UeSMakbcepWXPF8ycChBoAqs1pb2wzvbY6Q33WmN2ftw==}
    cpu: [arm64]
    os: [linux]
    libc: [glibc]
    requiresBuild: true
    dev: true
    optional: true

  /@rollup/rollup-linux-arm64-musl@4.18.0:
    resolution: {integrity: sha512-be6Yx37b24ZwxQ+wOQXXLZqpq4jTckJhtGlWGZs68TgdKXJgw54lUUoFYrg6Zs/kjzAQwEwYbp8JxZVzZLRepQ==}
    cpu: [arm64]
    os: [linux]
    libc: [musl]
    requiresBuild: true
    dev: true
    optional: true

  /@rollup/rollup-linux-powerpc64le-gnu@4.18.0:
    resolution: {integrity: sha512-hNVMQK+qrA9Todu9+wqrXOHxFiD5YmdEi3paj6vP02Kx1hjd2LLYR2eaN7DsEshg09+9uzWi2W18MJDlG0cxJA==}
    cpu: [ppc64]
    os: [linux]
    libc: [glibc]
    requiresBuild: true
    dev: true
    optional: true

  /@rollup/rollup-linux-riscv64-gnu@4.18.0:
    resolution: {integrity: sha512-ROCM7i+m1NfdrsmvwSzoxp9HFtmKGHEqu5NNDiZWQtXLA8S5HBCkVvKAxJ8U+CVctHwV2Gb5VUaK7UAkzhDjlg==}
    cpu: [riscv64]
    os: [linux]
    libc: [glibc]
    requiresBuild: true
    dev: true
    optional: true

  /@rollup/rollup-linux-s390x-gnu@4.18.0:
    resolution: {integrity: sha512-0UyyRHyDN42QL+NbqevXIIUnKA47A+45WyasO+y2bGJ1mhQrfrtXUpTxCOrfxCR4esV3/RLYyucGVPiUsO8xjg==}
    cpu: [s390x]
    os: [linux]
    libc: [glibc]
    requiresBuild: true
    dev: true
    optional: true

  /@rollup/rollup-linux-x64-gnu@4.18.0:
    resolution: {integrity: sha512-xuglR2rBVHA5UsI8h8UbX4VJ470PtGCf5Vpswh7p2ukaqBGFTnsfzxUBetoWBWymHMxbIG0Cmx7Y9qDZzr648w==}
    cpu: [x64]
    os: [linux]
    libc: [glibc]
    requiresBuild: true
    dev: true
    optional: true

  /@rollup/rollup-linux-x64-musl@4.18.0:
    resolution: {integrity: sha512-LKaqQL9osY/ir2geuLVvRRs+utWUNilzdE90TpyoX0eNqPzWjRm14oMEE+YLve4k/NAqCdPkGYDaDF5Sw+xBfg==}
    cpu: [x64]
    os: [linux]
    libc: [musl]
    requiresBuild: true
    dev: true
    optional: true

  /@rollup/rollup-win32-arm64-msvc@4.18.0:
    resolution: {integrity: sha512-7J6TkZQFGo9qBKH0pk2cEVSRhJbL6MtfWxth7Y5YmZs57Pi+4x6c2dStAUvaQkHQLnEQv1jzBUW43GvZW8OFqA==}
    cpu: [arm64]
    os: [win32]
    requiresBuild: true
    dev: true
    optional: true

  /@rollup/rollup-win32-ia32-msvc@4.18.0:
    resolution: {integrity: sha512-Txjh+IxBPbkUB9+SXZMpv+b/vnTEtFyfWZgJ6iyCmt2tdx0OF5WhFowLmnh8ENGNpfUlUZkdI//4IEmhwPieNg==}
    cpu: [ia32]
    os: [win32]
    requiresBuild: true
    dev: true
    optional: true

  /@rollup/rollup-win32-x64-msvc@4.18.0:
    resolution: {integrity: sha512-UOo5FdvOL0+eIVTgS4tIdbW+TtnBLWg1YBCcU2KWM7nuNwRz9bksDX1bekJJCpu25N1DVWaCwnT39dVQxzqS8g==}
    cpu: [x64]
    os: [win32]
    requiresBuild: true
    dev: true
    optional: true

  /@swc/core-darwin-arm64@1.6.7:
    resolution: {integrity: sha512-sNb+ghP2OhZyUjS7E5Mf3PqSvoXJ5gY6GBaH2qp8WQxx9VL7ozC4HVo6vkeFJBN5cmYqUCLnhrM3HU4W+7yMSA==}
    engines: {node: '>=10'}
    cpu: [arm64]
    os: [darwin]
    requiresBuild: true
    dev: true
    optional: true

  /@swc/core-darwin-x64@1.6.7:
    resolution: {integrity: sha512-LQwYm/ATYN5fYSYVPMfComPiFo5i8jh75h1ASvNWhXtS+/+k1dq1zXTJWZRuojd5NXgW3bb6mJtJ2evwYIgYbA==}
    engines: {node: '>=10'}
    cpu: [x64]
    os: [darwin]
    requiresBuild: true
    dev: true
    optional: true

  /@swc/core-linux-arm-gnueabihf@1.6.7:
    resolution: {integrity: sha512-kEDzVhNci38LX3kdY99t68P2CDf+2QFDk5LawVamXH0iN5DRAO/+wjOhxL8KOHa6wQVqKEt5WrhD+Rrvk/34Yw==}
    engines: {node: '>=10'}
    cpu: [arm]
    os: [linux]
    requiresBuild: true
    dev: true
    optional: true

  /@swc/core-linux-arm64-gnu@1.6.7:
    resolution: {integrity: sha512-SyOBUGfl31xLGpIJ/Jd6GKHtkfZyHBXSwFlK7FmPN//MBQLtTBm4ZaWTnWnGo4aRsJwQdXWDKPyqlMBtnIl1nQ==}
    engines: {node: '>=10'}
    cpu: [arm64]
    os: [linux]
    libc: [glibc]
    requiresBuild: true
    dev: true
    optional: true

  /@swc/core-linux-arm64-musl@1.6.7:
    resolution: {integrity: sha512-1fOAXkDFbRfItEdMZPxT3du1QWYhgToa4YsnqTujjE8EqJW8K27hIcHRIkVuzp7PNhq8nLBg0JpJM4g27EWD7g==}
    engines: {node: '>=10'}
    cpu: [arm64]
    os: [linux]
    libc: [musl]
    requiresBuild: true
    dev: true
    optional: true

  /@swc/core-linux-x64-gnu@1.6.7:
    resolution: {integrity: sha512-Gp7uCwPsNO5ATxbyvfTyeNCHUGD9oA+xKMm43G1tWCy+l07gLqWMKp7DIr3L3qPD05TfAVo3OuiOn2abpzOFbw==}
    engines: {node: '>=10'}
    cpu: [x64]
    os: [linux]
    libc: [glibc]
    requiresBuild: true
    dev: true
    optional: true

  /@swc/core-linux-x64-musl@1.6.7:
    resolution: {integrity: sha512-QeruGBZJ15tadqEMQ77ixT/CYGk20MtlS8wmvJiV+Wsb8gPW5LgCjtupzcLLnoQzDG54JGNCeeZ0l/T8NYsOvA==}
    engines: {node: '>=10'}
    cpu: [x64]
    os: [linux]
    libc: [musl]
    requiresBuild: true
    dev: true
    optional: true

  /@swc/core-win32-arm64-msvc@1.6.7:
    resolution: {integrity: sha512-ouRqgSnT95lTCiU/6kJRNS5b1o+p8I/V9jxtL21WUj/JOVhsFmBErqQ0MZyCu514noWiR5BIqOrZXR8C1Knx6Q==}
    engines: {node: '>=10'}
    cpu: [arm64]
    os: [win32]
    requiresBuild: true
    dev: true
    optional: true

  /@swc/core-win32-ia32-msvc@1.6.7:
    resolution: {integrity: sha512-eZAP/EmJ0IcfgAx6B4/SpSjq3aT8gr0ooktfMqw/w0/5lnNrbMl2v+2kvxcneNcF7bp8VNcYZnoHlsP+LvmVbA==}
    engines: {node: '>=10'}
    cpu: [ia32]
    os: [win32]
    requiresBuild: true
    dev: true
    optional: true

  /@swc/core-win32-x64-msvc@1.6.7:
    resolution: {integrity: sha512-QOdE+7GQg1UQPS6p0KxzJOh/8GLbJ5zI1vqKArCCB0unFqUfKIjYb2TaH0geEBy3w9qtXxe3ZW6hzxtZSS9lDg==}
    engines: {node: '>=10'}
    cpu: [x64]
    os: [win32]
    requiresBuild: true
    dev: true
    optional: true

  /@swc/core@1.6.7:
    resolution: {integrity: sha512-BBzORL9qWz5hZqAZ83yn+WNaD54RH5eludjqIOboolFOK/Pw+2l00/H77H4CEBJnzCIBQszsyqtITmrn4evp0g==}
    engines: {node: '>=10'}
    requiresBuild: true
    peerDependencies:
      '@swc/helpers': '*'
    peerDependenciesMeta:
      '@swc/helpers':
        optional: true
    dependencies:
      '@swc/counter': 0.1.3
      '@swc/types': 0.1.9
    optionalDependencies:
      '@swc/core-darwin-arm64': 1.6.7
      '@swc/core-darwin-x64': 1.6.7
      '@swc/core-linux-arm-gnueabihf': 1.6.7
      '@swc/core-linux-arm64-gnu': 1.6.7
      '@swc/core-linux-arm64-musl': 1.6.7
      '@swc/core-linux-x64-gnu': 1.6.7
      '@swc/core-linux-x64-musl': 1.6.7
      '@swc/core-win32-arm64-msvc': 1.6.7
      '@swc/core-win32-ia32-msvc': 1.6.7
      '@swc/core-win32-x64-msvc': 1.6.7
    dev: true

  /@swc/counter@0.1.3:
    resolution: {integrity: sha512-e2BR4lsJkkRlKZ/qCHPw9ZaSxc0MVUd7gtbtaB7aMvHeJVYe8sOB8DBZkP2DtISHGSku9sCK6T6cnY0CtXrOCQ==}
    dev: true

  /@swc/types@0.1.9:
    resolution: {integrity: sha512-qKnCno++jzcJ4lM4NTfYifm1EFSCeIfKiAHAfkENZAV5Kl9PjJIyd2yeeVv6c/2CckuLyv2NmRC5pv6pm2WQBg==}
    dependencies:
      '@swc/counter': 0.1.3
    dev: true

  /@types/estree@1.0.5:
    resolution: {integrity: sha512-/kYRxGDLWzHOB7q+wtSUQlFrtcdUccpfy+X+9iMBpHK8QLLhx2wIPYuS5DYtR9Wa/YlZAbIovy7qVdB1Aq6Lyw==}
    dev: true

  /@types/hoist-non-react-statics@3.3.5:
    resolution: {integrity: sha512-SbcrWzkKBw2cdwRTwQAswfpB9g9LJWfjtUeW/jvNwbhC8cpmmNYVePa+ncbUe0rGTQ7G3Ff6mYUN2VMfLVr+Sg==}
    dependencies:
      '@types/react': 18.2.0
      hoist-non-react-statics: 3.3.2
    dev: false

  /@types/js-cookie@2.2.7:
    resolution: {integrity: sha512-aLkWa0C0vO5b4Sr798E26QgOkss68Un0bLjs7u9qxzPT5CG+8DuNTffWES58YzJs3hrVAOs1wonycqEBqNJubA==}
    dev: false

  /@types/prop-types@15.7.12:
    resolution: {integrity: sha512-5zvhXYtRNRluoE/jAp4GVsSduVUzNWKkOZrCDBWYtE7biZywwdC2AcEzg+cSMLFRfVgeAFqpfNabiPjxFddV1Q==}

  /@types/react-dom@18.2.0:
    resolution: {integrity: sha512-8yQrvS6sMpSwIovhPOwfyNf2Wz6v/B62LFSVYQ85+Rq3tLsBIG7rP5geMxaijTUxSkrO6RzN/IRuIAADYQsleA==}
    dependencies:
      '@types/react': 18.2.0

  /@types/react@18.2.0:
    resolution: {integrity: sha512-0FLj93y5USLHdnhIhABk83rm8XEGA7kH3cr+YUlvxoUGp1xNt/DINUMvqPxLyOQMzLmZe8i4RTHbvb8MC7NmrA==}
    dependencies:
      '@types/prop-types': 15.7.12
      '@types/scheduler': 0.23.0
      csstype: 3.1.3

  /@types/scheduler@0.23.0:
    resolution: {integrity: sha512-YIoDCTH3Af6XM5VuwGG/QL/CJqga1Zm3NkU3HZ4ZHK2fRMPYP1VczsTUqtsf43PH/iJNVlPHAo2oWX7BSdB2Hw==}

  /@types/use-sync-external-store@0.0.3:
    resolution: {integrity: sha512-EwmlvuaxPNej9+T4v5AuBPJa2x2UOJVdjCtDHgcDqitUeOtjnJKJ+apYjVcAoBEMjKW1VVFGZLUb5+qqa09XFA==}
    dev: false

  /@vitejs/plugin-react-swc@3.0.0(vite@4.2.0):
    resolution: {integrity: sha512-vYlodz/mjYRbxMGbHzDgR8aPR+z8n7K/enWkyBGH096xrL2DIPCuTvQVRYPTXGyy6wO7OFiMxZ3r4nKQD1sH0A==}
    peerDependencies:
      vite: ^4.0.0
    dependencies:
      '@swc/core': 1.6.7
      vite: 4.2.0(less@4.1.3)
    transitivePeerDependencies:
      - '@swc/helpers'
    dev: true

  /adler-32@1.3.1:
    resolution: {integrity: sha512-ynZ4w/nUUv5rrsR8UUGoe1VC9hZj6V5hU9Qw1HlMDJGEJw5S7TfTErWTjMys6M7vr0YWcPqs3qAr4ss0nDfP+A==}
    engines: {node: '>=0.8'}
    dev: false

  /ahooks-v3-count@1.0.0:
    resolution: {integrity: sha512-V7uUvAwnimu6eh/PED4mCDjE7tokeZQLKlxg9lCTMPhN+NjsSbtdacByVlR1oluXQzD3MOw55wylDmQo4+S9ZQ==}
    dev: false

  /ahooks@3.7.6(react@18.2.0):
    resolution: {integrity: sha512-p+2j4H/BI9vqXR0fZI7S/q6fUPxPklQnHqvU7zAVBljMFNSFeYRWB2iHHbjpXGOwUTOBYCh2OuvIHyJYj6Lpag==}
    engines: {node: '>=8.0.0'}
    peerDependencies:
      react: ^16.8.0 || ^17.0.0 || ^18.0.0
    dependencies:
      '@babel/runtime': 7.24.7
      '@types/js-cookie': 2.2.7
      ahooks-v3-count: 1.0.0
      dayjs: 1.11.10
      intersection-observer: 0.12.2
      js-cookie: 2.2.1
      lodash: 4.17.21
      react: 18.2.0
      resize-observer-polyfill: 1.5.1
      screenfull: 5.2.0
      tslib: 2.6.3
    dev: false

  /antd@5.3.2(moment@2.29.4)(react-dom@18.2.0)(react@18.2.0):
    resolution: {integrity: sha512-AOCZ+5krqvlLPR1v33K2PQtt9XBAC0gUdjxuHltUliE9u2zlZzGT+4DSHTNnw8CtotPV4lFKab3+K+FgdtyTlw==}
    peerDependencies:
      react: '>=16.9.0'
      react-dom: '>=16.9.0'
    dependencies:
      '@ant-design/colors': 7.1.0
      '@ant-design/cssinjs': 1.21.0(react-dom@18.2.0)(react@18.2.0)
      '@ant-design/icons': 5.2.3(react-dom@18.2.0)(react@18.2.0)
      '@ant-design/react-slick': 1.0.2(react@18.2.0)
      '@babel/runtime': 7.24.7
      '@ctrl/tinycolor': 3.6.1
      '@rc-component/mutate-observer': 1.1.0(react-dom@18.2.0)(react@18.2.0)
      '@rc-component/tour': 1.8.1(react-dom@18.2.0)(react@18.2.0)
      '@rc-component/trigger': 1.18.3(react-dom@18.2.0)(react@18.2.0)
      classnames: 2.5.1
      copy-to-clipboard: 3.3.3
      dayjs: 1.11.10
      qrcode.react: 3.1.0(react@18.2.0)
      rc-cascader: 3.9.1(react-dom@18.2.0)(react@18.2.0)
      rc-checkbox: 2.3.2(react-dom@18.2.0)(react@18.2.0)
      rc-collapse: 3.5.2(react-dom@18.2.0)(react@18.2.0)
      rc-dialog: 9.0.2(react-dom@18.2.0)(react@18.2.0)
      rc-drawer: 6.1.6(react-dom@18.2.0)(react@18.2.0)
      rc-dropdown: 4.0.1(react-dom@18.2.0)(react@18.2.0)
      rc-field-form: 1.28.0(react-dom@18.2.0)(react@18.2.0)
      rc-image: 5.15.2(react-dom@18.2.0)(react@18.2.0)
      rc-input: 0.2.2(react-dom@18.2.0)(react@18.2.0)
      rc-input-number: 7.4.2(react-dom@18.2.0)(react@18.2.0)
      rc-mentions: 2.1.0(react-dom@18.2.0)(react@18.2.0)
      rc-menu: 9.8.4(react-dom@18.2.0)(react@18.2.0)
      rc-motion: 2.9.2(react-dom@18.2.0)(react@18.2.0)
      rc-notification: 5.0.5(react-dom@18.2.0)(react@18.2.0)
      rc-pagination: 3.3.1(react-dom@18.2.0)(react@18.2.0)
      rc-picker: 3.3.4(dayjs@1.11.10)(moment@2.29.4)(react-dom@18.2.0)(react@18.2.0)
      rc-progress: 3.4.2(react-dom@18.2.0)(react@18.2.0)
      rc-rate: 2.10.0(react-dom@18.2.0)(react@18.2.0)
      rc-resize-observer: 1.4.0(react-dom@18.2.0)(react@18.2.0)
      rc-segmented: 2.1.2(react-dom@18.2.0)(react@18.2.0)
      rc-select: 14.3.0(react-dom@18.2.0)(react@18.2.0)
      rc-slider: 10.1.1(react-dom@18.2.0)(react@18.2.0)
      rc-steps: 6.0.1(react-dom@18.2.0)(react@18.2.0)
      rc-switch: 4.0.0(react-dom@18.2.0)(react@18.2.0)
      rc-table: 7.31.1(react-dom@18.2.0)(react@18.2.0)
      rc-tabs: 12.5.10(react-dom@18.2.0)(react@18.2.0)
      rc-textarea: 1.1.0(react-dom@18.2.0)(react@18.2.0)
      rc-tooltip: 6.0.1(react-dom@18.2.0)(react@18.2.0)
      rc-tree: 5.7.12(react-dom@18.2.0)(react@18.2.0)
      rc-tree-select: 5.7.0(react-dom@18.2.0)(react@18.2.0)
      rc-trigger: 5.3.4(react-dom@18.2.0)(react@18.2.0)
      rc-upload: 4.3.6(react-dom@18.2.0)(react@18.2.0)
      rc-util: 5.43.0(react-dom@18.2.0)(react@18.2.0)
      react: 18.2.0
      react-dom: 18.2.0(react@18.2.0)
      scroll-into-view-if-needed: 3.1.0
      throttle-debounce: 5.0.2
    transitivePeerDependencies:
      - date-fns
      - moment
    dev: false

  /array-tree-filter@2.1.0:
    resolution: {integrity: sha512-4ROwICNlNw/Hqa9v+rk5h22KjmzB1JGTMVKP2AKJBOCgb0yL0ASf0+YvCcLNNwquOHNX48jkeZIJ3a+oOQqKcw==}
    dev: false

  /async-validator@4.2.5:
    resolution: {integrity: sha512-7HhHjtERjqlNbZtqNqy2rckN/SpOOlmDliet+lP7k+eKZEjPk3DgyeU9lIXLdeLz0uBbbVp+9Qdow9wJWgwwfg==}
    dev: false

  /asynckit@0.4.0:
    resolution: {integrity: sha512-Oei9OH4tRh0YqU3GxhX79dM/mwVgvbZJaSNaRk+bshkj0S5cfHcgYakreBjrHwatXKbz+IoIdYLxrKim2MjW0Q==}
    dev: false

  /axios@1.3.4:
    resolution: {integrity: sha512-toYm+Bsyl6VC5wSkfkbbNB6ROv7KY93PEBBL6xyDczaIHasAiv4wPqQ/c4RjoQzipxRD2W5g21cOqQulZ7rHwQ==}
    dependencies:
      follow-redirects: 1.15.6
      form-data: 4.0.0
      proxy-from-env: 1.1.0
    transitivePeerDependencies:
      - debug
    dev: false

  /cfb@1.2.2:
    resolution: {integrity: sha512-KfdUZsSOw19/ObEWasvBP/Ac4reZvAGauZhs6S/gqNhXhI7cKwvlH7ulj+dOEYnca4bm4SGo8C1bTAQvnTjgQA==}
    engines: {node: '>=0.8'}
    dependencies:
      adler-32: 1.3.1
      crc-32: 1.2.2
    dev: false

  /classnames@2.5.1:
    resolution: {integrity: sha512-saHYOzhIQs6wy2sVxTM6bUDsQO4F50V9RQ22qBpEdCW+I+/Wmke2HOl6lS6dTpdxVhb88/I6+Hs+438c3lfUow==}
    dev: false

  /codepage@1.15.0:
    resolution: {integrity: sha512-3g6NUTPd/YtuuGrhMnOMRjFc+LJw/bnMp3+0r/Wcz3IXUuCosKRJvMphm5+Q+bvTVGcJJuRvVLuYba+WojaFaA==}
    engines: {node: '>=0.8'}
    dev: false

  /combined-stream@1.0.8:
    resolution: {integrity: sha512-FQN4MRfuJeHf7cBbBMJFXhKSDq+2kAArBlmRBvcvFE5BB1HZKXtSFASDhdlz9zOYwxh8lDdnvmMOe/+5cdoEdg==}
    engines: {node: '>= 0.8'}
    dependencies:
      delayed-stream: 1.0.0
    dev: false

  /compute-scroll-into-view@3.1.0:
    resolution: {integrity: sha512-rj8l8pD4bJ1nx+dAkMhV1xB5RuZEyVysfxJqB1pRchh1KVvwOv9b7CGB8ZfjTImVv2oF+sYMUkMZq6Na5Ftmbg==}
    dev: false

  /copy-anything@2.0.6:
    resolution: {integrity: sha512-1j20GZTsvKNkc4BY3NpMOM8tt///wY3FpIzozTOFO2ffuZcV61nojHXVKIy3WM+7ADCy5FVhdZYHYDdgTU0yJw==}
    dependencies:
      is-what: 3.14.1
    dev: true

  /copy-to-clipboard@3.3.3:
    resolution: {integrity: sha512-2KV8NhB5JqC3ky0r9PMCAZKbUHSwtEo4CwCs0KXgruG43gX5PMqDEBbVU4OUzw2MuAWUfsuFmWvEKG5QRfSnJA==}
    dependencies:
      toggle-selection: 1.0.6
    dev: false

  /crc-32@1.2.2:
    resolution: {integrity: sha512-ROmzCKrTnOwybPcJApAA6WBWij23HVfGVNKqqrZpuyZOHqK2CwHSvpGuyt/UNNvaIjEd8X5IFGp4Mh+Ie1IHJQ==}
    engines: {node: '>=0.8'}
    hasBin: true
    dev: false

  /csstype@3.1.3:
    resolution: {integrity: sha512-M1uQkMl8rQK/szD0LNhtqxIPLpimGm8sOBwU7lLnCpSbTyY3yeU1Vc7l4KT5zT4s/yOxHH5O7tIuuLOCnLADRw==}

  /dayjs@1.11.10:
    resolution: {integrity: sha512-vjAczensTgRcqDERK0SR2XMwsF/tSvnvlv6VcF2GIhg6Sx4yOIt/irsr1RDJsKiIyBzJDpCoXiWWq28MqH2cnQ==}
    dev: false

  /delayed-stream@1.0.0:
    resolution: {integrity: sha512-ZySD7Nf91aLB0RxL4KGrKHBXl7Eds1DAmEdcoVawXnLD7SDhpNgtuII2aAkg7a7QS41jxPSZ17p4VdGnMHk3MQ==}
    engines: {node: '>=0.4.0'}
    dev: false

  /dom-align@1.12.4:
    resolution: {integrity: sha512-R8LUSEay/68zE5c8/3BDxiTEvgb4xZTF0RKmAHfiEVN3klfIpXfi2/QCoiWPccVQ0J/ZGdz9OjzL4uJEP/MRAw==}
    dev: false

  /echarts@5.4.2:
    resolution: {integrity: sha512-2W3vw3oI2tWJdyAz+b8DuWS0nfXtSDqlDmqgin/lfzbkB01cuMEN66KWBlmur3YMp5nEDEEt5s23pllnAzB4EA==}
    dependencies:
      tslib: 2.3.0
      zrender: 5.4.3
    dev: false

  /errno@0.1.8:
    resolution: {integrity: sha512-dJ6oBr5SQ1VSd9qkk7ByRgb/1SH4JZjCHSW/mr63/QcXO9zLVxvJ6Oy13nio03rxpSnVDDjFor75SjVeZWPW/A==}
    hasBin: true
    requiresBuild: true
    dependencies:
      prr: 1.0.1
    dev: true
    optional: true

  /esbuild@0.17.19:
    resolution: {integrity: sha512-XQ0jAPFkK/u3LcVRcvVHQcTIqD6E2H1fvZMA5dQPSOWb3suUbWbfbRf94pjc0bNzRYLfIrDRQXr7X+LHIm5oHw==}
    engines: {node: '>=12'}
    hasBin: true
    requiresBuild: true
    optionalDependencies:
      '@esbuild/android-arm': 0.17.19
      '@esbuild/android-arm64': 0.17.19
      '@esbuild/android-x64': 0.17.19
      '@esbuild/darwin-arm64': 0.17.19
      '@esbuild/darwin-x64': 0.17.19
      '@esbuild/freebsd-arm64': 0.17.19
      '@esbuild/freebsd-x64': 0.17.19
      '@esbuild/linux-arm': 0.17.19
      '@esbuild/linux-arm64': 0.17.19
      '@esbuild/linux-ia32': 0.17.19
      '@esbuild/linux-loong64': 0.17.19
      '@esbuild/linux-mips64el': 0.17.19
      '@esbuild/linux-ppc64': 0.17.19
      '@esbuild/linux-riscv64': 0.17.19
      '@esbuild/linux-s390x': 0.17.19
      '@esbuild/linux-x64': 0.17.19
      '@esbuild/netbsd-x64': 0.17.19
      '@esbuild/openbsd-x64': 0.17.19
      '@esbuild/sunos-x64': 0.17.19
      '@esbuild/win32-arm64': 0.17.19
      '@esbuild/win32-ia32': 0.17.19
      '@esbuild/win32-x64': 0.17.19
    dev: true

  /follow-redirects@1.15.6:
    resolution: {integrity: sha512-wWN62YITEaOpSK584EZXJafH1AGpO8RVgElfkuXbTOrPX4fIfOyEpW/CsiNd8JdYrAoOvafRTOEnvsO++qCqFA==}
    engines: {node: '>=4.0'}
    peerDependencies:
      debug: '*'
    peerDependenciesMeta:
      debug:
        optional: true
    dev: false

  /form-data@4.0.0:
    resolution: {integrity: sha512-ETEklSGi5t0QMZuiXoA/Q6vcnxcLQP5vdugSpuAyi6SVGi2clPPp+xgEhuMaHC+zGgn31Kd235W35f7Hykkaww==}
    engines: {node: '>= 6'}
    dependencies:
      asynckit: 0.4.0
      combined-stream: 1.0.8
      mime-types: 2.1.35
    dev: false

  /frac@1.1.2:
    resolution: {integrity: sha512-w/XBfkibaTl3YDqASwfDUqkna4Z2p9cFSr1aHDt0WoMTECnRfBOv2WArlZILlqgWlmdIlALXGpM2AOhEk5W3IA==}
    engines: {node: '>=0.8'}
    dev: false

  /fsevents@2.3.3:
    resolution: {integrity: sha512-5xoDfX+fL7faATnagmWPpbFtwh/R77WmMMqqHGS65C3vvB0YHrgF+B1YmZ3441tMj5n63k0212XNoJwzlhffQw==}
    engines: {node: ^8.16.0 || ^10.6.0 || >=11.0.0}
    os: [darwin]
    requiresBuild: true
    dev: true
    optional: true

  /function-bind@1.1.2:
    resolution: {integrity: sha512-7XHNxH7qX9xG5mIwxkhumTox/MIRNcOgDrxWsMt2pAr23WHp6MrRlN7FBSFpCpr+oVO0F744iUgR82nJMfG2SA==}
    dev: true

  /graceful-fs@4.2.11:
    resolution: {integrity: sha512-RbJ5/jmFcNNCcDV5o9eTnBLJ/HszWV0P73bc+Ff4nS/rJj+YaS6IGyiOL0VoBYX+l1Wrl3k63h/KrH+nhJ0XvQ==}
    requiresBuild: true
    dev: true
    optional: true

  /hasown@2.0.2:
    resolution: {integrity: sha512-0hJU9SCPvmMzIBdZFqNPXWa6dqh7WdH0cII9y+CyS8rG3nL48Bclra9HmKhVVUHyPWNH5Y7xDwAB7bfgSjkUMQ==}
    engines: {node: '>= 0.4'}
    dependencies:
      function-bind: 1.1.2
    dev: true

  /hoist-non-react-statics@3.3.2:
    resolution: {integrity: sha512-/gGivxi8JPKWNm/W0jSmzcMPpfpPLc3dY/6GxhX2hQ9iGj3aDfklV4ET7NjKpSinLpJ5vafa9iiGIEZg10SfBw==}
    dependencies:
      react-is: 16.13.1
    dev: false

  /iconv-lite@0.6.3:
    resolution: {integrity: sha512-4fCk79wshMdzMp2rH06qWrJE4iolqLhCUH+OiuIgU++RB0+94NlDL81atO7GX55uUKueo0txHNtvEyI6D7WdMw==}
    engines: {node: '>=0.10.0'}
    requiresBuild: true
    dependencies:
      safer-buffer: 2.1.2
    dev: true
    optional: true

  /image-size@0.5.5:
    resolution: {integrity: sha512-6TDAlDPZxUFCv+fuOkIoXT/V/f3Qbq8e37p+YOiYrUv3v9cc3/6x78VdfPgFVaB9dZYeLUfKgHRebpkm/oP2VQ==}
    engines: {node: '>=0.10.0'}
    hasBin: true
    requiresBuild: true
    dev: true
    optional: true

  /immediate@3.0.6:
    resolution: {integrity: sha512-XXOFtyqDjNDAQxVfYxuF7g9Il/IbWmmlQg2MYKOH8ExIT1qg6xc4zyS3HaEEATgs1btfzxq15ciUiY7gjSXRGQ==}
    dev: false

  /immer@9.0.21:
    resolution: {integrity: sha512-bc4NBHqOqSfRW7POMkHd51LvClaeMXpm8dx0e8oE2GORbq5aRK7Bxl4FyzVLdGtLmvLKL7BTDBG5ACQm4HWjTA==}
    dev: false

  /intersection-observer@0.12.2:
    resolution: {integrity: sha512-7m1vEcPCxXYI8HqnL8CKI6siDyD+eIWSwgB3DZA+ZTogxk9I4CDnj4wilt9x/+/QbHI4YG5YZNmC6458/e9Ktg==}
    dev: false

  /is-core-module@2.14.0:
    resolution: {integrity: sha512-a5dFJih5ZLYlRtDc0dZWP7RiKr6xIKzmn/oAYCDvdLThadVgyJwlaoQPmRtMSpz+rk0OGAgIu+TcM9HUF0fk1A==}
    engines: {node: '>= 0.4'}
    dependencies:
      hasown: 2.0.2
    dev: true

  /is-what@3.14.1:
    resolution: {integrity: sha512-sNxgpk9793nzSs7bA6JQJGeIuRBQhAaNGG77kzYQgMkrID+lS6SlK07K5LaptscDlSaIgH+GPFzf+d75FVxozA==}
    dev: true

  /js-cookie@2.2.1:
    resolution: {integrity: sha512-HvdH2LzI/EAZcUwA8+0nKNtWHqS+ZmijLA30RwZA0bo7ToCckjK5MkGhjED9KoRcXO6BaGI3I9UIzSA1FKFPOQ==}
    dev: false

  /js-tokens@4.0.0:
    resolution: {integrity: sha512-RdJUflcE3cUzKiMqQgsCu06FPu9UdIJO0beYbPhHN4k6apgJtifcoCtT9bcxOpYBtpD2kCM6Sbzg4CausW/PKQ==}
    dev: false

  /json2mq@0.2.0:
    resolution: {integrity: sha512-SzoRg7ux5DWTII9J2qkrZrqV1gt+rTaoufMxEzXbS26Uid0NwaJd123HcoB80TgubEppxxIGdNxCx50fEoEWQA==}
    dependencies:
      string-convert: 0.2.1
    dev: false

  /less@4.1.3:
    resolution: {integrity: sha512-w16Xk/Ta9Hhyei0Gpz9m7VS8F28nieJaL/VyShID7cYvP6IL5oHeL6p4TXSDJqZE/lNv0oJ2pGVjJsRkfwm5FA==}
    engines: {node: '>=6'}
    hasBin: true
    dependencies:
      copy-anything: 2.0.6
      parse-node-version: 1.0.1
      tslib: 2.6.3
    optionalDependencies:
      errno: 0.1.8
      graceful-fs: 4.2.11
      image-size: 0.5.5
      make-dir: 2.1.0
      mime: 1.6.0
      needle: 3.3.1
      source-map: 0.6.1
    dev: true

  /lie@3.1.1:
    resolution: {integrity: sha512-RiNhHysUjhrDQntfYSfY4MU24coXXdEOgw9WGcKHNeEwffDYbF//u87M1EWaMGzuFoSbqW0C9C6lEEhDOAswfw==}
    dependencies:
      immediate: 3.0.6
    dev: false

  /localforage@1.10.0:
    resolution: {integrity: sha512-14/H1aX7hzBBmmh7sGPd+AOMkkIrHM3Z1PAyGgZigA1H1p5O5ANnMyWzvpAETtG68/dC4pC0ncy3+PPGzXZHPg==}
    dependencies:
      lie: 3.1.1
    dev: false

  /lodash.camelcase@4.3.0:
    resolution: {integrity: sha512-TwuEnCnxbc3rAvhf/LbG7tJUDzhqXyFnv3dtzLOPgCG/hODL7WFnsbwktkD7yUV0RrreP/l1PALq/YSg6VvjlA==}
    dev: false

  /lodash@4.17.21:
    resolution: {integrity: sha512-v2kDEe57lecTulaDIuNTPy3Ry4gLGJ6Z1O3vE1krgXZNrsQ+LFTGHVxVjcXPs17LhbZVGedAJv8XZ1tvj5FvSg==}
    dev: false

  /loose-envify@1.4.0:
    resolution: {integrity: sha512-lyuxPGr/Wfhrlem2CL/UcnUc1zcqKAImBDzukY7Y5F/yQiNdko6+fRLevlw1HgMySw7f611UIY408EtxRSoK3Q==}
    hasBin: true
    dependencies:
      js-tokens: 4.0.0
    dev: false

  /make-dir@2.1.0:
    resolution: {integrity: sha512-LS9X+dc8KLxXCb8dni79fLIIUA5VyZoyjSMCwTluaXA0o27cCK0bhXkpgw+sTXVpPy/lSO57ilRixqk0vDmtRA==}
    engines: {node: '>=6'}
    requiresBuild: true
    dependencies:
      pify: 4.0.1
      semver: 5.7.2
    dev: true
    optional: true

  /match-sorter@6.3.1:
    resolution: {integrity: sha512-mxybbo3pPNuA+ZuCUhm5bwNkXrJTbsk5VWbR5wiwz/GC6LIiegBGn2w3O08UG/jdbYLinw51fSQ5xNU1U3MgBw==}
    dependencies:
      '@babel/runtime': 7.24.7
      remove-accents: 0.4.2
    dev: false

  /mime-db@1.52.0:
    resolution: {integrity: sha512-sPU4uV7dYlvtWJxwwxHD0PuihVNiE7TyAbQ5SWxDCB9mUYvOgroQOwYQQOKPJ8CIbE+1ETVlOoK1UC2nU3gYvg==}
    engines: {node: '>= 0.6'}
    dev: false

  /mime-types@2.1.35:
    resolution: {integrity: sha512-ZDY+bPm5zTTF+YpCrAU9nK0UgICYPT0QtT1NZWFv4s++TNkcgVaT0g6+4R2uI4MjQjzysHB1zxuWL50hzaeXiw==}
    engines: {node: '>= 0.6'}
    dependencies:
      mime-db: 1.52.0
    dev: false

  /mime@1.6.0:
    resolution: {integrity: sha512-x0Vn8spI+wuJ1O6S7gnbaQg8Pxh4NNHb7KSINmEWKiPE4RKOplvijn+NkmYmmRgP68mc70j2EbeTFRsrswaQeg==}
    engines: {node: '>=4'}
    hasBin: true
    requiresBuild: true
    dev: true
    optional: true

  /moment@2.29.4:
    resolution: {integrity: sha512-5LC9SOxjSc2HF6vO2CyuTDNivEdoz2IvyJJGj6X8DJ0eFyfszE0QiEd+iXmBvUP3WHxSjFH/vIsA0EN00cgr8w==}
    dev: false

  /nanoid@3.3.7:
    resolution: {integrity: sha512-eSRppjcPIatRIMC1U6UngP8XFcz8MQWGQdt1MTBQ7NaAmvXDfvNxbvWV3x2y6CdEUciCSsDHDQZbhYaB8QEo2g==}
    engines: {node: ^10 || ^12 || ^13.7 || ^14 || >=15.0.1}
    hasBin: true
    dev: true

  /needle@3.3.1:
    resolution: {integrity: sha512-6k0YULvhpw+RoLNiQCRKOl09Rv1dPLr8hHnVjHqdolKwDrdNyk+Hmrthi4lIGPPz3r39dLx0hsF5s40sZ3Us4Q==}
    engines: {node: '>= 4.4.x'}
    hasBin: true
    requiresBuild: true
    dependencies:
      iconv-lite: 0.6.3
      sax: 1.4.1
    dev: true
    optional: true

  /object-path@0.6.0:
    resolution: {integrity: sha512-fxrwsCFi3/p+LeLOAwo/wyRMODZxdGBtUlWRzsEpsUVrisZbEfZ21arxLGfaWfcnqb8oHPNihIb4XPE8CQPN5A==}
    engines: {node: '>=0.8.0'}
    dev: false

  /parse-node-version@1.0.1:
    resolution: {integrity: sha512-3YHlOa/JgH6Mnpr05jP9eDG254US9ek25LyIxZlDItp2iJtwyaXQb57lBYLdT3MowkUFYEV2XXNAYIPlESvJlA==}
    engines: {node: '>= 0.10'}
    dev: true

  /path-parse@1.0.7:
    resolution: {integrity: sha512-LDJzPVEEEPR+y48z93A0Ed0yXb8pAByGWo/k5YYdYgpY2/2EsOsksJrq7lOHxryrVOn1ejG6oAp8ahvOIQD8sw==}
    dev: true

  /picocolors@1.0.1:
    resolution: {integrity: sha512-anP1Z8qwhkbmu7MFP5iTt+wQKXgwzf7zTyGlcdzabySa9vd0Xt392U0rVmz9poOaBj0uHJKyyo9/upk0HrEQew==}
    dev: true

  /pify@4.0.1:
    resolution: {integrity: sha512-uB80kBFb/tfd68bVleG9T5GGsGPjJrLAUpR5PZIrhBnIaRTQRjqdJSsIKkOP6OAIFbj7GOrcudc5pNjZ+geV2g==}
    engines: {node: '>=6'}
    requiresBuild: true
    dev: true
    optional: true

  /postcss@8.4.39:
    resolution: {integrity: sha512-0vzE+lAiG7hZl1/9I8yzKLx3aR9Xbof3fBHKunvMfOCYAtMhrsnccJY2iTURb9EZd5+pLuiNV9/c/GZJOHsgIw==}
    engines: {node: ^10 || ^12 || >=14}
    dependencies:
      nanoid: 3.3.7
      picocolors: 1.0.1
      source-map-js: 1.2.0
    dev: true

  /proxy-from-env@1.1.0:
    resolution: {integrity: sha512-D+zkORCbA9f1tdWRK0RaCR3GPv50cMxcrz4X8k5LTSUD1Dkw47mKJEZQNunItRTkWwgtaUSo1RVFRIG9ZXiFYg==}
    dev: false

  /prr@1.0.1:
    resolution: {integrity: sha512-yPw4Sng1gWghHQWj0B3ZggWUm4qVbPwPFcRG8KyxiU7J2OHFSoEHKS+EZ3fv5l1t9CyCiop6l/ZYeWbrgoQejw==}
    requiresBuild: true
    dev: true
    optional: true

  /qrcode.react@3.1.0(react@18.2.0):
    resolution: {integrity: sha512-oyF+Urr3oAMUG/OiOuONL3HXM+53wvuH3mtIWQrYmsXoAq0DkvZp2RYUWFSMFtbdOpuS++9v+WAkzNVkMlNW6Q==}
    peerDependencies:
      react: ^16.8.0 || ^17.0.0 || ^18.0.0
    dependencies:
      react: 18.2.0
    dev: false

  /rc-align@4.0.15(react-dom@18.2.0)(react@18.2.0):
    resolution: {integrity: sha512-wqJtVH60pka/nOX7/IspElA8gjPNQKIx/ZqJ6heATCkXpe1Zg4cPVrMD2vC96wjsFFL8WsmhPbx9tdMo1qqlIA==}
    peerDependencies:
      react: '>=16.9.0'
      react-dom: '>=16.9.0'
    dependencies:
      '@babel/runtime': 7.24.7
      classnames: 2.5.1
      dom-align: 1.12.4
      rc-util: 5.43.0(react-dom@18.2.0)(react@18.2.0)
      react: 18.2.0
      react-dom: 18.2.0(react@18.2.0)
      resize-observer-polyfill: 1.5.1
    dev: false

  /rc-cascader@3.9.1(react-dom@18.2.0)(react@18.2.0):
    resolution: {integrity: sha512-wMOQrCWobkaQz3zHEFB82RNBLDkL6tDucOf4VSKQ8Z6SiVBqIfqnEHaEiYvL9GMI3/zStVGqwvOFar/xyOm+xw==}
    peerDependencies:
      react: '>=16.9.0'
      react-dom: '>=16.9.0'
    dependencies:
      '@babel/runtime': 7.24.7
      array-tree-filter: 2.1.0
      classnames: 2.5.1
      rc-select: 14.3.0(react-dom@18.2.0)(react@18.2.0)
      rc-tree: 5.7.12(react-dom@18.2.0)(react@18.2.0)
      rc-util: 5.43.0(react-dom@18.2.0)(react@18.2.0)
      react: 18.2.0
      react-dom: 18.2.0(react@18.2.0)
    dev: false

  /rc-checkbox@2.3.2(react-dom@18.2.0)(react@18.2.0):
    resolution: {integrity: sha512-afVi1FYiGv1U0JlpNH/UaEXdh6WUJjcWokj/nUN2TgG80bfG+MDdbfHKlLcNNba94mbjy2/SXJ1HDgrOkXGAjg==}
    peerDependencies:
      react: '>=16.9.0'
      react-dom: '>=16.9.0'
    dependencies:
      '@babel/runtime': 7.24.7
      classnames: 2.5.1
      react: 18.2.0
      react-dom: 18.2.0(react@18.2.0)
    dev: false

  /rc-collapse@3.5.2(react-dom@18.2.0)(react@18.2.0):
    resolution: {integrity: sha512-/TNiT3DW1t3sUCiVD/DPUYooJZ3BLA93/2rZsB3eM2bGJCCla2X9D2E4tgm7LGMQGy5Atb2lMUn2FQuvQNvavQ==}
    peerDependencies:
      react: '>=16.9.0'
      react-dom: '>=16.9.0'
    dependencies:
      '@babel/runtime': 7.24.7
      classnames: 2.5.1
      rc-motion: 2.9.2(react-dom@18.2.0)(react@18.2.0)
      rc-util: 5.43.0(react-dom@18.2.0)(react@18.2.0)
      react: 18.2.0
      react-dom: 18.2.0(react@18.2.0)
    dev: false

  /rc-dialog@9.0.2(react-dom@18.2.0)(react@18.2.0):
    resolution: {integrity: sha512-s3U+24xWUuB6Bn2Lk/Qt6rufy+uT+QvWkiFhNBcO9APLxcFFczWamaq7x9h8SCuhfc1nHcW4y8NbMsnAjNnWyg==}
    peerDependencies:
      react: '>=16.9.0'
      react-dom: '>=16.9.0'
    dependencies:
      '@babel/runtime': 7.24.7
      '@rc-component/portal': 1.1.2(react-dom@18.2.0)(react@18.2.0)
      classnames: 2.5.1
      rc-motion: 2.9.2(react-dom@18.2.0)(react@18.2.0)
      rc-util: 5.43.0(react-dom@18.2.0)(react@18.2.0)
      react: 18.2.0
      react-dom: 18.2.0(react@18.2.0)
    dev: false

  /rc-drawer@6.1.6(react-dom@18.2.0)(react@18.2.0):
    resolution: {integrity: sha512-EBRFM9o3lPU5kYh8sFoXYA9KxpdT765HDqj/AbZWicXkhwEYUH7MjUH0ctenPCiHBxXQUgIUvK14+6rPuURd6w==}
    peerDependencies:
      react: '>=16.9.0'
      react-dom: '>=16.9.0'
    dependencies:
      '@babel/runtime': 7.24.7
      '@rc-component/portal': 1.1.2(react-dom@18.2.0)(react@18.2.0)
      classnames: 2.5.1
      rc-motion: 2.9.2(react-dom@18.2.0)(react@18.2.0)
      rc-util: 5.43.0(react-dom@18.2.0)(react@18.2.0)
      react: 18.2.0
      react-dom: 18.2.0(react@18.2.0)
    dev: false

  /rc-dropdown@4.0.1(react-dom@18.2.0)(react@18.2.0):
    resolution: {integrity: sha512-OdpXuOcme1rm45cR0Jzgfl1otzmU4vuBVb+etXM8vcaULGokAKVpKlw8p6xzspG7jGd/XxShvq+N3VNEfk/l5g==}
    peerDependencies:
      react: '>=16.11.0'
      react-dom: '>=16.11.0'
    dependencies:
      '@babel/runtime': 7.24.7
      classnames: 2.5.1
      rc-trigger: 5.3.4(react-dom@18.2.0)(react@18.2.0)
      rc-util: 5.43.0(react-dom@18.2.0)(react@18.2.0)
      react: 18.2.0
      react-dom: 18.2.0(react@18.2.0)
    dev: false

  /rc-field-form@1.28.0(react-dom@18.2.0)(react@18.2.0):
    resolution: {integrity: sha512-k7VfxFiQQkaJV7G5TMfZfeN905WGwd6CQXsblH+s8g9wcOn7gIacjl2N7Xr4EPDFR4dAECtxcbd0guwIT01vjw==}
    engines: {node: '>=8.x'}
    peerDependencies:
      react: '>=16.9.0'
      react-dom: '>=16.9.0'
    dependencies:
      '@babel/runtime': 7.24.7
      async-validator: 4.2.5
      rc-util: 5.43.0(react-dom@18.2.0)(react@18.2.0)
      react: 18.2.0
      react-dom: 18.2.0(react@18.2.0)
    dev: false

  /rc-image@5.15.2(react-dom@18.2.0)(react@18.2.0):
    resolution: {integrity: sha512-QaeWP20v51eGyrkl24PyusTmbMk42A3vGPl7hEa15jcQjECOX36tLtvLk5sjl3vaLQpMskB8BbwiqPsN7I7aow==}
    peerDependencies:
      react: '>=16.9.0'
      react-dom: '>=16.9.0'
    dependencies:
      '@babel/runtime': 7.24.7
      '@rc-component/portal': 1.1.2(react-dom@18.2.0)(react@18.2.0)
      classnames: 2.5.1
      rc-dialog: 9.0.2(react-dom@18.2.0)(react@18.2.0)
      rc-motion: 2.9.2(react-dom@18.2.0)(react@18.2.0)
      rc-util: 5.43.0(react-dom@18.2.0)(react@18.2.0)
      react: 18.2.0
      react-dom: 18.2.0(react@18.2.0)
    dev: false

  /rc-input-number@7.4.2(react-dom@18.2.0)(react@18.2.0):
    resolution: {integrity: sha512-yGturTw7WGP+M1GbJ+UTAO7L4buxeW6oilhL9Sq3DezsRS8/9qec4UiXUbeoiX9bzvRXH11JvgskBtxSp4YSNg==}
    peerDependencies:
      react: '>=16.9.0'
      react-dom: '>=16.9.0'
    dependencies:
      '@babel/runtime': 7.24.7
      '@rc-component/mini-decimal': 1.1.0
      classnames: 2.5.1
      rc-util: 5.43.0(react-dom@18.2.0)(react@18.2.0)
      react: 18.2.0
      react-dom: 18.2.0(react@18.2.0)
    dev: false

  /rc-input@0.2.2(react-dom@18.2.0)(react@18.2.0):
    resolution: {integrity: sha512-xgkVcFgtRO0Hl9hmvslZhObNyxbSpTmy3nR1Tk4XrjjZ9lFJ7GcJBy6ss30Pdb0oX36cHzLN8I7VCjBGeRNB9A==}
    peerDependencies:
      react: '>=16.0.0'
      react-dom: '>=16.0.0'
    dependencies:
      '@babel/runtime': 7.24.7
      classnames: 2.5.1
      rc-util: 5.43.0(react-dom@18.2.0)(react@18.2.0)
      react: 18.2.0
      react-dom: 18.2.0(react@18.2.0)
    dev: false

  /rc-mentions@2.1.0(react-dom@18.2.0)(react@18.2.0):
    resolution: {integrity: sha512-yXfq/CcEbrbcVcbhjRSzVNjITfMwCO6KspmLIviAsxxMDHT/VYy9F0EgWNEuwUrDqJkN7c0foVJngR2dBvhMPw==}
    peerDependencies:
      react: '>=16.9.0'
      react-dom: '>=16.9.0'
    dependencies:
      '@babel/runtime': 7.24.7
      '@rc-component/trigger': 1.18.3(react-dom@18.2.0)(react@18.2.0)
      classnames: 2.5.1
      rc-input: 0.2.2(react-dom@18.2.0)(react@18.2.0)
      rc-menu: 9.8.4(react-dom@18.2.0)(react@18.2.0)
      rc-textarea: 1.1.0(react-dom@18.2.0)(react@18.2.0)
      rc-util: 5.43.0(react-dom@18.2.0)(react@18.2.0)
      react: 18.2.0
      react-dom: 18.2.0(react@18.2.0)
    dev: false

  /rc-menu@9.8.4(react-dom@18.2.0)(react@18.2.0):
    resolution: {integrity: sha512-lmw2j8I2fhdIzHmC9ajfImfckt0WDb2KVJJBBRIsxPEw2kGkEfjLMUoB1NgiNT/Q5cC8PdjGOGQjHJIJMwyNMw==}
    peerDependencies:
      react: '>=16.9.0'
      react-dom: '>=16.9.0'
    dependencies:
      '@babel/runtime': 7.24.7
      classnames: 2.5.1
      rc-motion: 2.9.2(react-dom@18.2.0)(react@18.2.0)
      rc-overflow: 1.3.2(react-dom@18.2.0)(react@18.2.0)
      rc-trigger: 5.3.4(react-dom@18.2.0)(react@18.2.0)
      rc-util: 5.43.0(react-dom@18.2.0)(react@18.2.0)
      react: 18.2.0
      react-dom: 18.2.0(react@18.2.0)
    dev: false

  /rc-motion@2.9.2(react-dom@18.2.0)(react@18.2.0):
    resolution: {integrity: sha512-fUAhHKLDdkAXIDLH0GYwof3raS58dtNUmzLF2MeiR8o6n4thNpSDQhOqQzWE4WfFZDCi9VEN8n7tiB7czREcyw==}
    peerDependencies:
      react: '>=16.9.0'
      react-dom: '>=16.9.0'
    dependencies:
      '@babel/runtime': 7.24.7
      classnames: 2.5.1
      rc-util: 5.43.0(react-dom@18.2.0)(react@18.2.0)
      react: 18.2.0
      react-dom: 18.2.0(react@18.2.0)
    dev: false

  /rc-notification@5.0.5(react-dom@18.2.0)(react@18.2.0):
    resolution: {integrity: sha512-uEz2jggourwv/rR0obe7RHEa63UchqX4k+e+Qt2c3LaY7U9Tc+L6ANhzgCKYSA/afm0ebjmNZHoB5Cv47xEOcA==}
    engines: {node: '>=8.x'}
    peerDependencies:
      react: '>=16.9.0'
      react-dom: '>=16.9.0'
    dependencies:
      '@babel/runtime': 7.24.7
      classnames: 2.5.1
      rc-motion: 2.9.2(react-dom@18.2.0)(react@18.2.0)
      rc-util: 5.43.0(react-dom@18.2.0)(react@18.2.0)
      react: 18.2.0
      react-dom: 18.2.0(react@18.2.0)
    dev: false

  /rc-overflow@1.3.2(react-dom@18.2.0)(react@18.2.0):
    resolution: {integrity: sha512-nsUm78jkYAoPygDAcGZeC2VwIg/IBGSodtOY3pMof4W3M9qRJgqaDYm03ZayHlde3I6ipliAxbN0RUcGf5KOzw==}
    peerDependencies:
      react: '>=16.9.0'
      react-dom: '>=16.9.0'
    dependencies:
      '@babel/runtime': 7.24.7
      classnames: 2.5.1
      rc-resize-observer: 1.4.0(react-dom@18.2.0)(react@18.2.0)
      rc-util: 5.43.0(react-dom@18.2.0)(react@18.2.0)
      react: 18.2.0
      react-dom: 18.2.0(react@18.2.0)
    dev: false

  /rc-pagination@3.3.1(react-dom@18.2.0)(react@18.2.0):
    resolution: {integrity: sha512-eI4dSeB3OrFxll7KzWa3ZH63LV2tHxt0AUmZmDwuI6vc3CK5lZhaKUYq0fRowb5586hN+L26j5WZoSz9cwEfjg==}
    peerDependencies:
      react: '>=16.9.0'
      react-dom: '>=16.9.0'
    dependencies:
      '@babel/runtime': 7.24.7
      classnames: 2.5.1
      react: 18.2.0
      react-dom: 18.2.0(react@18.2.0)
    dev: false

  /rc-picker@3.3.4(dayjs@1.11.10)(moment@2.29.4)(react-dom@18.2.0)(react@18.2.0):
    resolution: {integrity: sha512-y7muUVAXqcacXXgLERCC9klEClZybOjWrcVZ54glxO5y+UO2MZmBk+ysWhhVHQ0l2nNtY+wj19gk1xnv+5eCxg==}
    engines: {node: '>=8.x'}
    peerDependencies:
      date-fns: '>= 2.x'
      dayjs: '>= 1.x'
      moment: '>= 2.x'
      react: '>=16.9.0'
      react-dom: '>=16.9.0'
    peerDependenciesMeta:
      date-fns:
        optional: true
      dayjs:
        optional: true
      moment:
        optional: true
    dependencies:
      '@babel/runtime': 7.24.7
      '@rc-component/trigger': 1.18.3(react-dom@18.2.0)(react@18.2.0)
      classnames: 2.5.1
      dayjs: 1.11.10
      moment: 2.29.4
      rc-util: 5.43.0(react-dom@18.2.0)(react@18.2.0)
      react: 18.2.0
      react-dom: 18.2.0(react@18.2.0)
    dev: false

  /rc-progress@3.4.2(react-dom@18.2.0)(react@18.2.0):
    resolution: {integrity: sha512-iAGhwWU+tsayP+Jkl9T4+6rHeQTG9kDz8JAHZk4XtQOcYN5fj9H34NXNEdRdZx94VUDHMqCb1yOIvi8eJRh67w==}
    peerDependencies:
      react: '>=16.9.0'
      react-dom: '>=16.9.0'
    dependencies:
      '@babel/runtime': 7.24.7
      classnames: 2.5.1
      rc-util: 5.43.0(react-dom@18.2.0)(react@18.2.0)
      react: 18.2.0
      react-dom: 18.2.0(react@18.2.0)
    dev: false

  /rc-rate@2.10.0(react-dom@18.2.0)(react@18.2.0):
    resolution: {integrity: sha512-TCjEpKPeN1m0EnGDDbb1KyxjNTJRzoReiPdtbrBJEey4Ryf/UGOQ6vqmz2yC6DJdYVDVUoZPdoz043ryh0t/nQ==}
    engines: {node: '>=8.x'}
    peerDependencies:
      react: '>=16.9.0'
      react-dom: '>=16.9.0'
    dependencies:
      '@babel/runtime': 7.24.7
      classnames: 2.5.1
      rc-util: 5.43.0(react-dom@18.2.0)(react@18.2.0)
      react: 18.2.0
      react-dom: 18.2.0(react@18.2.0)
    dev: false

  /rc-resize-observer@1.4.0(react-dom@18.2.0)(react@18.2.0):
    resolution: {integrity: sha512-PnMVyRid9JLxFavTjeDXEXo65HCRqbmLBw9xX9gfC4BZiSzbLXKzW3jPz+J0P71pLbD5tBMTT+mkstV5gD0c9Q==}
    peerDependencies:
      react: '>=16.9.0'
      react-dom: '>=16.9.0'
    dependencies:
      '@babel/runtime': 7.24.7
      classnames: 2.5.1
      rc-util: 5.43.0(react-dom@18.2.0)(react@18.2.0)
      react: 18.2.0
      react-dom: 18.2.0(react@18.2.0)
      resize-observer-polyfill: 1.5.1
    dev: false

  /rc-segmented@2.1.2(react-dom@18.2.0)(react@18.2.0):
    resolution: {integrity: sha512-qGo1bCr83ESXpXVOCXjFe1QJlCAQXyi9KCiy8eX3rIMYlTeJr/ftySIaTnYsitL18SvWf5ZEHsfqIWoX0EMfFQ==}
    peerDependencies:
      react: '>=16.0.0'
      react-dom: '>=16.0.0'
    dependencies:
      '@babel/runtime': 7.24.7
      classnames: 2.5.1
      rc-motion: 2.9.2(react-dom@18.2.0)(react@18.2.0)
      rc-util: 5.43.0(react-dom@18.2.0)(react@18.2.0)
      react: 18.2.0
      react-dom: 18.2.0(react@18.2.0)
    dev: false

  /rc-select@14.3.0(react-dom@18.2.0)(react@18.2.0):
    resolution: {integrity: sha512-y+TeAKvAvfAS7WXn4nFU8xtWJ1kLC7SVBvX3UQYtfU+N3BYNpNzHw/3F1Gu34rN2YWTG4hwspwFvDuRtGXytlQ==}
    engines: {node: '>=8.x'}
    peerDependencies:
      react: '*'
      react-dom: '*'
    dependencies:
      '@babel/runtime': 7.24.7
      '@rc-component/trigger': 1.18.3(react-dom@18.2.0)(react@18.2.0)
      classnames: 2.5.1
      rc-motion: 2.9.2(react-dom@18.2.0)(react@18.2.0)
      rc-overflow: 1.3.2(react-dom@18.2.0)(react@18.2.0)
      rc-util: 5.43.0(react-dom@18.2.0)(react@18.2.0)
      rc-virtual-list: 3.14.5(react-dom@18.2.0)(react@18.2.0)
      react: 18.2.0
      react-dom: 18.2.0(react@18.2.0)
    dev: false

  /rc-slider@10.1.1(react-dom@18.2.0)(react@18.2.0):
    resolution: {integrity: sha512-gn8oXazZISEhnmRinI89Z/JD/joAaM35jp+gDtIVSTD/JJMCCBqThqLk1SVJmvtfeiEF/kKaFY0+qt4SDHFUDw==}
    engines: {node: '>=8.x'}
    peerDependencies:
      react: '>=16.9.0'
      react-dom: '>=16.9.0'
    dependencies:
      '@babel/runtime': 7.24.7
      classnames: 2.5.1
      rc-util: 5.43.0(react-dom@18.2.0)(react@18.2.0)
      react: 18.2.0
      react-dom: 18.2.0(react@18.2.0)
    dev: false

  /rc-steps@6.0.1(react-dom@18.2.0)(react@18.2.0):
    resolution: {integrity: sha512-lKHL+Sny0SeHkQKKDJlAjV5oZ8DwCdS2hFhAkIjuQt1/pB81M0cA0ErVFdHq9+jmPmFw1vJB2F5NBzFXLJxV+g==}
    engines: {node: '>=8.x'}
    peerDependencies:
      react: '>=16.9.0'
      react-dom: '>=16.9.0'
    dependencies:
      '@babel/runtime': 7.24.7
      classnames: 2.5.1
      rc-util: 5.43.0(react-dom@18.2.0)(react@18.2.0)
      react: 18.2.0
      react-dom: 18.2.0(react@18.2.0)
    dev: false

  /rc-switch@4.0.0(react-dom@18.2.0)(react@18.2.0):
    resolution: {integrity: sha512-IfrYC99vN0gKaTyjQdqYuADU0eH00SAFHg3jOp8HrmUpJruhV1SohJzrCbPqPraZeX/6X/QKkdLfkdnUub05WA==}
    peerDependencies:
      react: '>=16.9.0'
      react-dom: '>=16.9.0'
    dependencies:
      '@babel/runtime': 7.24.7
      classnames: 2.5.1
      rc-util: 5.43.0(react-dom@18.2.0)(react@18.2.0)
      react: 18.2.0
      react-dom: 18.2.0(react@18.2.0)
    dev: false

  /rc-table@7.31.1(react-dom@18.2.0)(react@18.2.0):
    resolution: {integrity: sha512-KZPi35aGpv2VaL1Jbc58FBJo063HtKyVjhOFWX4AkBV7tjHHQokMdUoua5E+GPJh6QZUpK/a8PjKa9IZzPLIEA==}
    engines: {node: '>=8.x'}
    peerDependencies:
      react: '>=16.9.0'
      react-dom: '>=16.9.0'
    dependencies:
      '@babel/runtime': 7.24.7
      '@rc-component/context': 1.4.0(react-dom@18.2.0)(react@18.2.0)
      classnames: 2.5.1
      rc-resize-observer: 1.4.0(react-dom@18.2.0)(react@18.2.0)
      rc-util: 5.43.0(react-dom@18.2.0)(react@18.2.0)
      react: 18.2.0
      react-dom: 18.2.0(react@18.2.0)
    dev: false

  /rc-tabs@12.5.10(react-dom@18.2.0)(react@18.2.0):
    resolution: {integrity: sha512-Ay0l0jtd4eXepFH9vWBvinBjqOpqzcsJTerBGwJy435P2S90Uu38q8U/mvc1sxUEVOXX5ZCFbxcWPnfG3dH+tQ==}
    engines: {node: '>=8.x'}
    peerDependencies:
      react: '>=16.9.0'
      react-dom: '>=16.9.0'
    dependencies:
      '@babel/runtime': 7.24.7
      classnames: 2.5.1
      rc-dropdown: 4.0.1(react-dom@18.2.0)(react@18.2.0)
      rc-menu: 9.8.4(react-dom@18.2.0)(react@18.2.0)
      rc-motion: 2.9.2(react-dom@18.2.0)(react@18.2.0)
      rc-resize-observer: 1.4.0(react-dom@18.2.0)(react@18.2.0)
      rc-util: 5.43.0(react-dom@18.2.0)(react@18.2.0)
      react: 18.2.0
      react-dom: 18.2.0(react@18.2.0)
    dev: false

  /rc-textarea@1.1.0(react-dom@18.2.0)(react@18.2.0):
    resolution: {integrity: sha512-NNYSrnCfVunH/ffGak33FDrQSwAljrqnKVYruBrDD8JcKmQFiPsW6v0HzouvqhcJX6hTqLFOci4tZMkmsJdwdw==}
    peerDependencies:
      react: '>=16.9.0'
      react-dom: '>=16.9.0'
    dependencies:
      '@babel/runtime': 7.24.7
      classnames: 2.5.1
      rc-input: 0.2.2(react-dom@18.2.0)(react@18.2.0)
      rc-resize-observer: 1.4.0(react-dom@18.2.0)(react@18.2.0)
      rc-util: 5.43.0(react-dom@18.2.0)(react@18.2.0)
      react: 18.2.0
      react-dom: 18.2.0(react@18.2.0)
    dev: false

  /rc-tooltip@6.0.1(react-dom@18.2.0)(react@18.2.0):
    resolution: {integrity: sha512-MdvPlsD1fDSxKp9+HjXrc/CxLmA/s11QYIh1R7aExxfodKP7CZA++DG1AjrW80F8IUdHYcR43HAm0Y2BYPelHA==}
    peerDependencies:
      react: '>=16.9.0'
      react-dom: '>=16.9.0'
    dependencies:
      '@babel/runtime': 7.24.7
      '@rc-component/trigger': 1.18.3(react-dom@18.2.0)(react@18.2.0)
      classnames: 2.5.1
      react: 18.2.0
      react-dom: 18.2.0(react@18.2.0)
    dev: false

  /rc-tree-select@5.7.0(react-dom@18.2.0)(react@18.2.0):
    resolution: {integrity: sha512-YGMpBFK9qBkgng2ZhOw7yFiL9VnjHij+uNvP+tiU/QZGdf2XcO8LHXQNUZRGAEzx4PT5lUs6d7kIfkQ9a74bqg==}
    peerDependencies:
      react: '*'
      react-dom: '*'
    dependencies:
      '@babel/runtime': 7.24.7
      classnames: 2.5.1
      rc-select: 14.3.0(react-dom@18.2.0)(react@18.2.0)
      rc-tree: 5.7.12(react-dom@18.2.0)(react@18.2.0)
      rc-util: 5.43.0(react-dom@18.2.0)(react@18.2.0)
      react: 18.2.0
      react-dom: 18.2.0(react@18.2.0)
    dev: false

  /rc-tree@5.7.12(react-dom@18.2.0)(react@18.2.0):
    resolution: {integrity: sha512-LXA5nY2hG5koIAlHW5sgXgLpOMz+bFRbnZZ+cCg0tQs4Wv1AmY7EDi1SK7iFXhslYockbqUerQan82jljoaItg==}
    engines: {node: '>=10.x'}
    peerDependencies:
      react: '*'
      react-dom: '*'
    dependencies:
      '@babel/runtime': 7.24.7
      classnames: 2.5.1
      rc-motion: 2.9.2(react-dom@18.2.0)(react@18.2.0)
      rc-util: 5.43.0(react-dom@18.2.0)(react@18.2.0)
      rc-virtual-list: 3.14.5(react-dom@18.2.0)(react@18.2.0)
      react: 18.2.0
      react-dom: 18.2.0(react@18.2.0)
    dev: false

  /rc-trigger@5.3.4(react-dom@18.2.0)(react@18.2.0):
    resolution: {integrity: sha512-mQv+vas0TwKcjAO2izNPkqR4j86OemLRmvL2nOzdP9OWNWA1ivoTt5hzFqYNW9zACwmTezRiN8bttrC7cZzYSw==}
    engines: {node: '>=8.x'}
    peerDependencies:
      react: '>=16.9.0'
      react-dom: '>=16.9.0'
    dependencies:
      '@babel/runtime': 7.24.7
      classnames: 2.5.1
      rc-align: 4.0.15(react-dom@18.2.0)(react@18.2.0)
      rc-motion: 2.9.2(react-dom@18.2.0)(react@18.2.0)
      rc-util: 5.43.0(react-dom@18.2.0)(react@18.2.0)
      react: 18.2.0
      react-dom: 18.2.0(react@18.2.0)
    dev: false

  /rc-upload@4.3.6(react-dom@18.2.0)(react@18.2.0):
    resolution: {integrity: sha512-Bt7ESeG5tT3IY82fZcP+s0tQU2xmo1W6P3S8NboUUliquJLQYLkUcsaExi3IlBVr43GQMCjo30RA2o0i70+NjA==}
    peerDependencies:
      react: '>=16.9.0'
      react-dom: '>=16.9.0'
    dependencies:
      '@babel/runtime': 7.24.7
      classnames: 2.5.1
      rc-util: 5.43.0(react-dom@18.2.0)(react@18.2.0)
      react: 18.2.0
      react-dom: 18.2.0(react@18.2.0)
    dev: false

  /rc-util@5.43.0(react-dom@18.2.0)(react@18.2.0):
    resolution: {integrity: sha512-AzC7KKOXFqAdIBqdGWepL9Xn7cm3vnAmjlHqUnoQaTMZYhM4VlXGLkkHHxj/BZ7Td0+SOPKB4RGPboBVKT9htw==}
    peerDependencies:
      react: '>=16.9.0'
      react-dom: '>=16.9.0'
    dependencies:
      '@babel/runtime': 7.24.7
      react: 18.2.0
      react-dom: 18.2.0(react@18.2.0)
      react-is: 18.3.1
    dev: false

  /rc-virtual-list@3.14.5(react-dom@18.2.0)(react@18.2.0):
    resolution: {integrity: sha512-ZMOnkCLv2wUN8Jz7yI4XiSLa9THlYvf00LuMhb1JlsQCewuU7ydPuHw1rGVPhe9VZYl/5UqODtNd7QKJ2DMGfg==}
    engines: {node: '>=8.x'}
    peerDependencies:
      react: '>=16.9.0'
      react-dom: '>=16.9.0'
    dependencies:
      '@babel/runtime': 7.24.7
      classnames: 2.5.1
      rc-resize-observer: 1.4.0(react-dom@18.2.0)(react@18.2.0)
      rc-util: 5.43.0(react-dom@18.2.0)(react@18.2.0)
      react: 18.2.0
      react-dom: 18.2.0(react@18.2.0)
    dev: false

  /react-dom@18.2.0(react@18.2.0):
    resolution: {integrity: sha512-6IMTriUmvsjHUjNtEDudZfuDQUoWXVxKHhlEGSk81n4YFS+r/Kl99wXiwlVXtPBtJenozv2P+hxDsw9eA7Xo6g==}
    peerDependencies:
      react: ^18.2.0
    dependencies:
      loose-envify: 1.4.0
      react: 18.2.0
      scheduler: 0.23.2
    dev: false

  /react-is@16.13.1:
    resolution: {integrity: sha512-24e6ynE2H+OKt4kqsOvNd8kBpV65zoxbA4BVsEOB3ARVWQki/DHzaUoC5KuON/BiccDaCCTZBuOcfZs70kR8bQ==}
    dev: false

  /react-is@18.3.1:
    resolution: {integrity: sha512-/LLMVyas0ljjAtoYiPqYiL8VWXzUUdThrmU5+n20DZv+a+ClRoevUzw5JxU+Ieh5/c87ytoTBV9G1FiKfNJdmg==}
    dev: false

  /react-redux@8.0.5(@types/react-dom@18.2.0)(@types/react@18.2.0)(react-dom@18.2.0)(react@18.2.0)(redux@4.2.1):
    resolution: {integrity: sha512-Q2f6fCKxPFpkXt1qNRZdEDLlScsDWyrgSj0mliK59qU6W5gvBiKkdMEG2lJzhd1rCctf0hb6EtePPLZ2e0m1uw==}
    peerDependencies:
      '@types/react': ^16.8 || ^17.0 || ^18.0
      '@types/react-dom': ^16.8 || ^17.0 || ^18.0
      react: ^16.8 || ^17.0 || ^18.0
      react-dom: ^16.8 || ^17.0 || ^18.0
      react-native: '>=0.59'
      redux: ^4
    peerDependenciesMeta:
      '@types/react':
        optional: true
      '@types/react-dom':
        optional: true
      react-dom:
        optional: true
      react-native:
        optional: true
      redux:
        optional: true
    dependencies:
      '@babel/runtime': 7.24.7
      '@types/hoist-non-react-statics': 3.3.5
      '@types/react': 18.2.0
      '@types/react-dom': 18.2.0
      '@types/use-sync-external-store': 0.0.3
      hoist-non-react-statics: 3.3.2
      react: 18.2.0
      react-dom: 18.2.0(react@18.2.0)
      react-is: 18.3.1
      redux: 4.2.1
      use-sync-external-store: 1.2.2(react@18.2.0)
    dev: false

  /react-router-dom@6.9.0(react-dom@18.2.0)(react@18.2.0):
    resolution: {integrity: sha512-/seUAPY01VAuwkGyVBPCn1OXfVbaWGGu4QN9uj0kCPcTyNYgL1ldZpxZUpRU7BLheKQI4Twtl/OW2nHRF1u26Q==}
    engines: {node: '>=14'}
    peerDependencies:
      react: '>=16.8'
      react-dom: '>=16.8'
    dependencies:
      '@remix-run/router': 1.4.0
      react: 18.2.0
      react-dom: 18.2.0(react@18.2.0)
      react-router: 6.9.0(react@18.2.0)
    dev: false

  /react-router@6.9.0(react@18.2.0):
    resolution: {integrity: sha512-51lKevGNUHrt6kLuX3e/ihrXoXCa9ixY/nVWRLlob4r/l0f45x3SzBvYJe3ctleLUQQ5fVa4RGgJOTH7D9Umhw==}
    engines: {node: '>=14'}
    peerDependencies:
      react: '>=16.8'
    dependencies:
      '@remix-run/router': 1.4.0
      react: 18.2.0
    dev: false

  /react@18.2.0:
    resolution: {integrity: sha512-/3IjMdb2L9QbBdWiW5e3P2/npwMBaU9mHCSCUzNln0ZCYbcfTsGbTJrU/kGemdH2IWmB2ioZ+zkxtmq6g09fGQ==}
    engines: {node: '>=0.10.0'}
    dependencies:
      loose-envify: 1.4.0
    dev: false

  /redux-thunk@2.4.2(redux@4.2.1):
    resolution: {integrity: sha512-+P3TjtnP0k/FEjcBL5FZpoovtvrTNT/UXd4/sluaSyrURlSlhLSzEdfsTBW7WsKB6yPvgd7q/iZPICFjW4o57Q==}
    peerDependencies:
      redux: ^4
    dependencies:
      redux: 4.2.1
    dev: false

  /redux@4.2.1:
    resolution: {integrity: sha512-LAUYz4lc+Do8/g7aeRa8JkyDErK6ekstQaqWQrNRW//MY1TvCEpMtpTWvlQ+FPbWCx+Xixu/6SHt5N0HR+SB4w==}
    dependencies:
      '@babel/runtime': 7.24.7
    dev: false

  /regenerator-runtime@0.14.1:
    resolution: {integrity: sha512-dYnhHh0nJoMfnkZs6GmmhFknAGRrLznOu5nc9ML+EJxGvrx6H7teuevqVqCuPcPK//3eDrrjQhehXVx9cnkGdw==}
    dev: false

  /remove-accents@0.4.2:
    resolution: {integrity: sha512-7pXIJqJOq5tFgG1A2Zxti3Ht8jJF337m4sowbuHsW30ZnkQFnDzy9qBNhgzX8ZLW4+UBcXiiR7SwR6pokHsxiA==}
    dev: false

  /reselect@4.1.8:
    resolution: {integrity: sha512-ab9EmR80F/zQTMNeneUr4cv+jSwPJgIlvEmVwLerwrWVbpLlBuls9XHzIeTFy4cegU2NHBp3va0LKOzU5qFEYQ==}
    dev: false

  /resize-observer-polyfill@1.5.1:
    resolution: {integrity: sha512-LwZrotdHOo12nQuZlHEmtuXdqGoOD0OhaxopaNFxWzInpEgaLWoVuAMbTzixuosCx2nEG58ngzW3vxdWoxIgdg==}
    dev: false

  /resolve@1.22.8:
    resolution: {integrity: sha512-oKWePCxqpd6FlLvGV1VU0x7bkPmmCNolxzjMf4NczoDnQcIWrAF+cPtZn5i6n+RfD2d9i0tzpKnG6Yk168yIyw==}
    hasBin: true
    dependencies:
      is-core-module: 2.14.0
      path-parse: 1.0.7
      supports-preserve-symlinks-flag: 1.0.0
    dev: true

  /rollup-plugin-gzip@3.1.0(rollup@4.18.0):
    resolution: {integrity: sha512-PFS9s6/w6dCra6/Z8PGD+ug3aaaqKLDCbr5y1Ek71Wd4rQSmMnOqCIIMgwbYxZ9A/gjP3pCN6rA4pAG47jxF0w==}
    engines: {node: '>=10.0.0'}
    peerDependencies:
      rollup: '>=2.0.0'
    dependencies:
      rollup: 4.18.0
    dev: true

  /rollup@3.29.4:
    resolution: {integrity: sha512-oWzmBZwvYrU0iJHtDmhsm662rC15FRXmcjCk1xD771dFDx5jJ02ufAQQTn0etB2emNk4J9EZg/yWKpsn9BWGRw==}
    engines: {node: '>=14.18.0', npm: '>=8.0.0'}
    hasBin: true
    optionalDependencies:
      fsevents: 2.3.3
    dev: true

  /rollup@4.18.0:
    resolution: {integrity: sha512-QmJz14PX3rzbJCN1SG4Xe/bAAX2a6NpCP8ab2vfu2GiUr8AQcr2nCV/oEO3yneFarB67zk8ShlIyWb2LGTb3Sg==}
    engines: {node: '>=18.0.0', npm: '>=8.0.0'}
    hasBin: true
    dependencies:
      '@types/estree': 1.0.5
    optionalDependencies:
      '@rollup/rollup-android-arm-eabi': 4.18.0
      '@rollup/rollup-android-arm64': 4.18.0
      '@rollup/rollup-darwin-arm64': 4.18.0
      '@rollup/rollup-darwin-x64': 4.18.0
      '@rollup/rollup-linux-arm-gnueabihf': 4.18.0
      '@rollup/rollup-linux-arm-musleabihf': 4.18.0
      '@rollup/rollup-linux-arm64-gnu': 4.18.0
      '@rollup/rollup-linux-arm64-musl': 4.18.0
      '@rollup/rollup-linux-powerpc64le-gnu': 4.18.0
      '@rollup/rollup-linux-riscv64-gnu': 4.18.0
      '@rollup/rollup-linux-s390x-gnu': 4.18.0
      '@rollup/rollup-linux-x64-gnu': 4.18.0
      '@rollup/rollup-linux-x64-musl': 4.18.0
      '@rollup/rollup-win32-arm64-msvc': 4.18.0
      '@rollup/rollup-win32-ia32-msvc': 4.18.0
      '@rollup/rollup-win32-x64-msvc': 4.18.0
      fsevents: 2.3.3
    dev: true

  /safer-buffer@2.1.2:
    resolution: {integrity: sha512-YZo3K82SD7Riyi0E1EQPojLz7kpepnSQI9IyPbHHg1XXXevb5dJI7tpyN2ADxGcQbHG7vcyRHk0cbwqcQriUtg==}
    requiresBuild: true
    dev: true
    optional: true

  /sax@1.4.1:
    resolution: {integrity: sha512-+aWOz7yVScEGoKNd4PA10LZ8sk0A/z5+nXQG5giUO5rprX9jgYsTdov9qCchZiPIZezbZH+jRut8nPodFAX4Jg==}
    requiresBuild: true
    dev: true
    optional: true

  /scheduler@0.23.2:
    resolution: {integrity: sha512-UOShsPwz7NrMUqhR6t0hWjFduvOzbtv7toDH1/hIrfRNIDBnnBWd0CwJTGvTpngVlmwGCdP9/Zl/tVrDqcuYzQ==}
    dependencies:
      loose-envify: 1.4.0
    dev: false

  /screenfull@5.2.0:
    resolution: {integrity: sha512-9BakfsO2aUQN2K9Fdbj87RJIEZ82Q9IGim7FqM5OsebfoFC6ZHXgDq/KvniuLTPdeM8wY2o6Dj3WQ7KeQCj3cA==}
    engines: {node: '>=0.10.0'}
    dev: false

  /scroll-into-view-if-needed@3.1.0:
    resolution: {integrity: sha512-49oNpRjWRvnU8NyGVmUaYG4jtTkNonFZI86MmGRDqBphEK2EXT9gdEUoQPZhuBM8yWHxCWbobltqYO5M4XrUvQ==}
    dependencies:
      compute-scroll-into-view: 3.1.0
    dev: false

  /semver@5.7.2:
    resolution: {integrity: sha512-cBznnQ9KjJqU67B52RMC65CMarK2600WFnbkcaiwWq3xy/5haFJlshgnpjovMVJ+Hff49d8GEn0b87C5pDQ10g==}
    hasBin: true
    requiresBuild: true
    dev: true
    optional: true

  /sort-by@1.2.0:
    resolution: {integrity: sha512-aRyW65r3xMnf4nxJRluCg0H/woJpksU1dQxRtXYzau30sNBOmf5HACpDd9MZDhKh7ALQ5FgSOfMPwZEtUmMqcg==}
    dependencies:
      object-path: 0.6.0
    dev: false

  /source-map-js@1.2.0:
    resolution: {integrity: sha512-itJW8lvSA0TXEphiRoawsCksnlf8SyvmFzIhltqAHluXd88pkCd+cXJVHTDwdCr0IzwptSm035IHQktUu1QUMg==}
    engines: {node: '>=0.10.0'}
    dev: true

  /source-map@0.6.1:
    resolution: {integrity: sha512-UjgapumWlbMhkBgzT7Ykc5YXUT46F0iKu8SGXq0bcwP5dz/h0Plj6enJqjz1Zbq2l5WaqYnrVbwWOWMyF3F47g==}
    engines: {node: '>=0.10.0'}
    requiresBuild: true
    dev: true
    optional: true

  /ssf@0.11.2:
    resolution: {integrity: sha512-+idbmIXoYET47hH+d7dfm2epdOMUDjqcB4648sTZ+t2JwoyBFL/insLfB/racrDmsKB3diwsDA696pZMieAC5g==}
    engines: {node: '>=0.8'}
    dependencies:
      frac: 1.1.2
    dev: false

  /string-convert@0.2.1:
    resolution: {integrity: sha512-u/1tdPl4yQnPBjnVrmdLo9gtuLvELKsAoRapekWggdiQNvvvum+jYF329d84NAa660KQw7pB2n36KrIKVoXa3A==}
    dev: false

  /stylis@4.3.2:
    resolution: {integrity: sha512-bhtUjWd/z6ltJiQwg0dUfxEJ+W+jdqQd8TbWLWyeIJHlnsqmGLRFFd8e5mA0AZi/zx90smXRlN66YMTcaSFifg==}
    dev: false

  /supports-preserve-symlinks-flag@1.0.0:
    resolution: {integrity: sha512-ot0WnXS9fgdkgIcePe6RHNk1WA8+muPa6cSjeR3V8K27q9BB1rTE3R1p7Hv0z1ZyAc8s6Vvv8DIyWf681MAt0w==}
    engines: {node: '>= 0.4'}
    dev: true

  /throttle-debounce@5.0.2:
    resolution: {integrity: sha512-B71/4oyj61iNH0KeCamLuE2rmKuTO5byTOSVwECM5FA7TiAiAW+UqTKZ9ERueC4qvgSttUhdmq1mXC3kJqGX7A==}
    engines: {node: '>=12.22'}
    dev: false

  /toggle-selection@1.0.6:
    resolution: {integrity: sha512-BiZS+C1OS8g/q2RRbJmy59xpyghNBqrr6k5L/uKBGRsTfxmu3ffiRnd8mlGPUVayg8pvfi5urfnu8TU7DVOkLQ==}
    dev: false

  /tslib@2.3.0:
    resolution: {integrity: sha512-N82ooyxVNm6h1riLCoyS9e3fuJ3AMG2zIZs2Gd1ATcSFjSA23Q0fzjjZeh0jbJvWVDZ0cJT8yaNNaaXHzueNjg==}
    dev: false

  /tslib@2.6.3:
    resolution: {integrity: sha512-xNvxJEOUiWPGhUuUdQgAJPKOOJfGnIyKySOc09XkKsgdUV/3E2zvwZYdejjmRgPCgcym1juLH3226yA7sEFJKQ==}

  /typescript@4.9.3:
    resolution: {integrity: sha512-CIfGzTelbKNEnLpLdGFgdyKhG23CKdKgQPOBc+OUNrkJ2vr+KSzsSV5kq5iWhEQbok+quxgGzrAtGWCyU7tHnA==}
    engines: {node: '>=4.2.0'}
    hasBin: true
    dev: true

  /use-sync-external-store@1.2.2(react@18.2.0):
    resolution: {integrity: sha512-PElTlVMwpblvbNqQ82d2n6RjStvdSoNe9FG28kNfz3WiXilJm4DdNkEzRhCZuIDwY8U08WVihhGR5iRqAwfDiw==}
    peerDependencies:
      react: ^16.8.0 || ^17.0.0 || ^18.0.0
    dependencies:
      react: 18.2.0
    dev: false

  /vite@4.2.0(less@4.1.3):
    resolution: {integrity: sha512-AbDTyzzwuKoRtMIRLGNxhLRuv1FpRgdIw+1y6AQG73Q5+vtecmvzKo/yk8X/vrHDpETRTx01ABijqUHIzBXi0g==}
    engines: {node: ^14.18.0 || >=16.0.0}
    hasBin: true
    peerDependencies:
      '@types/node': '>= 14'
      less: '*'
      sass: '*'
      stylus: '*'
      sugarss: '*'
      terser: ^5.4.0
    peerDependenciesMeta:
      '@types/node':
        optional: true
      less:
        optional: true
      sass:
        optional: true
      stylus:
        optional: true
      sugarss:
        optional: true
      terser:
        optional: true
    dependencies:
      esbuild: 0.17.19
      less: 4.1.3
      postcss: 8.4.39
      resolve: 1.22.8
      rollup: 3.29.4
    optionalDependencies:
      fsevents: 2.3.3
    dev: true

  /wmf@1.0.2:
    resolution: {integrity: sha512-/p9K7bEh0Dj6WbXg4JG0xvLQmIadrner1bi45VMJTfnbVHsc7yIajZyoSoK60/dtVBs12Fm6WkUI5/3WAVsNMw==}
    engines: {node: '>=0.8'}
    dev: false

  /word@0.3.0:
    resolution: {integrity: sha512-OELeY0Q61OXpdUfTp+oweA/vtLVg5VDOXh+3he3PNzLGG/y0oylSOC1xRVj0+l4vQ3tj/bB1HVHv1ocXkQceFA==}
    engines: {node: '>=0.8'}
    dev: false

  /xlsx@0.18.5:
    resolution: {integrity: sha512-dmg3LCjBPHZnQp5/F/+nnTa+miPJxUXB6vtk42YjBBKayDNagxGEeIdWApkYPOf3Z3pm3k62Knjzp7lMeTEtFQ==}
    engines: {node: '>=0.8'}
    hasBin: true
    dependencies:
      adler-32: 1.3.1
      cfb: 1.2.2
      codepage: 1.15.0
      crc-32: 1.2.2
      ssf: 0.11.2
      wmf: 1.0.2
      word: 0.3.0
    dev: false

  /zrender@5.4.3:
    resolution: {integrity: sha512-DRUM4ZLnoaT0PBVvGBDO9oWIDBKFdAVieNWxWwK0niYzJCMwGchRk21/hsE+RKkIveH3XHCyvXcJDkgLVvfizQ==}
    dependencies:
      tslib: 2.3.0
    dev: false
