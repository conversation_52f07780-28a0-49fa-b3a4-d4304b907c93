# See https://help.github.com/articles/ignoring-files/ for more about ignoring files.

# dependencies
/node_modules
/.pnp
.pnp.js

# testing
/coverage

# production
/build

# misc
.DS_Store
.env.local
.env.development.local
.env.test.local
.env.production.local
.env
.env.production
.env.development

npm-debug.log*
yarn-debug.log*
yarn-error.log*

yarn.lock
package-lock.json

deploy-test.sh
deploy-prod.sh
deploy-demo.sh

dist/