# 方案C测试验证指南

## 🧪 测试步骤

### 1. 重新构建和启动服务

```bash
# 停止当前服务
docker-compose down

# 重新构建（包含新代码）
docker-compose build

# 启动服务
docker-compose up -d

# 查看日志确认启动正常
docker-compose logs -f playedu
```

### 2. 验证后端代理接口

```bash
# 测试代理接口是否可访问
curl -X OPTIONS http://localhost:9700/backend/v1/upload/cos-proxy/test-bucket/ap-guangzhou/test-object

# 应该返回包含CORS头的响应
```

### 3. 浏览器测试上传

1. **打开管理后台**：http://localhost:9900
2. **登录系统**：admin/playeduxyz
3. **进入系统配置**：系统管理 → 系统配置 → S3存储
4. **配置腾讯云COS**：
   ```
   AccessKey: 你的SecretId
   SecretKey: 你的SecretKey
   Bucket: dev-1361654307
   Region: ap-guangzhou
   Endpoint: https://dev-1361654307.cos.ap-guangzhou.myqcloud.com
   ```
5. **保存配置**
6. **测试上传**：
   - 进入课程管理
   - 创建新课程
   - 上传封面图片或课件
   - 观察浏览器网络请求

### 4. 验证代理生效

**预期行为：**
- 原始COS URL：`https://dev-1361654307.cos.ap-guangzhou.myqcloud.com/xxx`
- 代理后URL：`http://localhost:9700/backend/v1/upload/cos-proxy/dev-1361654307/ap-guangzhou/xxx`

**浏览器开发者工具检查：**
1. 打开F12 → Network标签
2. 上传文件时观察请求URL
3. 应该看到请求发送到后端代理接口，而不是直接到COS
4. 检查响应状态码是否为200

### 5. 验证文件上传成功

1. **检查文件是否上传到COS**：
   - 登录腾讯云控制台
   - 进入COS → 你的存储桶
   - 检查是否有新上传的文件

2. **检查文件访问**：
   - 在PlayEdu中查看上传的文件
   - 确认图片/视频能正常显示和播放

## 🔍 常见问题排查

### 问题1：代理接口404错误
- **检查**：确认UploadProxyController已编译
- **解决**：重新构建docker镜像

### 问题2：仍然出现CORS错误
- **检查**：确认前端代码已更新并重新构建
- **解决**：清除浏览器缓存，重新加载页面

### 问题3：上传失败但无错误信息
- **检查**：查看后端日志 `docker-compose logs playedu`
- **检查**：确认COS配置参数正确
- **检查**：确认COS存储桶权限设置

### 问题4：代理接口500错误
- **检查**：查看后端详细错误日志
- **解决**：确认RestTemplate配置正确

## ✅ 成功验证标准

1. **上传过程**：文件能成功上传到腾讯云COS
2. **网络请求**：请求通过后端代理，无CORS错误
3. **文件访问**：上传的文件能在PlayEdu中正常访问
4. **控制台无错误**：浏览器控制台无CORS相关错误

## 📞 需要支持

如果测试过程中遇到问题：
1. 提供浏览器控制台错误截图
2. 提供后端服务日志
3. 提供网络请求的详细信息
4. 确认COS配置参数是否正确