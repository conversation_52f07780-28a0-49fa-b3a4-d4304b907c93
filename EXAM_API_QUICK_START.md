# 考试系统 API 快速实现指南

## 🚀 快速开始

### 第1步：创建 playedu-exam 模块

在 `playedu-api/` 下创建新模块：

```bash
cd playedu-api
mkdir -p playedu-exam/src/main/java/xyz/playedu/exam/{domain,mapper,service/impl,controller,vo}
mkdir -p playedu-exam/src/main/resources/mapper
```

### 第2步：创建 pom.xml

```xml
<project>
  <modelVersion>4.0.0</modelVersion>
  <parent>
    <groupId>xyz.playedu</groupId>
    <artifactId>playedu-api</artifactId>
    <version>1.0</version>
  </parent>
  
  <artifactId>playedu-exam</artifactId>
  <name>playedu-exam</name>
  
  <dependencies>
    <dependency>
      <groupId>xyz.playedu</groupId>
      <artifactId>playedu-common</artifactId>
      <version>1.0</version>
    </dependency>
    
    <!-- Excel 处理 -->
    <dependency>
      <groupId>org.apache.poi</groupId>
      <artifactId>poi-ooxml</artifactId>
      <version>5.0.0</version>
    </dependency>
  </dependencies>
</project>
```

### 第3步：数据模型

#### 1. Exam.java
```java
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class Exam {
    private Long id;
    private String title;
    private String type;
    private Integer duration;
    private Integer pass_score;
    private String description;
    private Integer admin_id;
    private LocalDateTime created_at;
    private LocalDateTime updated_at;
}
```

#### 2. ExamQuestion.java
```java
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class ExamQuestion {
    private Long id;
    private Long exam_id;
    private String title;
    private String type;           // single_choice/multiple_choice/fill_blank/essay
    private String difficulty;     // easy/medium/hard
    private Integer score;
    private String analysis;
    private String answer;
    private Integer created_by;
    private LocalDateTime created_at;
    private LocalDateTime updated_at;
}
```

#### 3. ExamQuestionOption.java
```java
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class ExamQuestionOption {
    private Long id;
    private Long question_id;
    private String option_key;     // A/B/C/D
    private String content;
    private Integer is_correct;
    private LocalDateTime created_at;
}
```

#### 4. ExamImportLog.java
```java
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class ExamImportLog {
    private Long id;
    private Long exam_id;
    private Integer admin_id;
    private Integer total_count;
    private Integer success_count;
    private Integer error_count;
    private String error_details;  // JSON
    private LocalDateTime created_at;
}
```

### 第4步：Mapper 接口

#### ExamMapper.java
```java
@Mapper
public interface ExamMapper {
    void insert(Exam exam);
    void updateById(Exam exam);
    void deleteById(Long id);
    Exam selectById(Long id);
    List<Exam> selectByPage(
        @Param("offset") int offset,
        @Param("limit") int limit,
        @Param("title") String title
    );
    long countByTitle(@Param("title") String title);
}
```

### 第5步：Service 接口

#### ExamService.java
```java
public interface ExamService {
    Page<ExamVo> paginate(int pageNo, int pageSize, String title);
    ExamVo detail(Long examId);
    void create(ExamCreateRequest req);
    void update(Long examId, ExamUpdateRequest req);
    void delete(Long examId);
}
```

#### ExamServiceImpl.java
```java
@Service
@Slf4j
public class ExamServiceImpl implements ExamService {
    @Autowired
    private ExamMapper examMapper;
    
    @Override
    public Page<ExamVo> paginate(int pageNo, int pageSize, String title) {
        // 实现分页逻辑
        long total = examMapper.countByTitle(title);
        int offset = (pageNo - 1) * pageSize;
        List<Exam> list = examMapper.selectByPage(offset, pageSize, title);
        return new Page<>(list, total, pageNo, pageSize);
    }
    
    // 其他方法...
}
```

### 第6步：Controller

#### ExamController.java
```java
@RestController
@RequestMapping("/backend/v1/exams")
@Slf4j
public class ExamController {
    @Autowired
    private ExamService examService;
    
    @GetMapping
    public JsonResponse list(
        @RequestParam(defaultValue = "1") int page,
        @RequestParam(defaultValue = "10") int size,
        @RequestParam(required = false) String title
    ) {
        Page<ExamVo> data = examService.paginate(page, size, title);
        return JsonResponse.data(data);
    }
    
    @PostMapping
    @Log(title = "创建考试", businessType = BusinessTypeConstant.INSERT)
    public JsonResponse create(@RequestBody ExamCreateRequest req) {
        examService.create(req);
        return JsonResponse.success("创建成功");
    }
    
    @GetMapping("/{id}")
    public JsonResponse detail(@PathVariable Long id) {
        ExamVo data = examService.detail(id);
        return JsonResponse.data(data);
    }
    
    @PutMapping("/{id}")
    @Log(title = "修改考试", businessType = BusinessTypeConstant.UPDATE)
    public JsonResponse update(
        @PathVariable Long id,
        @RequestBody ExamUpdateRequest req
    ) {
        examService.update(id, req);
        return JsonResponse.success("修改成功");
    }
    
    @DeleteMapping("/{id}")
    @Log(title = "删除考试", businessType = BusinessTypeConstant.DELETE)
    public JsonResponse delete(@PathVariable Long id) {
        examService.delete(id);
        return JsonResponse.success("删除成功");
    }
}
```

### 第7步：VO/DTO

#### ExamVo.java
```java
@Data
@Builder
public class ExamVo {
    private Long id;
    private String title;
    private String type;
    private Integer duration;
    private Integer pass_score;
    private String description;
    private LocalDateTime created_at;
}
```

#### ExamCreateRequest.java
```java
@Data
public class ExamCreateRequest {
    @NotBlank(message = "考试标题不能为空")
    private String title;
    
    @NotBlank(message = "考试类型不能为空")
    private String type;
    
    @NotNull(message = "考试时长不能为空")
    @Min(1)
    @Max(480)
    private Integer duration;
    
    @NotNull(message = "及格分数不能为空")
    @Min(0)
    @Max(100)
    private Integer pass_score;
    
    private String description;
}
```

## 📝 SQL 建表语句

```sql
-- 考试表
CREATE TABLE exams (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    title VARCHAR(255) NOT NULL,
    type VARCHAR(20),
    duration INT,
    pass_score INT,
    description TEXT,
    admin_id INT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    KEY idx_title (title)
) CHARSET=utf8mb4;

-- 试题表
CREATE TABLE exam_questions (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    exam_id BIGINT NOT NULL,
    title TEXT NOT NULL,
    type VARCHAR(20),
    difficulty VARCHAR(20),
    score INT,
    analysis TEXT,
    answer TEXT,
    created_by INT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    KEY idx_exam_id (exam_id)
) CHARSET=utf8mb4;

-- 试题选项表
CREATE TABLE exam_question_options (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    question_id BIGINT NOT NULL,
    option_key VARCHAR(10),
    content TEXT,
    is_correct INT DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    KEY idx_question_id (question_id)
) CHARSET=utf8mb4;

-- 导入日志表
CREATE TABLE exam_import_logs (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    exam_id BIGINT NOT NULL,
    admin_id INT,
    total_count INT,
    success_count INT,
    error_count INT,
    error_details LONGTEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    KEY idx_exam_id (exam_id)
) CHARSET=utf8mb4;
```

## 🔄 实现流程

1. ✅ 创建数据模型 (Domain)
2. ✅ 创建 Mapper 接口和 XML 映射
3. ✅ 创建 Service 接口和实现类
4. ✅ 创建 Controller
5. ✅ 创建 VO/DTO
6. ✅ 更新 pom.xml (主项目)
7. ✅ 写单元测试
8. ✅ 测试 API

## 📞 常见问题

**Q: 如何处理分页？**
A: 使用 Page 类来包装分页结果，包括数据列表、总条数、当前页、页大小等。

**Q: 如何验证输入数据？**
A: 使用 @NotNull、@NotBlank、@Min、@Max 等注解，配合 @Validated。

**Q: 如何处理异常？**
A: 使用全局异常处理器，返回统一的 JsonResponse。

**Q: 如何记录操作日志？**
A: 使用 @Log 注解，在 Interceptor 中处理。

---

**版本**: 1.0  
**更新日期**: 2025-10-30
