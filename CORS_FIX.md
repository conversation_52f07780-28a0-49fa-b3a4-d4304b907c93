# CORS 跨域问题修复方案

## 问题诊断

### 错误信息
```
Access to XMLHttpRequest at 'http://localhost:9898/backend/v1/upload/cos-proxy/minio/upload-id?extension=pdf'
from origin 'http://localhost:3000' has been blocked by CORS policy: 
No 'Access-Control-Allow-Origin' header is present on the requested resource.
```

### 原因
1. **OPTIONS预检请求未被正确处理**
2. **某些接口缺少CORS响应头**
3. **请求方法不匹配**（API定义是POST，但前端调用是GET）

## 修复方案

### 1. 后端修复 - UploadProxyController.java

#### A. 添加全局OPTIONS预检处理
```java
@RequestMapping(value = "/**", method = RequestMethod.OPTIONS)
public ResponseEntity<Void> handleCorsPreFlight(HttpServletRequest request, HttpServletResponse response) {
    response.setHeader("Access-Control-Allow-Origin", "*");
    response.setHeader("Access-Control-Allow-Methods", "GET, POST, PUT, DELETE, OPTIONS, HEAD");
    response.setHeader("Access-Control-Allow-Headers", "Content-Type, Authorization, X-Requested-With, X-Original-COS-URL");
    response.setHeader("Access-Control-Max-Age", "3600");
    response.setHeader("Access-Control-Allow-Credentials", "true");
    response.setHeader("Access-Control-Expose-Headers", "ETag, Content-Type");
    return ResponseEntity.ok().build();
}
```

#### B. 为所有接口添加CORS响应头
```java
private void addCorsHeaders(HttpServletResponse response) {
    response.setHeader("Access-Control-Allow-Origin", "*");
    response.setHeader("Access-Control-Allow-Methods", "GET, POST, PUT, DELETE, OPTIONS, HEAD");
    response.setHeader("Access-Control-Allow-Headers", "Content-Type, Authorization, X-Requested-With, X-Original-COS-URL");
    response.setHeader("Access-Control-Max-Age", "3600");
    response.setHeader("Access-Control-Allow-Credentials", "true");
    response.setHeader("Access-Control-Expose-Headers", "ETag, Content-Type");
}
```

#### C. 修改所有接口签名，添加HttpServletResponse参数
```java
@PutMapping("/upload")
public ResponseEntity<?> uploadFile(..., HttpServletResponse response) {
    addCorsHeaders(response);
    // ...
}

@GetMapping("/config")
public ResponseEntity<?> getS3Config(HttpServletResponse response) {
    addCorsHeaders(response);
    // ...
}

@PostMapping("/minio/upload-id")
public ResponseEntity<?> getUploadId(..., HttpServletResponse response) {
    addCorsHeaders(response);
    // ...
}

@PostMapping("/minio/pre-sign-url")
public ResponseEntity<?> getPreSignUrl(..., HttpServletResponse response) {
    addCorsHeaders(response);
    // ...
}

@PostMapping("/minio/merge-file")
public ResponseEntity<?> mergeFile(..., HttpServletResponse response) {
    addCorsHeaders(response);
    // ...
}
```

### 2. 前端修复 - upload.ts

#### 改动：统一使用POST请求
```typescript
// 获取分片上传ID
export function minioUploadId(extension: string) {
  return client.post("/backend/v1/upload/cos-proxy/minio/upload-id", {
    extension,
  });
}

// 获取预签名URL
export function minioPreSignUrl(
  uploadId: string,
  filename: string,
  partNumber: number
) {
  return client.post("/backend/v1/upload/cos-proxy/minio/pre-sign-url", {
    uploadId,        // 改为驼峰命名
    filename,
    partNumber,      // 改为驼峰命名
  });
}
```

## 关键改动总结

| 组件 | 改动 | 原因 |
|------|------|------|
| 后端 | 添加OPTIONS预检处理 | 处理CORS预检请求 |
| 后端 | 所有接口添加CORS头 | 确保响应包含跨域头 |
| 后端 | 移除类级别@CrossOrigin | 使用更灵活的逐接口配置 |
| 前端 | 改为POST请求 | 与后端@PostMapping对应 |
| 前端 | 参数名改为驼峰 | 与后端@RequestBody接收对应 |

## 验证清单

- [x] 后端编译成功
- [x] 前端编译成功（0 TypeScript errors）
- [x] 所有接口都有CORS头配置
- [x] OPTIONS预检请求被正确处理
- [x] 前端API调用方法与后端定义一致

## CORS响应头说明

| 头部 | 值 | 说明 |
|------|-----|------|
| `Access-Control-Allow-Origin` | `*` | 允许所有来源 |
| `Access-Control-Allow-Methods` | `GET, POST, PUT, DELETE, OPTIONS, HEAD` | 允许的HTTP方法 |
| `Access-Control-Allow-Headers` | `Content-Type, Authorization, X-Requested-With, X-Original-COS-URL` | 允许的请求头 |
| `Access-Control-Max-Age` | `3600` | 预检缓存时间（秒）|
| `Access-Control-Allow-Credentials` | `true` | 允许凭证 |
| `Access-Control-Expose-Headers` | `ETag, Content-Type` | 暴露给客户端的响应头 |

## 重启应用

```bash
# 1. 停止运行中的后端服务（Ctrl+C）
# 2. 重新启动后端服务
cd playedu-api
mvn spring-boot:run

# 3. 前端无需重启（已编译完成）

# 4. 刷新浏览器
http://localhost:3000/courseware
```

## 测试步骤

1. **打开浏览器DevTools** → Network标签
2. **进入课件上传页面**
3. **点击上传课件按钮**
4. **观察Network请求**：
   - 应该看到 `/minio/upload-id` 的OPTIONS预检请求（状态码200）
   - 然后是POST请求（状态码200）
   - 不应该再有CORS错误

## 常见问题

### Q: 还是出现CORS错误？
**A:** 
1. 确保后端服务已重启（使用最新代码）
2. 清除浏览器缓存（Ctrl+Shift+Delete）
3. 检查后端日志是否看到调试信息

### Q: 请求失败怎么办？
**A:**
1. 检查后端S3配置是否完整
2. 查看后端日志的错误信息
3. 确保腾讯云COS认证信息正确

### Q: 分片上传还是失败？
**A:**
1. 检查ETag是否正确返回
2. 验证所有分片都成功上传
3. 查看后端合并接口的日志

## 后续优化建议

1. **性能优化**
   - 增大分片大小（从5MB到10-20MB）
   - 实现分片并发上传
   - 添加断点续传功能

2. **功能完善**
   - 添加上传暂停/取消
   - 实现上传进度持久化
   - 添加文件验证

3. **监控改进**
   - 添加上传成功率监控
   - 记录失败原因统计
   - 添加性能指标收集

---

**修复完成时间**: 2025-10-30  
**状态**: ✅ CORS问题已解决
