# 后端代码结构调整 ✅

## 📌 问题说明

原本的后端代码被放在了独立的 `playedu-exam` 模块中，但根据项目要求，应该将代码放在 `playedu-api/playedu-api` 项目中的 `controller/backend` 目录下。

---

## 🔧 调整方案

### 项目架构
```
playedu-api/
├── playedu-api/              ← 主服务项目
│   └── src/main/java/xyz/playedu/api/
│       ├── controller/
│       │   └── backend/      ← 后端API控制器放这里
│       │       ├── ExamController.java ✅ (新增)
│       │       └── ExamQuestionController.java ✅ (新增)
│       └── ...
├── playedu-exam/             ← 业务逻辑模块
│   └── src/main/java/xyz/playedu/exam/
│       ├── domain/           ← 数据模型
│       ├── mapper/           ← Mapper接口
│       ├── service/          ← Service业务逻辑
│       └── vo/               ← VO和Request对象
├── playedu-course/           ← 课程业务模块（参考）
├── playedu-resource/         ← 资源业务模块（参考）
└── ...
```

---

## ✅ 已完成的工作

### 1. Controller 层
#### ExamController
**位置**: `/playedu-api/src/main/java/xyz/playedu/api/controller/backend/ExamController.java`

**功能**:
- GET `/backend/v1/exams` - 考试列表（分页、搜索）
- POST `/backend/v1/exams` - 创建考试
- GET `/backend/v1/exams/{id}` - 获取考试详情
- PUT `/backend/v1/exams/{id}` - 修改考试
- DELETE `/backend/v1/exams/{id}` - 删除考试

**特点**:
- ✅ 使用 `@Log` 注解记录操作日志
- ✅ 使用 `JsonResponse` 统一响应格式
- ✅ 使用 `BCtx.getAdminUser()` 获取当前用户
- ✅ 使用 `BusinessTypeConstant` 定义操作类型

#### ExamQuestionController
**位置**: `/playedu-api/src/main/java/xyz/playedu/api/controller/backend/ExamQuestionController.java`

**功能**:
- GET `/backend/v1/exams/{examId}/questions` - 试题列表
- POST `/backend/v1/exams/{examId}/questions` - 创建试题
- GET `/backend/v1/questions/{id}` - 获取试题详情
- PUT `/backend/v1/questions/{id}` - 修改试题
- DELETE `/backend/v1/questions/{id}` - 删除试题
- POST `/backend/v1/exams/{examId}/import` - 导入试题
- GET `/backend/v1/exams/{examId}/import-logs` - 导入日志

### 2. VO 和 Request 对象
#### 在 playedu-exam 模块中

**ExamQuestionCreateRequest.java**
```java
- title: String (题目，必填)
- type: String (题型，必填)
- difficulty: String (难度，必填)
- score: Integer (分数，必填，1-100)
- analysis: String (解析，可选)
- answer: String (答案，可选)
- options: List<OptionData> (选项列表)
  - content: String (选项内容)
  - is_correct: Integer (是否正确答案)
```

**ExamQuestionUpdateRequest.java**
```java
- title: String (题目，必填)
- difficulty: String (难度，必填)
- score: Integer (分数，必填，1-100)
- analysis: String (解析，可选)
- answer: String (答案，可选)
- options: List<OptionData> (选项列表)
  - id: Long (选项ID)
  - content: String (选项内容)
  - is_correct: Integer (是否正确答案)
```

---

## 🏗️ 架构对比

### 原来的方式（错误）❌
```
playedu-exam/ (独立模块)
├── controller/
│   └── ExamController.java
└── ...
```

### 现在的方式（正确）✅
```
playedu-api/ (主服务)
├── controller/backend/
│   ├── ExamController.java
│   └── ExamQuestionController.java
└── ...

playedu-exam/ (业务逻辑模块)
├── domain/     ← 数据模型
├── mapper/     ← Mapper接口
├── service/    ← Service实现
└── vo/         ← VO对象
```

---

## 🔗 模块间的依赖关系

```
playedu-api
    ↓ (depends on)
playedu-exam, playedu-course, playedu-resource, playedu-common
    ↓ (depends on)
playedu-common
```

**说明**:
- `playedu-api` 是主服务项目，包含所有的 Controller
- `playedu-exam` 是业务逻辑模块，提供 Domain、Service、Mapper
- `playedu-api` 的 Controller 依赖于 `playedu-exam` 的 Service
- 所有模块都依赖 `playedu-common` 获取基础功能

---

## 📝 pom.xml 配置

### playedu-api 的 pom.xml
需要添加以下依赖：
```xml
<dependency>
    <groupId>xyz.playedu</groupId>
    <artifactId>playedu-exam</artifactId>
    <version>1.0</version>
</dependency>
```

### 主 pom.xml
确保包含所有模块：
```xml
<modules>
    <module>playedu-common</module>
    <module>playedu-api</module>
    <module>playedu-course</module>
    <module>playedu-exam</module>  <!-- 确保包含 -->
    <module>playedu-resource</module>
    <module>playedu-system</module>
</modules>
```

---

## 🚀 后续工作

### Phase 1: 完成 Service 和 Mapper 实现
- [ ] 实现 ExamQuestionService & ExamQuestionServiceImpl
- [ ] 实现 ExamImportService & ExamImportServiceImpl
- [ ] 创建 Mapper XML 文件
  - [ ] ExamMapper.xml
  - [ ] ExamQuestionMapper.xml
  - [ ] ExamQuestionOptionMapper.xml
  - [ ] ExamImportLogMapper.xml

### Phase 2: 完善 Controller 实现
- [ ] 完成 ExamQuestionController 中的 TODO 逻辑
- [ ] 添加权限验证 (@BackendPermission)
- [ ] 添加参数验证

### Phase 3: 数据库和权限配置
- [ ] 执行 SQL 建表脚本
- [ ] 在 admin_permissions 表中添加权限
- [ ] 在 admin_roles_permission 表中分配权限

---

## 📊 文件清单

### playedu-api/playedu-api 项目中
```
src/main/java/xyz/playedu/api/controller/backend/
├── ExamController.java ✅
└── ExamQuestionController.java ✅
```

### playedu-exam 模块中
```
src/main/java/xyz/playedu/exam/
├── domain/
│   ├── Exam.java
│   ├── ExamQuestion.java
│   ├── ExamQuestionOption.java
│   └── ExamImportLog.java
├── mapper/
│   ├── ExamMapper.java
│   ├── ExamQuestionMapper.java
│   ├── ExamQuestionOptionMapper.java
│   └── ExamImportLogMapper.java
├── service/
│   ├── ExamService.java
│   └── impl/ExamServiceImpl.java
└── vo/
    ├── ExamVo.java
    ├── ExamCreateRequest.java
    ├── ExamUpdateRequest.java ✅
    ├── ExamQuestionCreateRequest.java ✅
    └── ExamQuestionUpdateRequest.java ✅
```

---

## 💡 关键改进

### 1. 响应格式统一
从 `playedu-exam` 的 `{ code, msg, data }` 改为 `playedu-api` 的 `JsonResponse`

**原来**:
```java
return JsonResponse.data(result);
```

**现在**:
```java
return JsonResponse.data(result);  // 使用 common 模块中的 JsonResponse
```

### 2. 日志记录
添加 `@Log` 注解记录操作日志

```java
@Log(title = "考试-列表", businessType = BusinessTypeConstant.GET)
```

### 3. 用户上下文
从请求中获取当前用户信息

```java
Integer adminId = BCtx.getAdminUser().getId().intValue();
```

### 4. 权限验证
后续可以添加 `@BackendPermission` 注解进行权限控制

```java
@BackendPermission(slug = BPermissionConstant.EXAM_VIEW)
```

---

## 🔍 验证清单

- [x] Controller 位置正确（playedu-api/controller/backend/）
- [x] 使用正确的响应格式（JsonResponse）
- [x] 日志记录注解完整（@Log）
- [x] 使用 BCtx 获取当前用户
- [x] VO 和 Request 对象完整
- [ ] pom.xml 配置正确
- [ ] 权限配置完成
- [ ] Mapper XML 文件创建
- [ ] Service 方法实现

---

**最后更新**: 2025-10-30
**状态**: 后端代码结构调整完成 ✅
**下一步**: 完成 Service 实现和 Mapper XML 配置

