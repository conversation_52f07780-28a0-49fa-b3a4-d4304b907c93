# 考试测评菜单 - 快速参考卡

## 🎯 一句话总结
**配置已完成，重启服务+硬刷新浏览器即可看到菜单！**

---

## ⚡ 快速命令

```bash
# 1. 重启服务
cd /Users/<USER>/pa/codes/playedu/playedu-admin
npm run dev

# 2. 硬刷新 (Mac)
Cmd + Shift + R

# 3. 或者 Windows/Linux
Ctrl + Shift + F5
```

---

## 📍 修改位置速查

| 修改内容 | 文件 | 行号 | 改什么 |
|---------|------|------|--------|
| 菜单项 | `left-menu/index.tsx` | 59 | `getItem("考试测评", "/exam", null, null, null, null)` |
| 路由映射 | `left-menu/index.tsx` | 120 | `"^/exam": ["courses"]` |
| 导入页面 | `routes/index.tsx` | 30 | `const ExamPage = lazy(...)` |
| 路由配置 | `routes/index.tsx` | 125-127 | `{ path: "/exam", ... }` |
| 新建页面 | `pages/exam/index.tsx` | - | React 组件 |
| 新建样式 | `pages/exam/index.module.less` | - | LESS 样式 |

---

## 🔍 快速检查

```bash
# 检查菜单是否添加
grep "考试测评" playedu-admin/src/compenents/left-menu/index.tsx

# 检查路由是否添加
grep "path.*exam" playedu-admin/src/routes/index.tsx

# 检查页面文件是否存在
ls playedu-admin/src/pages/exam/
```

---

## 🧩 三种配置方案速览

| 需要什么 | 方案 | 步骤 | 时间 |
|---------|------|------|------|
| 只要菜单显示 | **B** | 已完成 ✅ | 0 分钟 |
| 参考课程功能 | **A** | 复制课程代码 | 30 分钟 |
| 完全自定义 | **C** | 从零开发 | 2-3 小时 |

---

## 🆘 菜单不显示？

1. ✅ 检查: 服务是否重启? (`npm run dev`)
2. ✅ 检查: 浏览器是否硬刷新? (`Cmd+Shift+R`)
3. ✅ 检查: 控制台是否有错误? (`F12`)
4. ✅ 检查: 文件是否存在? (`ls pages/exam/`)

---

## 📋 配置改动清单

- [x] 菜单项添加
- [x] 路由映射添加
- [x] ExamPage 导入
- [x] 路由配置
- [x] 页面文件创建
- [x] 样式文件创建
- [x] TypeScript 编译通过

---

## 💻 配置代码片段

### 菜单项
```tsx
getItem("考试测评", "/exam", null, null, null, null)
```

### 路由映射
```tsx
"^/exam": ["courses"]
```

### 页面导入
```tsx
const ExamPage = lazy(() => import("../pages/exam/index"));
```

### 路由配置
```tsx
{
  path: "/exam",
  element: <PrivateRoute Component={<ExamPage />} />,
}
```

---

## 📞 文档导航

- 🎯 **此文件** - 快速参考 (现在)
- 📖 `README_EXAM_MENU.md` - 完整指南
- 🔧 `EXAM_CONFIG_GUIDE.md` - 配置方案详解
- 📊 `CONFIG_COMPARISON.md` - 三种方案对比
- 📝 `CURRENT_CONFIG_STATUS.md` - 配置状态详情

---

## ✨ 预期效果

打开 http://localhost:9900 后:

1. ✅ 左侧菜单 → 课程中心
2. ✅ 看到两个子菜单:
   - 线上课
   - 考试测评 (新增)
3. ✅ 点击"考试测评"
4. ✅ URL 变为 `#/exam`
5. ✅ 页面显示"考试测评"标题

---

## 🎓 学到的配置要点

| 概念 | 说明 |
|------|------|
| 菜单权限 | 最后一个参数: `null` = 无限制, `"permission"` = 需要权限 |
| 路由映射 | `children2Parent` - URL 包含某个路径时自动展开菜单 |
| 懒加载 | `lazy()` 函数实现按需加载，减小首页包体积 |
| PrivateRoute | 路由保护组件，检查用户权限 |
| 动态导入 | `import("path")` 实现动态加载模块 |

---

## 🚀 立即开始

```
1️⃣ npm run dev
2️⃣ Cmd+Shift+R (Mac) 或 Ctrl+Shift+F5 (Win)
3️⃣ 点击菜单 → 课程中心 → 考试测评
4️⃣ 完成！
```

---

## 📌 记住这个

**如果菜单还是不显示，99% 的原因是浏览器缓存。**

解决方法: **硬刷新** (不是普通刷新!)

- Mac: `Cmd + Shift + R`
- Windows: `Ctrl + Shift + F5`
- 或在F12中右键刷新按钮选"清空缓存并硬刷新"

