#!/bin/bash
# wait-for-it.sh: Wait for a host/port to become available before executing a command.
# More info: https://github.com/vishnubob/wait-for-it

set -e

TIMEOUT=15
QUIET=0
HOST=
PORT=
CHILD_CMD=

usage() {
    cat << USAGE >&2
Usage:
    $0 host:port [-t timeout] [-- command args]
    -h HOST | --host=HOST       Host or IP under test
    -p PORT | --port=PORT       TCP port under test
    -t TIMEOUT | --timeout=TIMEOUT
                                Timeout in seconds, zero for no timeout
    -q | --quiet                Don't output any status messages
    -- COMMAND ARGS             Execute command with args after the test finishes
USAGE
    exit 1
}

wait_for() {
    if [ $QUIET -eq 0 ]; then
        echo "Waiting for $HOST:$PORT..."
    fi

    for i in `seq $TIMEOUT`; do
        nc -z "$HOST" "$PORT" > /dev/null 2>&1
        result=$?
        if [ $result -eq 0 ]; then
            if [ $QUIET -eq 0 ]; then
                echo "Host $HOST is available on port $PORT - executing command"
            fi
            exec "$@"
        fi
        sleep 1
    done

    echo "Operation timed out" >&2
    exit 1
}

while [ $# -gt 0 ]
do
    case "$1" in
        *:* )
        HOST=$(printf "%s\n" "$1"| cut -d : -f 1)
        PORT=$(printf "%s\n" "$1"| cut -d : -f 2)
        shift 1
        ;;
        -q | --quiet)
        QUIET=1
        shift 1
        ;;
        -h)
        HOST="$2"
        if [ "$HOST" = "" ]; then break; fi
        shift 2
        ;;
        --host=*)
        HOST=$(printf "%s\n" "$1"| sed -e 's/^--host=//')
        shift 1
        ;;
        -p)
        PORT="$2"
        if [ "$PORT" = "" ]; then break; fi
        shift 2
        ;;
        --port=*)
        PORT=$(printf "%s\n" "$1"| sed -e 's/^--port=//')
        shift 1
        ;;
        -t)
        TIMEOUT="$2"
        if [ "$TIMEOUT" = "" ]; then break; fi
        shift 2
        ;;
        --timeout=*)
        TIMEOUT=$(printf "%s\n" "$1"| sed -e 's/^--timeout=//')
        shift 1
        ;;
        --)
        shift
        CHILD_CMD=("$@")
        break
        ;;
        -*)
        usage
        ;;
        *)
        usage
        ;;
    esac
done

if [ "$HOST" = "" ] || [ "$PORT" = "" ]; then
    usage
fi

if [ ${#CHILD_CMD[@]} -gt 0 ]; then
    wait_for "${CHILD_CMD[@]}"
else
    wait_for
fi
