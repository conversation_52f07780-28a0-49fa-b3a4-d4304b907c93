# 课件上传架构与数据流

## 整体架构

```
┌─────────────────────────────────────────────────────────────────────┐
│                       浏览器 (Admin前端)                            │
│  playedu-admin                                                      │
└──────────────────┬──────────────────────────────────────────────────┘
                   │
                   │ 跨域请求
                   │ (CORS问题)
                   ↓
┌─────────────────────────────────────────────────────────────────────┐
│                    后端代理 (UploadProxyController)                 │
│  /backend/v1/upload/cos-proxy/                                      │
│  - /upload (分片上传)                                                │
│  - /minio/upload-id (初始化)                                        │
│  - /minio/pre-sign-url (获取预签名URL)                             │
│  - /minio/merge-file (合并)                                         │
└──────────────────┬──────────────────────────────────────────────────┘
                   │
                   │ 同源请求
                   │ (解决CORS)
                   ↓
┌─────────────────────────────────────────────────────────────────────┐
│               腾讯云 COS (存储服务)                                  │
│  COSClient SDK                                                      │
│  - InitiateMultipartUpload                                         │
│  - UploadPart                                                       │
│  - CompleteMultipartUpload                                          │
└─────────────────────────────────────────────────────────────────────┘
```

## 完整的分片上传流程

### 步骤 1: 初始化分片上传

```
前端: 调用 minioUploadId(extension)
  ↓
URL: GET /backend/v1/upload/cos-proxy/minio/upload-id?extension=ppt
  ↓
后端: 调用 COSClient.initiateMultipartUpload()
  ↓
响应:
{
  "data": {
    "uploadId": "abc123xyz",
    "objectKey": "uploads/2024/10/30/uuid/file.ppt",
    "resource_type": "PPT"
  }
}
  ↓
前端: 保存 uploadId 和 objectKey
```

### 步骤 2: 获取分片预签名URL

```
前端: 调用 minioPreSignUrl(uploadId, filename, partNumber)
  ↓
URL: GET /backend/v1/upload/cos-proxy/minio/pre-sign-url?
       upload_id=abc123xyz&filename=path&part_number=1
  ↓
后端: 调用 COSClient.generatePresignedUrl()
  ↓
响应:
{
  "data": {
    "url": "https://bucket.cos.region.myqcloud.com/path?uploadId=...&partNumber=1&...",
    "objectKey": "uploads/2024/10/30/uuid/file.ppt"
  }
}
  ↓
前端: 获得预签名URL用于下一步
```

### 步骤 3: 上传分片数据

```
前端: 创建分片数据
  const chunkData = file.slice(start, start + 5MB)
  
  调用代理: PUT /backend/v1/upload/cos-proxy/upload?
           filename=path&content-type=...&upload_id=abc123xyz&part_number=1
  
  Request Body: 分片数据 (二进制)
  Request Headers: {
    "X-Original-COS-URL": "https://bucket.cos.region.myqcloud.com/path?uploadId=..."
  }
  ↓
后端 (UploadProxyController.uploadFile):
  1. 解析参数：upload_id, part_number
  2. 从Request Header获取原始COS URL
  3. 从Body读取分片数据
  4. 创建 UploadPartRequest
  5. 调用 cosClient.uploadPart()
  ↓
COS返回: ETag (如: "abc123def456")
  ↓
后端响应:
{
  "data": {
    "objectKey": "uploads/2024/10/30/uuid/file.ppt",
    "partNumber": 1,
    "etag": "\"abc123def456\"",
    "status": "success"
  }
}
  ↓
前端: 保存 ETag 到 partETags Map
     分块 1: "abc123def456"
     分块 2: "xyz789abc123"
     分块 3: "......"
```

### 步骤 4: 完成分片合并

```
前端: 所有分片上传完成后
  构建 parts 数组:
  [
    { partNumber: 1, etag: "abc123def456" },
    { partNumber: 2, etag: "xyz789abc123" },
    { partNumber: 3, etag: "......" }
  ]
  
  调用合并接口: POST /backend/v1/upload/cos-proxy/minio/merge-file
  Request Body: {
    "filename": "uploads/2024/10/30/uuid/file.ppt",
    "upload_id": "abc123xyz",
    "original_filename": "file.ppt",
    "category_ids": "1,2,3",
    "size": 15728640,
    "extension": "ppt",
    "parts": [
      { "partNumber": 1, "etag": "abc123def456" },
      { "partNumber": 2, "etag": "xyz789abc123" }
    ]
  }
  ↓
后端 (UploadProxyController.mergeFile):
  1. 解析参数：uploadId, parts
  2. 创建 List<PartETag>
  3. 创建 CompleteMultipartUploadRequest
  4. 调用 cosClient.completeMultipartUpload()
  ↓
COS返回: 完成的对象信息
  ↓
后端响应:
{
  "data": {
    "objectKey": "uploads/2024/10/30/uuid/file.ppt",
    "accessUrl": "https://bucket.cos.region.myqcloud.com/uploads/...",
    "status": "success"
  }
}
  ↓
前端: 显示成功提示，文件上传完成
```

## 关键类和方法说明

### 后端关键类

#### 1. UploadProxyController
```java
@RestController
@RequestMapping("/backend/v1/upload/cos-proxy")
@CrossOrigin(...)  // 允许跨域
public class UploadProxyController {
    
    // 主上传接口 - 支持分片上传
    @PutMapping("/upload")
    public ResponseEntity<?> uploadFile(
        @RequestParam("filename") String filename,
        @RequestParam("content-type") String contentType,
        @RequestParam(value = "upload_id", required = false) String uploadId,
        @RequestParam(value = "part_number", required = false) Integer partNumber,
        @RequestHeader(value = "X-Original-COS-URL", required = false) String originalCosUrl,
        HttpServletRequest request)
    
    // 私有方法：处理分片上传
    private ResponseEntity<?> handleMultipartUpload(...)
    
    // 私有方法：处理单文件上传
    private ResponseEntity<?> handleSimpleUpload(...)
    
    // 获取分片上传ID
    @PostMapping("/minio/upload-id")
    public ResponseEntity<?> getUploadId(...)
    
    // 获取预签名URL
    @PostMapping("/minio/pre-sign-url")
    public ResponseEntity<?> getPreSignUrl(...)
    
    // 合并分片
    @PostMapping("/minio/merge-file")
    public ResponseEntity<?> mergeFile(...)
}
```

#### 2. 腾讯云 COS 相关类
```java
// 创建分片上传请求
UploadPartRequest uploadPartRequest = new UploadPartRequest();
uploadPartRequest.setBucketName(bucket);
uploadPartRequest.setKey(objectKey);
uploadPartRequest.setUploadId(uploadId);
uploadPartRequest.setPartNumber(partNumber);
uploadPartRequest.setInputStream(inputStream);
uploadPartRequest.setPartSize(fileSize);

// 执行上传并获取结果
UploadPartResult result = cosClient.uploadPart(uploadPartRequest);
String etag = result.getETag();  // 获取 ETag
```

### 前端关键类

#### 1. UploadChunk
```typescript
export class UploadChunk {
    // 存储分片信息
    partETags: Map<number, string>;  // 分片号 → ETag
    
    // 开始上传
    start(): void
    
    // 获取所有分片信息
    getPartETags(): Array<{partNumber: number, etag: string}>
    
    // 事件回调
    on(event: 'success' | 'error' | 'progress', handler: Function)
}
```

#### 2. UploadCoursewareButton
```typescript
export const UploadCoursewareButton = (props: {
    categoryIds: number[],
    onUpdate: () => void
}) => {
    // 上传成功回调
    item.upload.handler.on("success", () => {
        // 获取所有分片的ETag
        const partETags = item.upload.handler.getPartETags();
        
        // 调用合并接口
        minioMergeVideo(...)
    });
}
```

## 数据模型

### 分片上传请求

```typescript
interface UploadPartRequest {
    filename: string;           // 对象键
    'content-type': string;     // 内容类型
    'upload_id': string;        // 分片上传ID
    'part_number': number;      // 分片号
}

// Header中包含
'X-Original-COS-URL': string;   // 腾讯云预签名URL
```

### 分片上传响应

```typescript
interface UploadPartResponse {
    data: {
        objectKey: string;      // 对象键
        partNumber: number;     // 分片号
        etag: string;          // ETag值
        status: 'success';     // 状态
        message: string;       // 消息
    }
}
```

### 合并请求

```typescript
interface MergeRequest {
    filename: string;               // 对象键
    upload_id: string;              // 分片上传ID
    original_filename: string;      // 原始文件名
    category_ids: string;           // 分类ID
    size: number;                   // 文件大小
    extension: string;              // 文件扩展名
    parts: Array<{                  // 分片列表
        partNumber: number;         // 分片号
        etag: string;              // ETag
    }>;
}
```

## 错误处理

### 后端错误处理

```java
try {
    // 上传逻辑
    cosClient.uploadPart(uploadPartRequest);
} catch (CosServiceException e) {
    // COS服务端错误
    return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
        .body(JsonResponse.error("COS错误: " + e.getMessage()));
} catch (CosClientException e) {
    // 客户端错误
    return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
        .body(JsonResponse.error("上传失败: " + e.getMessage()));
}
```

### 前端错误处理

```typescript
item.upload.handler.on("error", (msg: string) => {
    // msg: "上传失败: 网络连接错误" 等
    item.upload.status = 5;  // 失败状态
    item.upload.remark = msg;
    setFileList([...localFileList.current]);
    message.error(msg);
});
```

## 性能考虑

### 分片大小
- **默认：** 5MB
- **优化建议：**
  - 网络好：可增大到 10-50MB
  - 网络差：可减小到 1-2MB

### 并发上传
- **当前实现：** 顺序上传（一次上传一个分片）
- **优化空间：** 可改为并发上传（同时上传多个分片）

### 超时设置
```typescript
this.client = axios.create({
    timeout: 60000,  // 60秒超时
    withCredentials: false,
});
```

## 测试检查清单

- [ ] 后端能否正确解析所有请求参数
- [ ] 后端能否正确调用腾讯云SDK
- [ ] 后端能否正确返回ETag
- [ ] 前端能否正确接收ETag
- [ ] 前端能否正确收集所有ETag
- [ ] 后端能否正确合并分片
- [ ] 前端能否正确显示上传进度
- [ ] 前端能否正确处理上传错误
- [ ] 能否在网络中断时正确处理
- [ ] 能否正确支持大文件上传
