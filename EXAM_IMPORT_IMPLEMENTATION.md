# 试题导入功能实施计划

## 📋 功能概述

实现Excel批量导入试题功能，支持单选、多选、填空、论述四种题型，包含完整的数据验证和导入日志。

---

## 🔄 实施流程

```
前端UI设计
   ↓
文件选择+上传
   ↓
后端接收文件
   ↓
Excel解析
   ↓
逐行数据验证
   ↓
创建题目记录
   ↓
记录导入日志
   ↓
返回结果给前端
   ↓
前端显示结果
```

---

## 📌 实施步骤

### 后端实施 (Java Spring Boot)

#### 第1步：添加依赖
```xml
<!-- pom.xml -->
<dependency>
    <groupId>org.apache.poi</groupId>
    <artifactId>poi-ooxml</artifactId>
    <version>5.0.0</version>
</dependency>
```

#### 第2步：创建导入数据模型
- ExamImportRequest (导入请求)
- ExamImportResult (导入结果)
- ExamImportError (导入错误)
- QuestionData (单条题目数据)

#### 第3步：创建导入验证类
- ExamQuestionValidator (题目数据验证)
- ExcelParser (Excel解析)

#### 第4步：创建导入Service
- ExamImportService (导入业务逻辑)
- 包含: 解析、验证、创建题目、记录日志

#### 第5步：创建Controller
- ExamImportController
- 暴露 POST /backend/v1/exams/{id}/import 接口

### 前端实施 (React)

#### 第1步：创建导入Modal组件
- 文件选择
- 上传按钮
- 进度显示

#### 第2步：创建导入结果展示组件
- 成功/失败统计
- 错误详情列表
- 重试功能

#### 第3步：集成到题目管理页面
- 在页面顶部添加"导入"按钮
- 点击显示导入Modal

#### 第4步：添加导入API调用
- 上传文件
- 显示进度
- 处理响应

---

## 📊 数据流设计

### 1. Excel文件格式
```
第一行: 表头 (题目|题型|难度|分数|选项A|选项B|选项C|选项D|正确答案|解析)
第二行+: 题目数据
```

### 2. 后端处理流程
```
接收Excel文件 → 创建导入任务 → 解析Excel
  ↓
逐行处理:
  ├─ 验证必填字段
  ├─ 验证题型
  ├─ 验证难度
  ├─ 验证分数
  ├─ 验证选项
  ├─ 验证正确答案
  └─ 创建题目记录

记录导入日志 → 返回结果
```

### 3. 验证规则
- 题目: 1-255字符, 必填
- 题型: 单选/多选/填空/论述, 必填
- 难度: 简单/中等/困难, 必填
- 分数: 1-100整数, 必填
- 选项: 单选/多选题必填4个, 填空/论述题留空
- 正确答案: 必填, 格式根据题型检查

---

## 💾 数据库操作

### 需要的操作
1. 插入 exam_questions (题目表)
2. 若是选择题，插入 exam_question_options (选项表)
3. 插入 exam_import_logs (导入日志表)
4. 可选：更新 exams 表的 total_score

---

## 📈 API 设计

### 导入接口
```
POST /backend/v1/exams/{id}/import
Content-Type: multipart/form-data

请求:
{
  file: File (Excel文件)
}

响应:
{
  code: 0,
  msg: "导入成功",
  data: {
    import_log_id: 123,
    total_count: 50,
    success_count: 48,
    error_count: 2,
    errors: [
      { row: 10, field: "题型", message: "无效的题型: 判断题" },
      { row: 15, field: "分数", message: "分数必须是1-100之间的整数" }
    ]
  }
}
```

### 导入日志查询接口
```
GET /backend/v1/exams/{id}/import-logs

响应:
{
  code: 0,
  data: [
    {
      id: 1,
      exam_id: 1,
      file_name: "questions.xlsx",
      total_count: 50,
      success_count: 48,
      error_count: 2,
      status: 1,
      created_at: "2025-10-30 20:00:00"
    }
  ]
}
```

---

## 🎯 前端功能

### 导入页面功能清单
- [ ] 文件选择器
- [ ] 上传进度条
- [ ] 导入结果显示
- [ ] 成功/失败统计
- [ ] 错误详情表格
- [ ] 重试功能
- [ ] 模板下载

---

## 🔐 权限控制

需要在 AdminPermission 表添加权限:
```
exam-import - 导入题目
```

在Controller中检查权限:
```java
@CheckPermission("exam-import")
public ResponseEntity<?> importQuestions(...) { }
```

---

## ✅ 测试场景

### 成功场景
1. 导入标准格式的Excel
2. 各种题型都成功导入

### 失败场景
1. 题型错误
2. 分数超出范围
3. 选项缺失
4. 正确答案格式错误
5. 文件格式错误 (非xlsx)

### 部分失败
1. 某些行失败，其他行成功
2. 正确返回成功数和失败数

---

## 📝 实施时间预估

| 环节 | 时间 |
|------|------|
| 后端Service | 2小时 |
| 后端Controller | 1小时 |
| 后端测试 | 1小时 |
| 前端组件 | 2小时 |
| 前端集成 | 1小时 |
| 前端测试 | 1小时 |
| **总计** | **8小时** |

---

## 📚 相关文件位置

后端:
- `playedu-api/playedu-api/src/main/java/xyz/playedu/api/controller/backend/ExamImportController.java`
- `playedu-api/playedu-api/src/main/java/xyz/playedu/api/service/ExamImportService.java`
- `playedu-api/playedu-api/src/main/java/xyz/playedu/api/service/ExamQuestionImportValidator.java`

前端:
- `playedu-admin/src/pages/exam/compenents/import-modal.tsx`
- `playedu-admin/src/api/exam.ts`

