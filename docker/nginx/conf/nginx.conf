client_max_body_size 500m;

server {
	listen 9800;
	server_name _;
	root /app/pc;
	index index.html;

    gzip on;
    gzip_static on;
    gzip_buffers 4 16k;
    gzip_http_version 1.1;
    gzip_comp_level 5;
    gzip_types text/plain application/javascript text/css application/xml text/javascript;
    gzip_vary on;

	location /api/ {
	    proxy_set_header X-Real-IP $remote_addr;
	    proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
	    proxy_pass http://127.0.0.1:9898/;
	}

    location ~* ^/(?![api].*) {
        try_files $uri /index.html;
    }
}

server {
	listen 9801;
	server_name _;
	root /app/h5;
	index index.html;
    
    gzip on;
    gzip_static on;
    gzip_buffers 4 16k;
    gzip_http_version 1.1;
    gzip_comp_level 5;
    gzip_types text/plain application/javascript text/css application/xml text/javascript;
    gzip_vary on;

	location /api/ {
	    proxy_set_header X-Real-IP $remote_addr;
	    proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
	    proxy_pass http://127.0.0.1:9898/;
	}

    location ~* ^/(?![api].*) {
        try_files $uri /index.html;
    }
}

server {
	listen 9900;
	server_name _;
	root /app/admin;
	index index.html;
    
    gzip on;
    gzip_static on;
    gzip_buffers 4 16k;
    gzip_http_version 1.1;
    gzip_comp_level 5;
    gzip_types text/plain application/javascript text/css application/xml text/javascript;
    gzip_vary on;

	location /api/ {
	    proxy_set_header X-Real-IP $remote_addr;
	    proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
	    proxy_pass http://127.0.0.1:9898/;
	}

    location ~* ^/(?![api].*) {
        try_files $uri /index.html;
    }
}