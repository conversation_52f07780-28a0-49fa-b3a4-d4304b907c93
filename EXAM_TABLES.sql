-- ==========================================
-- 考试测评系统数据表
-- ==========================================

-- 1. 考试表
DROP TABLE IF EXISTS `exams`;
CREATE TABLE `exams` (
  `id` int unsigned NOT NULL AUTO_INCREMENT COMMENT '主键',
  `title` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '考试名称',
  `description` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci COMMENT '考试描述',
  `type` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'normal' COMMENT '考试类型[normal:普通,timed:限时]',
  `duration` int NOT NULL DEFAULT 60 COMMENT '考试时长(分钟)',
  `pass_score` int NOT NULL DEFAULT 60 COMMENT '及格分数(百分比)',
  `total_score` int NOT NULL DEFAULT 100 COMMENT '总分',
  `status` tinyint NOT NULL DEFAULT 1 COMMENT '状态[1:启用,0:禁用]',
  `show_score` tinyint NOT NULL DEFAULT 1 COMMENT '是否显示分数[1:显示,0:隐藏]',
  `show_answer` tinyint NOT NULL DEFAULT 1 COMMENT '是否显示答案[1:显示,0:隐藏]',
  `admin_id` int NOT NULL DEFAULT 0 COMMENT '创建管理员ID',
  `admin_name` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '创建管理员名称',
  `start_time` timestamp NULL COMMENT '考试开始时间',
  `end_time` timestamp NULL COMMENT '考试结束时间',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NULL DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
  `deleted_at` timestamp NULL DEFAULT NULL COMMENT '删除时间',
  PRIMARY KEY (`id`),
  KEY `status` (`status`),
  KEY `created_at` (`created_at`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='考试表';

-- 2. 考试题目表
DROP TABLE IF EXISTS `exam_questions`;
CREATE TABLE `exam_questions` (
  `id` int unsigned NOT NULL AUTO_INCREMENT COMMENT '主键',
  `exam_id` int NOT NULL DEFAULT 0 COMMENT '考试ID',
  `title` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '题目标题',
  `type` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'single_choice' COMMENT '题型[single_choice:单选,multiple_choice:多选,fill_blank:填空,essay:论述]',
  `difficulty` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'medium' COMMENT '难度[easy:简单,medium:中等,hard:困难]',
  `score` int NOT NULL DEFAULT 1 COMMENT '分数',
  `sort` int NOT NULL DEFAULT 0 COMMENT '题目顺序',
  `analysis` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci COMMENT '答案解析',
  `status` tinyint NOT NULL DEFAULT 1 COMMENT '状态[1:启用,0:禁用]',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NULL DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
  PRIMARY KEY (`id`),
  KEY `exam_id` (`exam_id`),
  KEY `type` (`type`),
  KEY `sort` (`sort`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='考试题目表';

-- 3. 题目选项表
DROP TABLE IF EXISTS `exam_question_options`;
CREATE TABLE `exam_question_options` (
  `id` int unsigned NOT NULL AUTO_INCREMENT COMMENT '主键',
  `question_id` int NOT NULL DEFAULT 0 COMMENT '题目ID',
  `content` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '选项内容',
  `option_key` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '选项键[A,B,C,D,E,F...]',
  `is_correct` tinyint NOT NULL DEFAULT 0 COMMENT '是否正确答案[1:是,0:否]',
  `sort` int NOT NULL DEFAULT 0 COMMENT '排序',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`),
  KEY `question_id` (`question_id`),
  KEY `option_key` (`option_key`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='考试题目选项表';

-- 4. 考试分配表
DROP TABLE IF EXISTS `exam_department_user`;
CREATE TABLE `exam_department_user` (
  `id` int unsigned NOT NULL AUTO_INCREMENT COMMENT '主键',
  `exam_id` int NOT NULL DEFAULT 0 COMMENT '考试ID',
  `range_id` int NOT NULL DEFAULT 0 COMMENT '指派范围ID',
  `range_type` tinyint NOT NULL DEFAULT 0 COMMENT '指派范围类型[0:部门,1:学员]',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `unique_exam_range` (`exam_id`, `range_id`, `range_type`),
  KEY `exam_id` (`exam_id`),
  KEY `range_id` (`range_id`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='考试指派范围表';

-- 5. 学生考试记录表
DROP TABLE IF EXISTS `exam_user_records`;
CREATE TABLE `exam_user_records` (
  `id` int unsigned NOT NULL AUTO_INCREMENT COMMENT '主键',
  `exam_id` int NOT NULL DEFAULT 0 COMMENT '考试ID',
  `user_id` int NOT NULL DEFAULT 0 COMMENT '学生ID',
  `user_name` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '学生名称',
  `user_email` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '学生邮箱',
  `status` tinyint NOT NULL DEFAULT 0 COMMENT '状态[0:未开始,1:进行中,2:已完成,3:已过期]',
  `total_score` int NOT NULL DEFAULT 0 COMMENT '总分',
  `score` int DEFAULT NULL COMMENT '得分',
  `pass_status` tinyint DEFAULT NULL COMMENT '是否通过[1:通过,0:未通过,null:未考]',
  `start_at` timestamp NULL COMMENT '开始时间',
  `end_at` timestamp NULL COMMENT '结束时间',
  `duration` int DEFAULT 0 COMMENT '花费时间(秒)',
  `answer_count` int NOT NULL DEFAULT 0 COMMENT '答题数',
  `correct_count` int NOT NULL DEFAULT 0 COMMENT '正确数',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NULL DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `unique_exam_user` (`exam_id`, `user_id`),
  KEY `exam_id` (`exam_id`),
  KEY `user_id` (`user_id`),
  KEY `status` (`status`),
  KEY `created_at` (`created_at`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='学生考试记录表';

-- 6. 学生答题记录表
DROP TABLE IF EXISTS `exam_user_answers`;
CREATE TABLE `exam_user_answers` (
  `id` int unsigned NOT NULL AUTO_INCREMENT COMMENT '主键',
  `exam_record_id` int NOT NULL DEFAULT 0 COMMENT '考试记录ID',
  `exam_id` int NOT NULL DEFAULT 0 COMMENT '考试ID',
  `user_id` int NOT NULL DEFAULT 0 COMMENT '学生ID',
  `question_id` int NOT NULL DEFAULT 0 COMMENT '题目ID',
  `question_type` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '题型',
  `user_answer` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci COMMENT '学生答案',
  `correct_answer` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci COMMENT '正确答案',
  `is_correct` tinyint DEFAULT NULL COMMENT '是否正确[1:正确,0:错误,null:未批改]',
  `score` int DEFAULT NULL COMMENT '得分',
  `answer_time` int NOT NULL DEFAULT 0 COMMENT '答题耗时(秒)',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`),
  KEY `exam_record_id` (`exam_record_id`),
  KEY `user_id` (`user_id`),
  KEY `question_id` (`question_id`),
  KEY `is_correct` (`is_correct`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='学生答题记录表';

-- 7. 题目导入日志表
DROP TABLE IF EXISTS `exam_import_logs`;
CREATE TABLE `exam_import_logs` (
  `id` int unsigned NOT NULL AUTO_INCREMENT COMMENT '主键',
  `exam_id` int NOT NULL DEFAULT 0 COMMENT '考试ID',
  `file_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '文件名',
  `file_path` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '文件路径',
  `total_count` int NOT NULL DEFAULT 0 COMMENT '总条数',
  `success_count` int NOT NULL DEFAULT 0 COMMENT '成功条数',
  `error_count` int NOT NULL DEFAULT 0 COMMENT '错误条数',
  `status` tinyint NOT NULL DEFAULT 1 COMMENT '状态[1:成功,0:失败]',
  `error_message` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci COMMENT '错误信息',
  `admin_id` int NOT NULL DEFAULT 0 COMMENT '操作管理员ID',
  `admin_name` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '操作管理员名称',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`),
  KEY `exam_id` (`exam_id`),
  KEY `created_at` (`created_at`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='题目导入日志表';

