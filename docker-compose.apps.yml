version: '3.8'

# 这个 docker-compose 文件只用于部署和管理应用服务
# 它会连接到由原始 compose.yml 文件创建的外部网络 (playedu_playedu)
# 从而访问正在运行的 MySQL 容器

services:
  api:
    container_name: playedu-api
    build:
      context: .
      dockerfile: playedu-api/Dockerfile
    image: playedu-api:latest
    restart: always
    environment:
      - DB_HOST=mysql # 'mysql' 是 MySQL 容器在 playedu_playedu 网络中的服务名
      - DB_PORT=3306
      - DB_NAME=playedu
      - DB_USER=root
      - DB_PASS=playeduxyz
      - SA_TOKEN_IS_CONCURRENT=false
      - SA_TOKEN_JWT_SECRET_KEY=playeduxyz
    ports:
      - "9700:9898"
    networks:
      - playedu_network

  admin:
    container_name: playedu-admin
    build:
      context: .
      dockerfile: Dockerfile.admin
    image: playedu-admin:latest
    restart: always
    ports:
      - "9900:80"
    networks:
      - playedu_network
    depends_on:
      - api

  pc:
    container_name: playedu-pc
    build:
      context: .
      dockerfile: Dockerfile.pc
    image: playedu-pc:latest
    restart: always
    ports:
      - "9800:80"
    networks:
      - playedu_network
    depends_on:
      - api

  h5:
    container_name: playedu-h5
    build:
      context: .
      dockerfile: Dockerfile.h5
    image: playedu-h5:latest
    restart: always
    ports:
      - "9801:80"
    networks:
      - playedu_network
    depends_on:
      - api

networks:
  playedu_network:
    name: playedu_playedu # 这里指定了要连接的外部网络的名称
    external: true
