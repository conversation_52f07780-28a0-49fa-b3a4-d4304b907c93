# Courseware 上传跨域解决方案

## 问题描述
Admin服务的courseware上传文件遇到了跨域（CORS）问题。原因是前端直接向腾讯云COS上传文件时，浏览器会发送CORS预检请求，而COS可能未正确配置CORS策略，导致上传失败。

## 解决方案
使用 **UploadProxyController** 作为中间代理，将前端的上传请求转发到腾讯云COS，从而绕过跨域问题。

## 上传流程

### 1. 初始化阶段
```
前端 → 后端 /backend/v1/upload/cos-proxy/minio/upload-id
← 获取 uploadId 和 objectKey
```

### 2. 分块上传阶段
```
前端 → 后端 /backend/v1/upload/cos-proxy/upload (代理请求)
       ↓
      后端 → 腾讯云 COS (使用服务器端认证)
       ↓
      后端 → 前端 (返回 ETag)
```

**关键参数：**
- `filename`: 对象键（object key）
- `content-type`: 内容类型
- `upload_id`: 从初始化阶段获取
- `part_number`: 当前分片号
- `X-Original-COS-URL`: 预签名URL（作为header传递）

**代理返回：**
```json
{
  "data": {
    "objectKey": "uploads/2024/10/30/uuid/file.ppt",
    "partNumber": 1,
    "etag": "\"abc123def456\"",
    "status": "success",
    "message": "分片上传成功"
  }
}
```

### 3. 合并阶段
```
前端 → 后端 /backend/v1/upload/cos-proxy/minio/merge-file
  ↓ (包含所有分片的 ETag 信息)
后端 → 腾讯云 COS (CompleteMultipartUpload)
  ↓
后端 → 前端 (返回访问URL)
```

## 代码修改

### 后端修改
**文件：** `playedu-api/playedu-api/src/main/java/xyz/playedu/api/controller/backend/UploadProxyController.java`

#### 主要改动：
1. `/upload` 接口改为支持分片上传
   - 添加 `upload_id` 和 `part_number` 参数
   - 使用 `UploadPartRequest` 替代 `PutObjectRequest`
   - 返回 `ETag` 用于后续合并

2. 新增两个私有方法：
   - `handleMultipartUpload()`: 处理分片上传
   - `handleSimpleUpload()`: 处理单文件上传

3. 分片上传流程：
```java
UploadPartRequest uploadPartRequest = new UploadPartRequest();
uploadPartRequest.setBucketName(s3Config.getBucket());
uploadPartRequest.setKey(objectKey);
uploadPartRequest.setUploadId(uploadId);
uploadPartRequest.setPartNumber(partNumber);
uploadPartRequest.setInputStream(inputStream);
uploadPartRequest.setPartSize(fileData.length);

UploadPartResult uploadPartResult = cosClient.uploadPart(uploadPartRequest);
```

### 前端修改
**文件1：** `playedu-admin/src/js/minio-upload-chunk.ts`

#### 主要改动：
1. 添加 `partETags` Map 存储每个分片的 ETag
2. 修改请求逻辑，将所有参数作为查询参数传递：
```typescript
const params = new URLSearchParams({
  filename: this.filename,
  'content-type': 'application/octet-stream',
  'upload_id': this.uploadId,
  'part_number': this.chunkIndex.toString()
});

return this.client.put(proxyUrl + '?' + params.toString(), tmpFile, {
  headers: {
    "X-Original-COS-URL": cosUrl,
  },
});
```

3. 从代理响应中提取并保存 ETag：
```typescript
if (response.data && response.data.data && response.data.data.etag) {
  const etag = response.data.data.etag;
  this.partETags.set(partNumber, etag);
}
```

4. 新增 `getPartETags()` 方法返回所有分片信息

**文件2：** `playedu-admin/src/compenents/upload-courseware-button/index.tsx`

#### 主要改动：
1. 上传完成后获取所有 ETag 信息
2. 构建 parts 数组传递给合并接口
3. 添加成功/失败提示

## 腾讯云 COS SDK 关键 API

参考：[腾讯云 COS 上传对象文档](https://www.tencentcloud.com/zh/document/product/436/44015#.E4.B8.8A.E4.BC.A0.E6.9C.AC.E5.9C.B0.E6.96.87.E4.BB.B6)

### 分块上传三步骤

1. **初始化分块上传** - `InitiateMultipartUpload`
   - 获得 `uploadId`

2. **上传分块** - `UploadPart`
   - 需要 `uploadId` 和 `partNumber`
   - 返回 `ETag`

3. **完成分块上传** - `CompleteMultipartUpload`
   - 需要 `uploadId` 和所有分块的 `PartETag` 列表
   - `PartETag` 包含 `partNumber` 和 `eTag`

## 测试步骤

1. **启动服务**
   ```bash
   # 前端服务
   cd playedu-admin
   npm start
   
   # 后端服务
   cd playedu-api
   mvn spring-boot:run
   ```

2. **进行课件上传测试**
   - 登录后台管理系统
   - 进入资源 → 课件
   - 点击"上传课件"按钮
   - 选择支持的文件格式（Word、Excel、PPT、PDF、TXT、RAR、ZIP）
   - 观察上传进度和结果

3. **检查日志**
   - 后端日志会输出：
     - 获取 uploadId 的信息
     - 每个分片上传的详细信息
     - 分片合并的过程
     - 最终访问 URL

## 常见问题

### 1. 跨域错误仍然存在
- 检查后端 CORS 配置
- 确保 `@CrossOrigin` 注解正确配置了允许的方法和 header

### 2. ETag 为空
- 检查腾讯云 COS 是否正确返回了 ETag
- 查看后端日志中的 ETag 值

### 3. 合并失败
- 确保所有分片都已成功上传
- 检查 PartETag 列表是否完整
- 验证 uploadId 是否有效

## 相关文件
- 后端控制器：`playedu-api/playedu-api/src/main/java/xyz/playedu/api/controller/backend/UploadProxyController.java`
- 前端上传类：`playedu-admin/src/js/minio-upload-chunk.ts`
- 前端按钮组件：`playedu-admin/src/compenents/upload-courseware-button/index.tsx`
- 前端 API：`playedu-admin/src/api/upload.ts`
