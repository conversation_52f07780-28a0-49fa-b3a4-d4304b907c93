# 考试测评系统架构总结

## 🎯 系统概览

```
┌─────────────────────────────────────────────┐
│           考试测评管理系统                   │
├─────────────────────────────────────────────┤
│  前端 (React)                               │
│  ├─ 考试管理页面                            │
│  ├─ 题目管理页面                            │
│  ├─ Excel导入页面                           │
│  ├─ 成绩查询页面                            │
│  └─ 学生考试页面                            │
├─────────────────────────────────────────────┤
│  后端 (Java Spring Boot)                     │
│  ├─ ExamController - 考试管理API            │
│  ├─ QuestionController - 题目管理API        │
│  ├─ ImportController - 导入服务API          │
│  ├─ RecordController - 成绩管理API          │
│  └─ AnswerController - 答题服务API          │
├─────────────────────────────────────────────┤
│  数据库 (MySQL)                              │
│  ├─ exams - 考试表                          │
│  ├─ exam_questions - 题目表                 │
│  ├─ exam_question_options - 选项表          │
│  ├─ exam_user_records - 成绩记录表          │
│  ├─ exam_user_answers - 答题记录表          │
│  ├─ exam_department_user - 分配表           │
│  └─ exam_import_logs - 导入日志表           │
└─────────────────────────────────────────────┘
```

---

## 📊 数据库设计

### 核心表结构

```
exams (考试表)
  ├─ id: 主键
  ├─ title: 考试名称
  ├─ type: 考试类型 (normal/timed)
  ├─ duration: 时长
  ├─ pass_score: 及格分数
  ├─ status: 启用状态
  └─ ...

exam_questions (题目表)
  ├─ id: 主键
  ├─ exam_id: 所属考试
  ├─ type: 题型 (single_choice/multiple_choice/fill_blank/essay)
  ├─ difficulty: 难度 (easy/medium/hard)
  ├─ score: 分数
  └─ ...

exam_question_options (选项表)
  ├─ id: 主键
  ├─ question_id: 所属题目
  ├─ content: 选项内容
  ├─ option_key: A/B/C/D
  └─ is_correct: 是否正确答案

exam_user_records (成绩记录表)
  ├─ id: 主键
  ├─ exam_id: 考试ID
  ├─ user_id: 学生ID
  ├─ status: 状态 (0:未开始/1:进行中/2:已完成/3:已过期)
  ├─ score: 得分
  ├─ pass_status: 是否通过
  └─ ...

exam_user_answers (答题记录表)
  ├─ id: 主键
  ├─ exam_record_id: 成绩记录ID
  ├─ question_id: 题目ID
  ├─ user_answer: 学生答案
  ├─ is_correct: 是否正确
  └─ score: 得分

exam_import_logs (导入日志表)
  ├─ id: 主键
  ├─ exam_id: 考试ID
  ├─ file_name: 文件名
  ├─ total_count: 总条数
  ├─ success_count: 成功数
  ├─ status: 导入状态
  └─ error_message: 错误信息
```

---

## 🔄 主要业务流程

### 流程 1：创建考试和导入题目

```
1. 管理员创建考试
   ├─ 填写考试基本信息 (名称/时长/分数/状态)
   ├─ 选择考试类型 (普通/限时)
   ├─ 设置及格分数
   └─ 保存到 exams 表

2. 下载导入模板
   └─ 系统生成 Excel 模板

3. 管理员填写题目数据
   ├─ 题目信息 (标题/类型/难度/分数)
   ├─ 选项信息 (只有选择题需要)
   ├─ 正确答案
   └─ 答案解析

4. 上传并导入 Excel
   ├─ 系统解析 Excel 文件
   ├─ 验证每一行数据
   ├─ 对于单选/多选题，创建题目和选项
   ├─ 对于填空/论述题，只创建题目
   ├─ 创建导入日志记录
   └─ 返回导入结果

5. 分配考试
   ├─ 选择分配范围 (部门/学员)
   └─ 保存到 exam_department_user 表
```

### 流程 2：学生参加考试

```
1. 学生进入考试
   ├─ 检查考试是否开始
   ├─ 检查是否有权限
   ├─ 创建 exam_user_records 记录
   └─ 状态变为 "进行中"

2. 学生答题
   ├─ 加载题目列表
   ├─ 显示各类型题目
   ├─ 记录答题时间
   └─ 实时保存答案

3. 学生提交考试
   ├─ 确认是否提交
   ├─ 自动评分 (选择题/填空题)
   ├─ 论述题标记为待评
   ├─ 更新 exam_user_records (状态/分数/通过状态)
   └─ 创建 exam_user_answers 记录

4. 查看成绩
   ├─ 显示总分/得分
   ├─ 显示通过/未通过
   ├─ 显示答题详情 (可选)
   └─ 显示答案解析 (可选)
```

### 流程 3：成绩管理

```
1. 查看成绩列表
   ├─ 按考试筛选
   ├─ 按学员筛选
   ├─ 按状态筛选
   └─ 显示成绩统计

2. 查看详细答题
   ├─ 显示每题的答案
   ├─ 显示正确答案
   ├─ 显示是否正确
   ├─ 显示得分
   └─ 显示解析

3. 手动评分 (论述题)
   ├─ 查找待评分的答题
   ├─ 输入评分和反馈
   ├─ 更新 exam_user_answers
   └─ 重新计算总分

4. 导出成绩
   └─ 生成 Excel 成绩表
```

---

## 🎯 题型支持

### 1. 单选题
```
题目: 1+1等于多少?
选项:
  A: 1
  B: 2 ✓ (正确)
  C: 3
  D: 4

正确答案: B
自动评分: 是
```

### 2. 多选题
```
题目: 以下哪些是元音?
选项:
  A: A ✓
  B: B
  C: E ✓
  D: D

正确答案: AC
自动评分: 是 (全选才给分)
```

### 3. 填空题
```
题目: 中国的首都是 ______
正确答案: 北京

评分方式:
  - 精确匹配
  - 模糊匹配 (可去除空格/标点)
  - 支持多个正确答案

自动评分: 是
```

### 4. 论述题
```
题目: 简述你对学习的理解

特点:
  - 无标准答案
  - 需要人工评分
  - 支持评分反馈

自动评分: 否
需要管理员评分: 是
```

---

## 📋 Excel 导入数据流

```
用户选择 Excel 文件
        ↓
前端验证文件格式 (xlsx)
        ↓
上传文件到服务器
        ↓
后端接收文件 (ImportController)
        ↓
使用 POI/EasyExcel 库解析
        ↓
逐行解析数据
        ├─ 题目信息 (标题/类型/难度/分数)
        ├─ 选项信息 (仅选择题)
        ├─ 正确答案
        └─ 解析

数据验证
├─ 题型是否有效
├─ 难度是否有效
├─ 分数范围检查
├─ 必填字段检查
├─ 选项完整性检查
├─ 正确答案合法性检查
└─ 返回验证错误

通过验证后创建题目
├─ 插入 exam_questions 表
├─ 若是选择题，插入 exam_question_options 表
└─ 返回成功数

记录导入日志
├─ 文件名
├─ 总条数
├─ 成功数
├─ 失败数
└─ 错误详情

返回导入结果给前端
```

---

## 🔐 权限控制

### 后端权限清单

```
exam-index         - 查看考试列表
exam-create        - 创建考试
exam-edit          - 编辑考试
exam-delete        - 删除考试
exam-publish       - 发布考试

exam-question      - 题目管理
exam-import        - 题目导入
exam-assign        - 分配考试

exam-records       - 查看成绩
exam-grade         - 评分 (论述题)
exam-export        - 导出成绩
```

### 前端路由保护

```
所有考试管理页面需要 <PrivateRoute> 保护
检查用户是否有对应权限
无权限时显示 403 页面
```

---

## 📈 API 清单

### 考试管理
```
GET    /backend/v1/exams/list              - 获取列表
POST   /backend/v1/exams/create            - 创建
PUT    /backend/v1/exams/{id}              - 更新
DELETE /backend/v1/exams/{id}              - 删除
GET    /backend/v1/exams/{id}              - 详情
```

### 题目管理
```
GET    /backend/v1/exams/{id}/questions    - 题目列表
POST   /backend/v1/exams/{id}/questions    - 添加题目
PUT    /backend/v1/questions/{id}          - 编辑题目
DELETE /backend/v1/questions/{id}          - 删除题目
```

### 导入功能
```
POST   /backend/v1/exams/{id}/import       - 导入题目
GET    /backend/v1/exams/{id}/import-logs  - 导入日志
GET    /backend/v1/exams/{id}/template     - 下载模板
```

### 分配管理
```
POST   /backend/v1/exams/{id}/assign       - 分配考试
GET    /backend/v1/exams/{id}/assignments  - 查看分配
DELETE /backend/v1/exams/{id}/assignments  - 取消分配
```

### 成绩管理
```
GET    /backend/v1/exams/{id}/records                  - 成绩列表
GET    /backend/v1/exams/{id}/records/{userId}         - 学生成绩
GET    /backend/v1/exams/{id}/records/{userId}/answers - 答题详情
PUT    /backend/v1/exams/{id}/records/{userId}/grade   - 评分
GET    /backend/v1/exams/{id}/records/export           - 导出成绩
```

---

## 💾 数据备份建议

1. **导入前备份**
   - 每次大批量导入前备份数据库

2. **定期备份**
   - 每周备份一次考试数据

3. **日志保留**
   - 所有导入日志至少保留 1 年

4. **成绩存档**
   - 考试结束后存档成绩数据

---

## 🚀 实施路线图

### 第一阶段：后端开发
- [ ] 创建数据表
- [ ] 开发考试管理 API
- [ ] 开发题目管理 API
- [ ] 开发导入功能
- [ ] 开发成绩计算引擎

### 第二阶段：前端开发
- [ ] 考试列表页面
- [ ] 考试创建/编辑页面
- [ ] 题目管理页面
- [ ] Excel 导入页面
- [ ] 成绩查询页面

### 第三阶段：学生端开发
- [ ] 考试列表页面
- [ ] 在线考试页面
- [ ] 成绩查询页面
- [ ] 答题详情页面

### 第四阶段：测试和优化
- [ ] 单元测试
- [ ] 集成测试
- [ ] 性能优化
- [ ] 安全审计

---

## 📚 相关文档

- `EXAM_SYSTEM_DESIGN.md` - 系统详细设计
- `EXAM_TABLES.sql` - 数据库建表脚本
- `EXAM_IMPORT_TEMPLATE.md` - Excel 导入指南

