#!/bin/bash

# PlayEdu 前后端更新部署脚本
# 说明：仅部署应用，不部署MySQL数据库，MySQL继续使用现有容器

set -e

echo "=========================================="
echo "PlayEdu 前后端应用更新部署"
echo "=========================================="

# 配置变量
PROJECT_ROOT="/Users/<USER>/pa/codes/playedu"
BACKEND_PORT="${BACKEND_PORT:-9700}"
ADMIN_PORT="${ADMIN_PORT:-9900}"
ADMIN_API_URL="http://playedu-api:9898"

cd "$PROJECT_ROOT"

# ============ 第1步：编译后端 ============
echo ""
echo "【1/6】编译后端 API..."
cd "$PROJECT_ROOT/playedu-api"
mvn clean package -DskipTests
echo "✓ 后端编译完成"

# ============ 第2步：编译前端 ============
echo ""
echo "【2/6】编译前端 Admin..."
cd "$PROJECT_ROOT/playedu-admin"
npm install
VITE_APP_URL=/api/ npm run build
echo "✓ 前端编译完成"

# ============ 第3步：构建后端镜像 ============
echo ""
echo "【3/6】构建后端 Docker 镜像..."
cd "$PROJECT_ROOT"
docker build -f playedu-api/Dockerfile -t playedu-api:latest .
echo "✓ 后端镜像构建完成"

# ============ 第4步：构建前端镜像（单独构建admin） ============
echo ""
echo "【4/6】构建前端 Admin Docker 镜像..."
cat > "$PROJECT_ROOT/Dockerfile.admin" << 'DOCKERFILE'
FROM registry.cn-hangzhou.aliyuncs.com/hzbs/node:20-alpine AS builder

COPY playedu-admin /app

WORKDIR /app

RUN npm install && VITE_APP_URL=/api/ npm run build

FROM registry.cn-hangzhou.aliyuncs.com/hzbs/nginx:latest

COPY --from=builder /app/dist /usr/share/nginx/html

RUN rm /etc/nginx/conf.d/default.conf

COPY docker/nginx/conf/nginx.conf /etc/nginx/conf.d/default.conf

EXPOSE 80

CMD ["nginx", "-g", "daemon off;"]
DOCKERFILE

docker build -f "$PROJECT_ROOT/Dockerfile.admin" -t playedu-admin:latest .
echo "✓ 前端镜像构建完成"

# ============ 第5步：停止旧容器并删除 ============
echo ""
echo "【5/6】停止并删除旧容器..."
docker rm -f playedu-api playedu-admin 2>/dev/null || true
sleep 2
echo "✓ 旧容器已删除"

# ============ 第6步：启动新容器 ============
echo ""
echo "【6/6】启动新容器..."

# 启动后端API容器
echo "  启动后端 API 容器..."
docker run -d \
  --name playedu-api \
  --network playedu \
  -p ${BACKEND_PORT}:9898 \
  -e DB_HOST=mysql \
  -e DB_PORT=3306 \
  -e DB_NAME=playedu \
  -e DB_USER=root \
  -e DB_PASS=playeduxyz \
  -e SA_TOKEN_IS_CONCURRENT=false \
  -e SA_TOKEN_JWT_SECRET_KEY=playeduxyz \
  playedu-api:latest

sleep 3

# 启动前端Admin容器
echo "  启动前端 Admin 容器..."
docker run -d \
  --name playedu-admin \
  --network playedu \
  -p ${ADMIN_PORT}:80 \
  -e VITE_APP_URL=/api/ \
  playedu-admin:latest

echo "✓ 容器启动完成"

# ============ 完成 ============
echo ""
echo "=========================================="
echo "✅ 部署完成！"
echo "=========================================="
echo ""
echo "📋 服务地址："
echo "  后端 API:     http://localhost:${BACKEND_PORT}"
echo "  前端 Admin:   http://localhost:${ADMIN_PORT}"
echo ""
echo "📝 查看日志:"
echo "  后端日志:     docker logs -f playedu-api"
echo "  前端日志:     docker logs -f playedu-admin"
echo ""
echo "🔄 重启服务:"
echo "  docker restart playedu-api playedu-admin"
echo ""
echo "停止服务:"
echo "  docker stop playedu-api playedu-admin"
echo ""
