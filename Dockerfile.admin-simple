FROM registry.cn-hangzhou.aliyuncs.com/hzbs/node:20-alpine AS builder

WORKDIR /app

COPY playedu-admin .

# 安装依赖（包括dev依赖）
RUN npm install --legacy-peer-deps

# 构建
RUN npm run build

# 使用Node http-server来serve静态文件
FROM registry.cn-hangzhou.aliyuncs.com/hzbs/node:20-alpine

WORKDIR /app

RUN npm install -g http-server

COPY --from=builder /app/dist /app/dist

EXPOSE 80

CMD ["http-server", "/app/dist", "-p", "80", "--cors"]
