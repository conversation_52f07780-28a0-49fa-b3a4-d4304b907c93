## 2.0

- 新增:`LDAP`同步数据统计
- 新增:`LDAP`同步的详细记录
- 新增:`LDAP`同步的数据下载
- 修复:学员详情接口报错的Bug
- 修复:限流缓存无过期时间的Bug
- 优化:存储桶由`public`改为`private`
- 优化:资源访问URL动态生成
- 优化:数据表结构[注意:1.x版本无法直接升级2.x版本]
- 优化:移除`Redis`运行依赖改为使用内存缓存
- 优化:移除本地存储方案`MinIO`的支持改为支持阿里云OSS和腾讯云COS

## 1.8

- 优化:[API]日志输出
- 优化:[API]LDAP的部门同步逻辑
- 优化:[API]LDAP的用户同步逻辑
- 优化:[其它]`docker`镜像更换为阿里云加速

## 1.7

- 新增:[API]MinIO配置信息增加环境变量的读取
- 新增:[API]学员学习权限优化
- 新增:[后台]后台首页增加课件数量的显示
- 新增:[后台]线上课列表增加创建人字段
- 优化:[后台]学员部门包含子部门所有学员数量
- 优化:[API]根据分类ID获取所有子分类的课程
- 优化:[API]根据部门ID获取所有父级部门的课程
- 优化:[后台]部门指派器
- 优化:[PC]视频播放器去除右键点击
- 优化:[PC]首页学习时长去掉秒
- 优化:[H5]首页tab切换优化

## 1.6

- 优化：删除学员身份证号字段并移除身份证号查看权限
- 修复：LDAP用户读取。现已支持分页读取数据
- 修复：学员批量导入
- 优化：[PC]线上课课程详情tab交互
- 优化：[PC]首页学习时间不显示天数
- 优化：[PC]删除无用的js依赖
- 优化：[PC]安全退出登录
- 优化：[PC]系统错误提示
- 优化：[PC]部门切换缓存
- 修复：[PC]pnpm build 和 dev 报错
- 优化：[后台]视频上传
- 优化：[后台]删除无用的依赖
- 优化：[后台]部门页面组件交互增加loading
- 优化：[后台]删除资源选择上传资源的按钮
- 优化：[后台]学员添加编辑
- 修复：[后台]pnpm build 和 dev 报错
- 优化：[H5]删除无用依赖
- 优化：[H5]下拉刷新
- 修复：[H5]pnpm build 和 dev 报错
- 修复：[H5]底部导航栏悬浮bug

## 1.5

- 新增：线上课新增创建人字段
- 新增：视频资源增加视频名搜索
- 新增：LDAP部门+学员自动同步（每小时同步一次）
- 优化：前台学习总时长统计最大单位由“天”改为“小时”
- 优化：更换 minio 底层的SDK
- 修复：修复角色权限配置参数错误的bug
- 修复：修复学员开始学习时间错误bug
- 修复：线上课编辑章节名排序bug
- 修复：修复课时拖拽排序父子集交互
- 修复：MySQL8版本无法连接bug

## 1.4

- 新增Window AD域登录支持
- 新增一键同步 LDAP 的部门组织架构
- 优化docker镜像内的时区
- 优化后台的全局配置接口
- 新增后台资源菜单权限
- 新增后台分类菜单权限
- 新增文件上传权限

## 1.3

- 新增 `LDAP` 登录
- 数据库表在运行时自动同步到数据库，以后安装/升级程序无需手动将 `sql` 文件导入到数据库
- 线上课新增 `published_at` 字段，用于控制课程的排序
- `API` 程序模块化
- 移除 `minio` 上传的限流控制
- 图片上传最大尺寸调整为 `10mb`
- 优化跨域配置
- 新增视频上传失败的重新上传

## 1.2

- 课程附件
- 管理员日志

## 1.1

+ 移除图形验证码
+ 优化dockerfile
+ 修复前后台的账号冲突
+ 新增API限流
+ 新增账户限流

## 1.0-beta7

+ 新增：后台|系统配置增加 API 地址配置
+ 新增：后台|已上传视频预览播放
+ 新增：后台|视频批量删除
+ 新增：后台|视频、图片的分类修改
+ 优化：后台|系统配置部分敏感配置 * 号代替显示
+ 优化：后台|分类删除交互优化
+ 优化：后台|跑马灯交互优化
+ 优化：后台|超级管理员不显示权限配置按钮
+ 修复：后台|未分类下上传资源的资源分类关联 bug
+ 优化：API|重构用户的 JWT 底层服务
+ 优化：PC学员端|全屏播放播放结束的下节课时显示

## 1.0-beta6

+ 优化MinIO配置
+ 优化图形验证码
+ 优化学员删除的关联数据删除
+ 优化dockerfile
+ 新增：禁止拖拽播放配置

## 1.0-beta5

+ 学员注册增加课程分类筛选
+ 线上课-学员列表增加学员所属部门、邮箱字段的显示
+ 线上课-学员列表显示可以观看该线上课的所有学员
+ 修复学员的多部门查询bug
+ 优化学员学习进度的展示(前后台保持统一)
+ 修复后台部门的文案显示溢出
+ 优化视频播放页面样式和交互

## 1.0-beta4

+ 新增学员线上课详细学习进度api
+ 新增课时详情api
+ 后台部门学员的学习进度api增加更多过滤参数
+ 修复图形验证码的大写校验bug
+ 提升系统稳定性

## 1.0-beta3

+ 后台的学员列表左侧部门列表显示每个部门的学员人数
+ 后台学员列表在选择部门的情况下可直接查看该部门下的学员学习进度
+ 后台学员列表学员增加『学习』按钮，点击可查看该学员的最近30天每日学习时长、该学员的所有课时、线上课的学习进度
+ 优化 Docker 编译。现在可直接编译镜像无需手动执行命令安装依赖
+ 修复后台学员默认头像的bug
+ 修复部门的排序和父子级变更的权限控制Bug
+ 优化已知的null错误，提升系统稳定性
+ 修复PC端口的部门切换无法持久化保存的bug
+ 修复PC端口的页面切换滚动条位置bug
+ 优化PC端口的学员退出的逻辑

## 1.0-beta2

+ 后台线上课部门选择增加学员数量的显示
+ 优化学员的默认头像
+ 优化线上课的默认三张封面
+ 优化学员导入
+ 修复已知bug

## 1.0-beta1

- 一款开源的培训系统，您可以使用它快速搭建私有化内部培训平台