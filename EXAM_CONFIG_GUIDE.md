# 考试测评菜单完整配置指南

## 目前的状态
✅ 菜单项已添加
✅ 路由已配置
✅ 基础页面已创建
❌ 页面功能还不完整

---

## 方案A：快速方案（复用线上课页面）
如果你想要一个和"线上课"一样功能的考试测评管理页面，可以这样做：

### 1. 复制线上课页面作为基础
```bash
cp -r /Users/<USER>/pa/codes/playedu/playedu-admin/src/pages/course \
      /Users/<USER>/pa/codes/playedu/playedu-admin/src/pages/exam
```

### 2. 修改exam页面中的导入和API调用
- 将所有 `import { course }` 改成 `import { exam }`
- 调整API方法名从 `course.*` 改成 `exam.*`
- 修改界面标题和按钮文案

### 3. 创建exam API文件
创建 `playedu-admin/src/api/exam.ts`：

```typescript
import client from "./internal/httpClient";

// 获取考试列表
export function getExamList(page: number, size: number, title: string = "") {
  return client.get("/backend/v1/exam/list", {
    params: { page, size, title },
  });
}

// 创建考试
export function storeExam(title: string, description: string) {
  return client.post("/backend/v1/exam/create", {
    title,
    description,
  });
}

// 更新考试
export function updateExam(id: number, title: string, description: string) {
  return client.put(`/backend/v1/exam/update/${id}`, {
    title,
    description,
  });
}

// 删除考试
export function deleteExam(id: number) {
  return client.delete(`/backend/v1/exam/delete/${id}`, {});
}

// 导出exam到api/index.ts
export default {
  getExamList,
  storeExam,
  updateExam,
  deleteExam,
};
```

### 4. 更新 `playedu-admin/src/api/index.ts`
```typescript
// ... 其他导入 ...
export { default as exam } from "./exam";
```

---

## 方案B：简单方案（保持当前占位符）

如果你暂时不需要完整功能，只需要菜单能点击显示页面，当前配置就可以了。

### 需要做的只是：
1. ✅ 菜单已添加 - 完成
2. ✅ 路由已配置 - 完成
3. ✅ 页面已创建 - 完成
4. 需要：**硬刷新浏览器**（Cmd+Shift+R 或 Ctrl+Shift+F5）

---

## 方案C：完整从零开始

如果要从零开始完整开发考试测评功能，需要配置：

### 前端需要的文件
```
playedu-admin/src/
├── api/
│   └── exam.ts                    # API接口定义
├── pages/
│   └── exam/
│       ├── index.tsx              # 主页面
│       ├── index.module.less       # 样式
│       └── compenents/             # 子组件
│           ├── create.tsx          # 创建/编辑
│           └── list.tsx            # 列表
└── routes/
    └── index.tsx                  # 路由已配置✅
```

### 后端需要的接口 (Java API)
```
GET    /backend/v1/exam/list       # 获取考试列表
POST   /backend/v1/exam/create     # 创建考试
PUT    /backend/v1/exam/update/:id # 更新考试
DELETE /backend/v1/exam/delete/:id # 删除考试
```

---

## 当前状态检查清单

### 前端配置 ✅
- [x] 菜单项添加到 `left-menu/index.tsx`
- [x] 菜单权限设为 `null`（无限制）
- [x] 路由映射 `"^/exam": ["courses"]` 已配置
- [x] ExamPage 导入已添加到 `routes/index.tsx`
- [x] 路由配置 `/exam` 已添加
- [x] 页面文件已创建 `pages/exam/index.tsx`
- [x] 样式文件已创建 `pages/exam/index.module.less`

### 后端配置 ❓
- [ ] 是否有考试相关的后端API接口？
- [ ] 数据库是否有exam表？

### 立即测试
```bash
# 1. 在项目根目录运行
npm run dev

# 2. 浏览器中打开
http://localhost:9900

# 3. 硬刷新（清除缓存）
Cmd + Shift + R (Mac)
Ctrl + Shift + F5 (Windows/Linux)

# 4. 点击左侧菜单 "课程中心" → "考试测评"
```

---

## 常见问题

### Q: 菜单点击无效？
A: 检查浏览器控制台（F12）是否有红色错误。如果有"Cannot find module"错误，说明页面文件加载失败。

### Q: 点击菜单后显示404或错误页面？
A: 这表示路由找不到页面。需要检查：
1. 文件 `pages/exam/index.tsx` 是否存在
2. 文件是否有 `export default ExamPage`
3. routes/index.tsx 中的路由配置是否正确

### Q: 如何隐藏菜单？
A: 修改 `left-menu/index.tsx` 的第59行，把权限改成一个其他的标识：
```tsx
getItem("考试测评", "/exam", null, null, null, "exam-permission")  // 添加权限限制
```

---

## 推荐方案

**建议使用方案B**：保持当前配置，然后：
1. 硬刷新浏览器
2. 验证菜单能显示
3. 当需要真实功能时，再参考方案C实现后端API和完整页面

这样可以快速验证配置是否正确！
