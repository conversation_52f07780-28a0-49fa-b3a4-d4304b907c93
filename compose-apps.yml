x-logging: &default-logging
  driver: "json-file"
  options:
    max-size: "10m"
    max-file: "10"

networks:
  playedu:
    driver: bridge

services:
  playedu-api:
    image: playedu-api:latest
    restart: always
    environment:
      - DB_HOST=mysql
      - DB_PORT=3306
      - DB_NAME=playedu
      - DB_USER=root
      - DB_PASS=playeduxyz
      - SA_TOKEN_IS_CONCURRENT=false
      - SA_TOKEN_JWT_SECRET_KEY=playeduxyz
    ports:
      - "${PLAYEDU_API_PORT:-9700}:9898"
    networks:
      - playedu
    logging: *default-logging

  playedu-admin:
    image: playedu-admin:latest
    restart: always
    ports:
      - "${PLAYEDU_ADMIN_PORT:-9900}:80"
    networks:
      - playedu
    logging: *default-logging
    depends_on:
      - playedu-api
