# 考试测评系统设计完整文档

## 📌 文档导航

本项目包含考试测评系统的完整设计文档，包括数据库设计、功能设计、API设计和实施指南。

### 📚 文档清单

| 文档 | 描述 | 用途 |
|------|------|------|
| **EXAM_SYSTEM_DESIGN.md** | 📋 详细系统设计 | 了解系统全貌、数据库表结构、业务流程 |
| **EXAM_TABLES.sql** | 🗄️ 数据库建表脚本 | 直接在数据库中执行创建表 |
| **EXAM_ARCHITECTURE_SUMMARY.md** | 🏗️ 架构总结 | 理解系统架构、API清单、实施路线图 |
| **EXAM_IMPORT_TEMPLATE.md** | 📥 Excel导入指南 | 了解如何准备导入Excel、字段说明、验证规则 |
| **此文件** | 📖 项目总览 | 快速了解整体情况 |

---

## 🎯 系统概述

考试测评系统是PlayEdu平台的重要组成部分，支持管理员在线创建和管理考试，学生在线参加考试，系统自动评分和手动评分相结合。

### 主要特性

✅ **多题型支持**
- 单选题（自动评分）
- 多选题（自动评分）
- 填空题（自动评分）
- 论述题（手动评分）

✅ **Excel批量导入**
- 支持大批量导入题目
- 完整的数据验证
- 详细的导入日志
- 导入模板下载

✅ **灵活的考试配置**
- 普通考试和限时考试
- 自定义考试时长
- 自定义及格分数
- 显示/隐藏分数和答案

✅ **完善的成绩管理**
- 自动评分（选择题）
- 手动评分（论述题）
- 成绩统计和分析
- 成绩导出

✅ **权限控制**
- 菜单级权限
- API级权限
- 细粒度权限设计

---

## 🗄️ 核心数据库表

系统包含7个核心数据表：

```
exams                        - 考试信息
exam_questions               - 题目信息
exam_question_options        - 题目选项（选择题）
exam_user_records            - 学生成绩记录
exam_user_answers            - 学生答题记录
exam_department_user         - 考试分配范围
exam_import_logs             - 导入日志
```

**查看详细设计**: 打开 `EXAM_SYSTEM_DESIGN.md`

---

## 📊 核心业务流程

### 流程 1：出题过程
```
创建考试 → 下载模板 → 填写题目 → 上传导入 → 分配考试 → 发布
```

### 流程 2：学生答题
```
查看考试 → 进入考试 → 答题 → 提交 → 自动评分 → 查看成绩
```

### 流程 3：成绩管理
```
查看成绩 → 评分（论述题） → 导出成绩
```

---

## 📈 API 接口总览

系统提供以下API接口：

**考试管理** (5个)
- 列表、创建、更新、删除、详情

**题目管理** (4个)
- 列表、添加、编辑、删除

**导入功能** (3个)
- 导入、查看日志、下载模板

**分配管理** (3个)
- 分配、查看、取消

**成绩管理** (5个)
- 列表、详情、答题记录、评分、导出

**总计: 20+ 个 API 接口**

查看详细: 打开 `EXAM_ARCHITECTURE_SUMMARY.md`

---

## 🎯 题型对比

| 题型 | 选项 | 自动评分 | 说明 |
|------|------|---------|------|
| 单选题 | A/B/C/D 4个 | ✅ | 只能选一个 |
| 多选题 | A/B/C/D 4个 | ✅ | 必须全选才给分 |
| 填空题 | 无 | ✅ | 支持精确/模糊匹配 |
| 论述题 | 无 | ❌ | 需要管理员评分 |

---

## 📥 Excel导入详解

### 导入模板结构

| 题目 | 题型 | 难度 | 分数 | 选项A | 选项B | 选项C | 选项D | 正确答案 | 解析 |
|------|------|------|------|-------|-------|-------|-------|---------|------|
| JavaScript基础？ | 单选 | 简单 | 1 | ES5 | ES6 | ES7 | ES8 | B | 最常用 |

### 数据验证

✅ **有效的题型**: 单选、多选、填空、论述
✅ **有效的难度**: 简单、中等、困难
✅ **有效的分数**: 1-100 的整数
✅ **选择题**: 必须有4个选项
✅ **正确答案**: 单选填字母，多选填字母组合，填空填内容

❌ **无效的数据**: 会被记录到导入日志中

详细说明: 打开 `EXAM_IMPORT_TEMPLATE.md`

---

## 🔐 权限设计

### 后端权限清单

```
exam-index      - 查看考试列表
exam-create     - 创建考试
exam-edit       - 编辑考试
exam-delete     - 删除考试
exam-question   - 管理题目
exam-import     - 导入题目
exam-records    - 查看成绩
exam-grade      - 评分
exam-export     - 导出成绩
```

### 推荐角色分配

- **超级管理员**: 所有权限
- **考试管理员**: exam-* 所有权限
- **阅卷员**: exam-records、exam-grade
- **查看员**: exam-records

---

## 🚀 实施计划

### 第一阶段：后端开发 (1-2周)
- [ ] 创建数据表
- [ ] 开发考试管理 API
- [ ] 开发题目管理 API
- [ ] 开发导入功能

### 第二阶段：前端开发 (1-2周)
- [ ] 考试管理页面
- [ ] 题目管理页面
- [ ] Excel 导入页面
- [ ] 成绩查询页面

### 第三阶段：测试 (1周)
- [ ] 功能测试
- [ ] 性能测试
- [ ] 安全测试

### 第四阶段：上线 (1天)
- [ ] 数据迁移
- [ ] 灰度发布
- [ ] 正式上线

---

## 📋 建表说明

### 快速建表

```bash
# 登录MySQL
mysql -u root -p

# 选择数据库
USE playedu;

# 执行建表脚本
source /path/to/EXAM_TABLES.sql;
```

### 验证建表

```sql
-- 查看所有表
SHOW TABLES LIKE 'exam%';

-- 查看表结构
DESC exams;
DESC exam_questions;
DESC exam_question_options;
DESC exam_user_records;
DESC exam_user_answers;
DESC exam_department_user;
DESC exam_import_logs;
```

---

## 💡 核心设计思想

### 1. 数据独立性
- 考试、题目、选项分表存储
- 允许题目复用
- 支持多对多关系

### 2. 灵活的题型支持
- 同一套系统支持4种题型
- 自动评分和手动评分相结合
- 便于未来扩展新题型

### 3. 完整的追溯
- 所有导入操作有日志
- 所有答题操作有记录
- 支持数据审计

### 4. 高效的评分
- 选择题自动评分
- 填空题模糊匹配
- 论述题手动批量评分

### 5. 清晰的权限
- 按功能划分权限
- 支持细粒度控制
- 便于团队协作

---

## 🔗 相关资源

### 前置知识
- MySQL 数据库设计基础
- Spring Boot RESTful API 开发
- React 前端开发
- Excel 文件处理

### 相关技术
- Apache POI 或 EasyExcel (Excel解析)
- JWT (身份验证)
- Redis (缓存)
- Spring Security (权限控制)

### 参考项目
- 查看 `playedu-admin/src/pages/exam/` - 前端页面示例
- 查看 `playedu-api/` - 后端API实现方式

---

## 📞 常见问题

### Q: 导入Excel时出错怎么办？
A: 查看导入日志中的错误信息，参考 `EXAM_IMPORT_TEMPLATE.md` 中的"导入失败常见原因"部分。

### Q: 支持哪些题型？
A: 当前支持：单选题、多选题、填空题、论述题。

### Q: 能否支持其他题型？
A: 可以，在 `exam_questions` 表中增加新的 `type` 值，并相应调整前后端逻辑。

### Q: 评分规则是什么？
A: 单选/多选/填空题自动评分，论述题需要手动评分。

### Q: 能否修改已经导入的题目？
A: 可以，通过题目管理页面或API修改。修改历史由数据库记录。

---

## ✨ 项目亮点

1. **完整的系统设计** - 从数据库到API再到前端，一应俱全
2. **灵活的题型支持** - 同一系统支持多种题型
3. **强大的导入能力** - 支持Excel批量导入，完整的数据验证
4. **清晰的权限设计** - 细粒度权限控制，支持多角色
5. **详细的文档** - 完整的设计文档和实施指南

---

## 📝 文档维护

- **更新时间**: 2025-10-30
- **文档版本**: 1.0
- **作者**: PlayEdu Team
- **最后修改**: 设计完成

---

## 🎓 快速开始

### 1. 了解系统整体设计
👉 阅读 `EXAM_SYSTEM_DESIGN.md`

### 2. 了解数据库表结构
👉 阅读 `EXAM_SYSTEM_DESIGN.md` 的表设计部分

### 3. 准备导入Excel模板
👉 阅读 `EXAM_IMPORT_TEMPLATE.md`

### 4. 执行建表脚本
👉 使用 `EXAM_TABLES.sql`

### 5. 开发后端API
👉 参考 `EXAM_ARCHITECTURE_SUMMARY.md` 的API清单

### 6. 开发前端页面
👉 参考 `playedu-admin/src/pages/exam/` 的示例

---

## 📞 技术支持

如有任何问题或建议，请联系开发团队。

---

**版权所有 © 2025 PlayEdu**

