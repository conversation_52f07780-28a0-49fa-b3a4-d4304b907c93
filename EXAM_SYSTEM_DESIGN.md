# 考试测评系统设计文档

## 📋 系统概述

基于现有PlayEdu系统，设计一个完整的**在线考试测评模块**，支持：
- 多题型（选择题、填空题、论述题等）
- Excel 批量导入题目
- 学生在线考试
- 成绩管理和统计

---

## 🗄️ 数据库表设计

### 1. 考试表 (exams)
```sql
CREATE TABLE `exams` (
  `id` int unsigned NOT NULL AUTO_INCREMENT COMMENT '主键',
  `title` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '考试名称',
  `description` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci COMMENT '考试描述',
  `type` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'normal' COMMENT '考试类型[normal:普通,timed:限时]',
  `duration` int NOT NULL DEFAULT 60 COMMENT '考试时长(分钟)',
  `pass_score` int NOT NULL DEFAULT 60 COMMENT '及格分数(百分比)',
  `total_score` int NOT NULL DEFAULT 100 COMMENT '总分',
  `status` tinyint NOT NULL DEFAULT 1 COMMENT '状态[1:启用,0:禁用]',
  `show_score` tinyint NOT NULL DEFAULT 1 COMMENT '是否显示分数[1:显示,0:隐藏]',
  `show_answer` tinyint NOT NULL DEFAULT 1 COMMENT '是否显示答案[1:显示,0:隐藏]',
  `admin_id` int NOT NULL DEFAULT 0 COMMENT '创建管理员ID',
  `admin_name` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '创建管理员名称',
  `start_time` timestamp NULL COMMENT '考试开始时间',
  `end_time` timestamp NULL COMMENT '考试结束时间',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NULL DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
  `deleted_at` timestamp NULL DEFAULT NULL COMMENT '删除时间',
  PRIMARY KEY (`id`),
  KEY `status` (`status`),
  KEY `created_at` (`created_at`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='考试表';
```

### 2. 考试题目表 (exam_questions)
```sql
CREATE TABLE `exam_questions` (
  `id` int unsigned NOT NULL AUTO_INCREMENT COMMENT '主键',
  `exam_id` int NOT NULL DEFAULT 0 COMMENT '考试ID',
  `title` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '题目标题',
  `type` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'single_choice' COMMENT '题型[single_choice:单选,multiple_choice:多选,fill_blank:填空,essay:论述]',
  `difficulty` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'medium' COMMENT '难度[easy:简单,medium:中等,hard:困难]',
  `score` int NOT NULL DEFAULT 1 COMMENT '分数',
  `sort` int NOT NULL DEFAULT 0 COMMENT '题目顺序',
  `analysis` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci COMMENT '答案解析',
  `status` tinyint NOT NULL DEFAULT 1 COMMENT '状态[1:启用,0:禁用]',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NULL DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
  PRIMARY KEY (`id`),
  KEY `exam_id` (`exam_id`),
  KEY `type` (`type`),
  KEY `sort` (`sort`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='考试题目表';
```

### 3. 题目选项表 (exam_question_options)
```sql
CREATE TABLE `exam_question_options` (
  `id` int unsigned NOT NULL AUTO_INCREMENT COMMENT '主键',
  `question_id` int NOT NULL DEFAULT 0 COMMENT '题目ID',
  `content` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '选项内容',
  `option_key` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '选项键[A,B,C,D,E,F...]',
  `is_correct` tinyint NOT NULL DEFAULT 0 COMMENT '是否正确答案[1:是,0:否]',
  `sort` int NOT NULL DEFAULT 0 COMMENT '排序',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`),
  KEY `question_id` (`question_id`),
  KEY `option_key` (`option_key`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='考试题目选项表';
```

### 4. 考试分配表 (exam_department_user)
```sql
CREATE TABLE `exam_department_user` (
  `id` int unsigned NOT NULL AUTO_INCREMENT COMMENT '主键',
  `exam_id` int NOT NULL DEFAULT 0 COMMENT '考试ID',
  `range_id` int NOT NULL DEFAULT 0 COMMENT '指派范围ID',
  `range_type` tinyint NOT NULL DEFAULT 0 COMMENT '指派范围类型[0:部门,1:学员]',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `unique_exam_range` (`exam_id`, `range_id`, `range_type`),
  KEY `exam_id` (`exam_id`),
  KEY `range_id` (`range_id`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='考试指派范围表';
```

### 5. 学生考试记录表 (exam_user_records)
```sql
CREATE TABLE `exam_user_records` (
  `id` int unsigned NOT NULL AUTO_INCREMENT COMMENT '主键',
  `exam_id` int NOT NULL DEFAULT 0 COMMENT '考试ID',
  `user_id` int NOT NULL DEFAULT 0 COMMENT '学生ID',
  `user_name` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '学生名称',
  `user_email` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '学生邮箱',
  `status` tinyint NOT NULL DEFAULT 0 COMMENT '状态[0:未开始,1:进行中,2:已完成,3:已过期]',
  `total_score` int NOT NULL DEFAULT 0 COMMENT '总分',
  `score` int DEFAULT NULL COMMENT '得分',
  `pass_status` tinyint DEFAULT NULL COMMENT '是否通过[1:通过,0:未通过,null:未考]',
  `start_at` timestamp NULL COMMENT '开始时间',
  `end_at` timestamp NULL COMMENT '结束时间',
  `duration` int DEFAULT 0 COMMENT '花费时间(秒)',
  `answer_count` int NOT NULL DEFAULT 0 COMMENT '答题数',
  `correct_count` int NOT NULL DEFAULT 0 COMMENT '正确数',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NULL DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `unique_exam_user` (`exam_id`, `user_id`),
  KEY `exam_id` (`exam_id`),
  KEY `user_id` (`user_id`),
  KEY `status` (`status`),
  KEY `created_at` (`created_at`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='学生考试记录表';
```

### 6. 学生答题记录表 (exam_user_answers)
```sql
CREATE TABLE `exam_user_answers` (
  `id` int unsigned NOT NULL AUTO_INCREMENT COMMENT '主键',
  `exam_record_id` int NOT NULL DEFAULT 0 COMMENT '考试记录ID',
  `exam_id` int NOT NULL DEFAULT 0 COMMENT '考试ID',
  `user_id` int NOT NULL DEFAULT 0 COMMENT '学生ID',
  `question_id` int NOT NULL DEFAULT 0 COMMENT '题目ID',
  `question_type` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '题型',
  `user_answer` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci COMMENT '学生答案',
  `correct_answer` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci COMMENT '正确答案',
  `is_correct` tinyint DEFAULT NULL COMMENT '是否正确[1:正确,0:错误,null:未批改]',
  `score` int DEFAULT NULL COMMENT '得分',
  `answer_time` int NOT NULL DEFAULT 0 COMMENT '答题耗时(秒)',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`),
  KEY `exam_record_id` (`exam_record_id`),
  KEY `user_id` (`user_id`),
  KEY `question_id` (`question_id`),
  KEY `is_correct` (`is_correct`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='学生答题记录表';
```

### 7. 题目导入日志表 (exam_import_logs)
```sql
CREATE TABLE `exam_import_logs` (
  `id` int unsigned NOT NULL AUTO_INCREMENT COMMENT '主键',
  `exam_id` int NOT NULL DEFAULT 0 COMMENT '考试ID',
  `file_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '文件名',
  `file_path` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '文件路径',
  `total_count` int NOT NULL DEFAULT 0 COMMENT '总条数',
  `success_count` int NOT NULL DEFAULT 0 COMMENT '成功条数',
  `error_count` int NOT NULL DEFAULT 0 COMMENT '错误条数',
  `status` tinyint NOT NULL DEFAULT 1 COMMENT '状态[1:成功,0:失败]',
  `error_message` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci COMMENT '错误信息',
  `admin_id` int NOT NULL DEFAULT 0 COMMENT '操作管理员ID',
  `admin_name` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '操作管理员名称',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`),
  KEY `exam_id` (`exam_id`),
  KEY `created_at` (`created_at`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='题目导入日志表';
```

---

## 📊 Excel 导入格式

### 导入模板结构

| 题目 | 题型 | 难度 | 分数 | 选项A | 选项B | 选项C | 选项D | 正确答案 | 解析 |
|------|------|------|------|-------|-------|-------|-------|---------|------|
| 1+1等于多少? | 单选 | 简单 | 1 | 1 | 2 | 3 | 4 | B | 数学基础 |
| 2+2等于多少? | 单选 | 简单 | 1 | 2 | 3 | 4 | 5 | D | 数学基础 |
| 首都是什么? | 填空 | 中等 | 2 | - | - | - | - | 北京 | 地理常识 |

### Excel 字段说明

| 字段 | 类型 | 必填 | 说明 |
|------|------|------|------|
| 题目 | String | ✅ | 题目标题，不超过255字 |
| 题型 | String | ✅ | 单选/多选/填空/论述 |
| 难度 | String | ✅ | 简单/中等/困难 |
| 分数 | Number | ✅ | 1-100之间的整数 |
| 选项A/B/C/D | String | 条件 | 选择题必填，填空题和论述题留空 |
| 正确答案 | String | ✅ | 单选/多选填选项字母(A/B/C/D)，填空题填答案内容 |
| 解析 | String | ❌ | 可选，题目解析说明 |

---

## 🎯 题型定义

```json
{
  "题型": {
    "single_choice": "单选题",
    "multiple_choice": "多选题",
    "fill_blank": "填空题",
    "essay": "论述题"
  },
  "难度": {
    "easy": "简单",
    "medium": "中等",
    "hard": "困难"
  },
  "状态": {
    "0": "未开始",
    "1": "进行中",
    "2": "已完成",
    "3": "已过期"
  },
  "考试类型": {
    "normal": "普通考试",
    "timed": "限时考试"
  }
}
```

---

## 🔄 主要业务流程

### 1. 创建考试
```
新建考试 → 配置考试信息(名称/时长/分数/状态) → 添加题目 → 分配学员/部门 → 发布
```

### 2. 导入题目
```
选择Excel文件 → 解析数据 → 验证字段 → 创建题目记录 → 记录导入日志 → 完成
```

### 3. 学生考试
```
进入考试 → 答题 → 提交 → 系统自动评分 → 生成成绩单 → 查看结果
```

### 4. 成绩管理
```
成绩查询 → 详细答题情况 → 导出成绩 → 重做考试
```

---

## 📈 API 接口清单

### 考试管理
- `GET /backend/v1/exams/list` - 获取考试列表
- `POST /backend/v1/exams/create` - 创建考试
- `PUT /backend/v1/exams/{id}` - 更新考试
- `DELETE /backend/v1/exams/{id}` - 删除考试
- `GET /backend/v1/exams/{id}/detail` - 获取考试详情

### 题目管理
- `GET /backend/v1/exams/{id}/questions` - 获取考试的题目列表
- `POST /backend/v1/exams/{id}/questions` - 添加题目
- `PUT /backend/v1/questions/{id}` - 编辑题目
- `DELETE /backend/v1/questions/{id}` - 删除题目

### 题目导入
- `POST /backend/v1/exams/{id}/import-questions` - 导入题目(Excel)
- `GET /backend/v1/exams/{id}/import-logs` - 获取导入日志
- `GET /backend/v1/exams/{id}/import-template` - 下载导入模板

### 分配管理
- `POST /backend/v1/exams/{id}/assign` - 分配考试给部门/学员
- `GET /backend/v1/exams/{id}/assignments` - 获取分配情况

### 成绩管理
- `GET /backend/v1/exams/{id}/records` - 获取考试成绩列表
- `GET /backend/v1/exams/{id}/records/{userId}` - 获取学生的考试记录
- `GET /backend/v1/exams/{id}/records/{userId}/answers` - 获取学生的答题详情
- `POST /backend/v1/exams/{id}/records/{userId}/grade` - 手动评分(论述题)
- `POST /backend/v1/exams/{id}/records/export` - 导出成绩

---

## 🔐 权限设置

建议在后端配置以下权限:

```
exam-index        - 考试列表查看
exam-create       - 考试创建
exam-edit         - 考试编辑
exam-delete       - 考试删除
exam-import       - 题目导入
exam-records      - 成绩查看
exam-grade        - 成绩评分
```

---

## 📝 Excel 导入字段验证规则

| 字段 | 验证规则 |
|------|---------|
| 题目 | 不为空，长度1-255 |
| 题型 | 必须是: 单选/多选/填空/论述 |
| 难度 | 必须是: 简单/中等/困难 |
| 分数 | 必须是数字，1-100之间 |
| 选项A/B/C/D | 选择题时必填，其他题型可空 |
| 正确答案 | 不为空；选择题需验证答案是否在选项中 |
| 解析 | 可选，字符串 |

---

## 🗑️ SQL 完整建表语句

完整的SQL脚本见下方代码块

