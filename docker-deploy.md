# PlayEdu Docker 部署指南

## 部署状态
✅ **部署成功** - 所有服务正常运行

## 服务端口
- **API接口**: http://localhost:9700 (后端API)
- **PC端**: http://localhost:9800 (PC端网页)
- **H5端**: http://localhost:9801 (移动端网页)  
- **管理端**: http://localhost:9900 (后台管理)
- **MySQL**: localhost:23307 (数据库)

## 登录账号
### 管理员账号（后台管理）
- **访问地址**: http://localhost:9900
- **用户名**: `<EMAIL>`
- **密码**: `playedu`
- **角色**: 超级管理员

## 存储配置

系统支持多种云存储服务，需要在管理后台配置：

1. 访问管理后台：`http://localhost:9900`
2. 进入：系统配置 → S3存储
3. 配置存储参数（支持阿里云OSS、MinIO、AWS S3等）

详细配置请参考 [storage-config.md](storage-config.md)

### 数据库配置
- **数据库**: MySQL 8.1
- **端口**: 23307
- **用户名**: root
- **密码**: playeduxyz
- **数据库名**: playedu
- **JWT密钥**: playeduxyz

## 管理命令
```bash
# 启动服务
docker-compose up -d

# 停止服务
docker-compose down

# 查看日志
docker-compose logs -f

# 重启服务
docker-compose restart

# 查看状态
docker-compose ps
```

## 服务验证
所有端口测试返回200状态码，服务运行正常：
- ✅ API接口 (9700): 200
- ✅ PC端 (9800): 200  
- ✅ H5端 (9801): 200
- ✅ 管理端 (9900): 200

## 注意事项
1. 首次启动可能需要等待1-2分钟让服务完全初始化
2. 数据库数据会持久化到 `mysql-data` 卷中
3. 可以通过修改 `.env` 文件来更改端口配置
4. 日志文件会自动轮转，最大10MB，保留10个文件
