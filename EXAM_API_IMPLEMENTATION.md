# 考试系统 API 实现计划

## 📋 总体架构

基于现有的 playedu 项目结构，我们将创建一个新的 `playedu-exam` 模块来管理考试相关的业务逻辑。

```
playedu-exam/
├── src/main/java/xyz/playedu/exam/
│   ├── domain/                    # 数据模型
│   ├── mapper/                    # MyBatis Mapper
│   ├── service/                   # 业务逻辑
│   │   └── impl/
│   ├── controller/                # REST API
│   └── vo/                        # 视图对象
└── src/main/resources/
    └── mapper/                    # SQL映射文件
```

## 🎯 核心实现步骤

### 1. 数据模型（Domain）

#### 考试表 (Exam.java)
```
- id: Long (主键)
- title: String (考试标题)
- type: String (考试类型)
- duration: Integer (考试时长，分钟)
- pass_score: Integer (及格分数)
- description: String (考试描述)
- admin_id: Integer (创建者ID)
- created_at: LocalDateTime (创建时间)
- updated_at: LocalDateTime (修改时间)
```

#### 试题表 (ExamQuestion.java)
```
- id: Long (主键)
- exam_id: Long (考试ID)
- title: String (题目)
- type: String (题型: single_choice/multiple_choice/fill_blank/essay)
- difficulty: String (难度: easy/medium/hard)
- score: Integer (分数)
- analysis: String (解析)
- answer: String (答案/参考答案)
- created_by: Integer (创建人ID)
- created_at: LocalDateTime (创建时间)
- updated_at: LocalDateTime (修改时间)
```

#### 试题选项表 (ExamQuestionOption.java)
```
- id: Long (主键)
- question_id: Long (试题ID)
- option_key: String (选项键: A/B/C/D)
- content: String (选项内容)
- is_correct: Integer (是否正确答案: 0/1)
- created_at: LocalDateTime (创建时间)
```

#### 导入日志表 (ExamImportLog.java)
```
- id: Long (主键)
- exam_id: Long (考试ID)
- admin_id: Integer (导入人ID)
- total_count: Integer (总条数)
- success_count: Integer (成功条数)
- error_count: Integer (失败条数)
- error_details: Text (错误详情JSON)
- created_at: LocalDateTime (导入时间)
```

### 2. Mapper 接口

- ExamMapper.java
  - insert(Exam)
  - updateById(Exam)
  - deleteById(id)
  - selectById(id)
  - selectByPage(pageNo, pageSize, title)
  - countByTitle(title)

- ExamQuestionMapper.java
  - insert(ExamQuestion)
  - updateById(ExamQuestion)
  - deleteById(id)
  - selectById(id)
  - selectByExamId(examId, pageNo, pageSize)
  - countByExamId(examId)
  - selectByExamIdAndType(examId, type)

- ExamQuestionOptionMapper.java
  - insert(ExamQuestionOption)
  - deleteByQuestionId(questionId)
  - selectByQuestionId(questionId)

- ExamImportLogMapper.java
  - insert(ExamImportLog)
  - selectByExamId(examId, pageNo, pageSize)

### 3. Service 接口

#### ExamService
```java
// 考试管理
Page<ExamVo> paginate(int pageNo, int pageSize, String title);
ExamVo detail(Long examId);
void create(ExamCreateRequest req);
void update(Long examId, ExamUpdateRequest req);
void delete(Long examId);
```

#### ExamQuestionService
```java
// 试题管理
Page<ExamQuestionVo> paginate(Long examId, int pageNo, int pageSize, String title, String type);
ExamQuestionVo detail(Long questionId);
void create(Long examId, ExamQuestionCreateRequest req);
void update(Long questionId, ExamQuestionUpdateRequest req);
void delete(Long questionId);

// 选项管理
void saveOptions(Long questionId, List<OptionRequest> options);
List<ExamQuestionOptionVo> getOptions(Long questionId);
```

#### ExamImportService
```java
// 导入管理
ExamImportResult importQuestions(Long examId, MultipartFile file);
Page<ExamImportLogVo> importLogs(Long examId, int pageNo, int pageSize);
```

### 4. REST API 端点

#### 考试管理 API

```
GET    /backend/v1/exams                    # 考试列表
POST   /backend/v1/exams                    # 创建考试
GET    /backend/v1/exams/{id}              # 获取考试详情
PUT    /backend/v1/exams/{id}              # 修改考试
DELETE /backend/v1/exams/{id}              # 删除考试
```

#### 试题管理 API

```
GET    /backend/v1/exams/{id}/questions    # 试题列表
POST   /backend/v1/exams/{id}/questions    # 添加试题
GET    /backend/v1/questions/{id}          # 获取试题详情
PUT    /backend/v1/questions/{id}          # 修改试题
DELETE /backend/v1/questions/{id}          # 删除试题

GET    /backend/v1/questions/{id}/options  # 获取试题选项
```

#### 导入管理 API

```
POST   /backend/v1/exams/{id}/import       # 导入试题
GET    /backend/v1/exams/{id}/import-logs  # 导入日志
```

### 5. 请求/响应 DTO

#### 考试相关
- ExamCreateRequest
- ExamUpdateRequest
- ExamVo (视图对象)

#### 试题相关
- ExamQuestionCreateRequest
- ExamQuestionUpdateRequest
- ExamQuestionVo
- OptionRequest
- ExamQuestionOptionVo

#### 导入相关
- ExamImportRequest
- ExamImportResult
- ExamImportError
- ExamImportLogVo

## 📊 请求/响应示例

### 创建考试
```json
POST /backend/v1/exams

Request:
{
  "title": "JavaScript基础考试",
  "type": "1",
  "duration": 60,
  "pass_score": 60,
  "description": "这是一个JavaScript基础知识考试"
}

Response:
{
  "code": 0,
  "msg": "success",
  "data": {
    "id": 1,
    "title": "JavaScript基础考试",
    "type": "1",
    "duration": 60,
    "pass_score": 60,
    "created_at": "2025-10-30 16:55:00"
  }
}
```

### 添加试题
```json
POST /backend/v1/exams/1/questions

Request:
{
  "title": "1+1等于多少？",
  "type": "single_choice",
  "difficulty": "easy",
  "score": 5,
  "analysis": "基础运算",
  "options": [
    { "content": "1", "is_correct": 0 },
    { "content": "2", "is_correct": 1 },
    { "content": "3", "is_correct": 0 },
    { "content": "4", "is_correct": 0 }
  ]
}

Response:
{
  "code": 0,
  "msg": "success",
  "data": {
    "id": 1,
    "exam_id": 1,
    "title": "1+1等于多少？",
    "type": "single_choice",
    "difficulty": "easy",
    "score": 5,
    "created_at": "2025-10-30 10:00:00"
  }
}
```

### 导入试题
```json
POST /backend/v1/exams/1/import

Request: multipart/form-data
- file: 二进制 Excel 文件

Response:
{
  "code": 0,
  "msg": "success",
  "data": {
    "import_log_id": 1,
    "total_count": 50,
    "success_count": 48,
    "error_count": 2,
    "errors": [
      {
        "row": 10,
        "field": "type",
        "message": "无效的题型",
        "value": "判断题"
      }
    ]
  }
}
```

## 🛠️ 实现优先级

1. **第一阶段**：基础 CRUD
   - [ ] 数据模型和 Mapper
   - [ ] ExamService 和 ExamController
   - [ ] ExamQuestionService 和 ExamQuestionController

2. **第二阶段**：导入功能
   - [ ] ExamImportService (Excel 解析和验证)
   - [ ] ExamImportController

3. **第三阶段**：优化和完善
   - [ ] 权限控制
   - [ ] 错误处理
   - [ ] 日志记录
   - [ ] 性能优化

## ⚙️ 技术栈

- Spring Boot 2.x
- MyBatis
- MySQL
- Apache POI (Excel 处理)
- Lombok
- Validated (数据验证)

## 📝 关键业务逻辑

### 导入流程
1. 验证 Excel 文件格式
2. 逐行读取数据
3. 数据验证（字段完整性、类型等）
4. 批量插入数据库
5. 记录导入日志
6. 返回导入结果

### 选项管理
- 选择题必须 2-5 个选项
- 至少有一个正确答案
- 支持多个正确答案（多选题）

## 🔐 权限控制

需要添加的权限：
- `exam:view` - 查看考试
- `exam:create` - 创建考试
- `exam:update` - 修改考试
- `exam:delete` - 删除考试
- `exam:import` - 导入试题

---

**版本**: 1.0  
**更新日期**: 2025-10-30
