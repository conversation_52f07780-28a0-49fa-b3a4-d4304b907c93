#!/bin/bash

# PlayEdu 完整部署脚本
# 部署：后端API + 前端Admin + 前端PC + 前端H5

set -e

PROJECT_ROOT="/Users/<USER>/pa/codes/playedu"
API_PORT="${API_PORT:-9700}"
ADMIN_PORT="${ADMIN_PORT:-9900}"
PC_PORT="${PC_PORT:-9800}"
H5_PORT="${H5_PORT:-9801}"

echo "=========================================="
echo "PlayEdu 完整应用部署"
echo "=========================================="

cd "$PROJECT_ROOT"

# ============ 第1步：编译后端 ============
echo ""
echo "【1/8】编译后端 API..."
cd "$PROJECT_ROOT/playedu-api"
mvn clean package -DskipTests > /dev/null 2>&1
echo "✓ 后端编译完成"

# ============ 第2-4步：编译三个前端应用 ============
for APP in admin pc h5; do
  echo ""
  echo "【$(($(echo $APP | grep -o . | wc -l) + 1))/8】编译前端 $APP..."
  cd "$PROJECT_ROOT/playedu-$APP"
  rm -rf node_modules package-lock.json 2>/dev/null || true
  npm install --legacy-peer-deps > /dev/null 2>&1
  VITE_APP_URL=/api/ npm run build > /dev/null 2>&1
  echo "✓ 前端 $APP 编译完成"
done

# ============ 第5步：构建后端镜像 ============
echo ""
echo "【5/8】构建后端 Docker 镜像..."
cd "$PROJECT_ROOT"
docker build -f playedu-api/Dockerfile -t playedu-api:latest . > /dev/null 2>&1
echo "✓ 后端镜像构建完成"

# ============ 第6-8步：构建前端镜像 ============
for APP in admin pc h5; do
  echo ""
  echo "【$(($(echo $APP | grep -o . | wc -l) + 5))/8】构建前端 $APP Docker 镜像..."
  
  cat > "$PROJECT_ROOT/Dockerfile.$APP" << APPEOF
FROM registry.cn-hangzhou.aliyuncs.com/hzbs/node:20-alpine AS builder

WORKDIR /app

COPY playedu-$APP .

RUN npm install --legacy-peer-deps

RUN npm run build

FROM registry.cn-hangzhou.aliyuncs.com/hzbs/node:20-alpine

WORKDIR /app

RUN npm install -g http-server

COPY --from=builder /app/dist /app/dist

EXPOSE 80

CMD ["http-server", "/app/dist", "-p", "80", "--cors"]
APPEOF

  docker build -f "$PROJECT_ROOT/Dockerfile.$APP" -t playedu-$APP:latest . > /dev/null 2>&1
  echo "✓ 前端 $APP 镜像构建完成"
done

# ============ 第9步：停止旧容器 ============
echo ""
echo "【停止旧容器】清理旧的容器..."
docker rm -f playedu-api playedu-admin playedu-pc playedu-h5 2>/dev/null || true
sleep 2
echo "✓ 旧容器已清理"

# ============ 第10步：启动新容器 ============
echo ""
echo "【启动新容器】启动应用服务..."

# 启动后端API
docker run -d \
  --name playedu-api \
  --network playedu \
  -p ${API_PORT}:9898 \
  -e DB_HOST=mysql \
  -e DB_PORT=3306 \
  -e DB_NAME=playedu \
  -e DB_USER=root \
  -e DB_PASS=playeduxyz \
  -e SA_TOKEN_IS_CONCURRENT=false \
  -e SA_TOKEN_JWT_SECRET_KEY=playeduxyz \
  playedu-api:latest > /dev/null

# 启动前端应用
docker run -d \
  --name playedu-admin \
  --network playedu \
  -p ${ADMIN_PORT}:80 \
  playedu-admin:latest > /dev/null

docker run -d \
  --name playedu-pc \
  --network playedu \
  -p ${PC_PORT}:80 \
  playedu-pc:latest > /dev/null

docker run -d \
  --name playedu-h5 \
  --network playedu \
  -p ${H5_PORT}:80 \
  playedu-h5:latest > /dev/null

sleep 5

echo "✓ 所有容器启动完成"

# ============ 完成 ============
echo ""
echo "=========================================="
echo "✅ 部署完成！"
echo "=========================================="
echo ""
echo "📋 服务地址："
echo "  后端 API:     http://localhost:${API_PORT}"
echo "  前端 Admin:   http://localhost:${ADMIN_PORT}"
echo "  前端 PC:      http://localhost:${PC_PORT}"
echo "  前端 H5:      http://localhost:${H5_PORT}"
echo ""
echo "📝 查看日志:"
echo "  后端日志:     docker logs -f playedu-api"
echo "  Admin日志:    docker logs -f playedu-admin"
echo "  PC日志:       docker logs -f playedu-pc"
echo "  H5日志:       docker logs -f playedu-h5"
echo ""
echo "🔄 查看所有运行容器:"
echo "  docker ps | grep playedu"
echo ""
echo "📊 验证容器运行状态:"
docker ps | grep playedu
echo ""
