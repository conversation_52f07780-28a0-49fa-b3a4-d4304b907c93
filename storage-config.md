# PlayEdu 存储配置指南

PlayEdu 支持多种云存储服务，包括 MinIO、阿里云 OSS、AWS S3 等兼容 S3 协议的对象存储服务。

## 存储配置位置

存储配置在管理后台的 **系统配置** -> **S3存储** 中进行设置。

需要配置以下参数：

### 1. AccessKey（访问密钥）
- 阿里云 OSS：AccessKey ID
- MinIO：Access Key
- AWS S3：Access Key ID

### 2. SecretKey（私有访问密钥）
- 阿里云 OSS：AccessKey Secret  
- MinIO：Secret Key
- AWS S3：Secret Access Key

### 3. Bucket（存储空间）
- 存储文件的容器名称
- 需要在对应的云存储服务中先创建

### 4. Region（区域）
- 阿里云 OSS：如 `oss-cn-hangzhou`
- AWS S3：如 `us-west-1`
- MinIO：通常为空或 `us-east-1`

### 5. Endpoint（节点）
- 阿里云 OSS：`https://oss-cn-hangzhou.aliyuncs.com`
- MinIO：`http://your-minio-server:9000`
- AWS S3：`https://s3.amazonaws.com`

## 常用云存储配置示例

### 阿里云 OSS 配置
```
AccessKey: 你的AccessKeyId
SecretKey: 你的AccessKeySecret
Bucket: 你的存储空间名称
Region: oss-cn-hangzhou
Endpoint: https://oss-cn-hangzhou.aliyuncs.com
```

### 腾讯云 COS 配置
```
AccessKey: 你的SecretId
SecretKey: 你的SecretKey
Bucket: playedu-1250000000
Region: ap-beijing
Endpoint: https://cos.ap-beijing.myqcloud.com
```

#### 腾讯云COS跨域配置（重要）

由于PlayEdu采用前端直传方式，必须在腾讯云COS控制台配置跨域规则：

1. **登录腾讯云COS控制台**
2. **选择存储桶 → 安全管理 → 跨域访问CORS设置**
3. **添加规则**：
   - 允许的Origin: `http://localhost:9900` (或你的实际域名)
   - 允许的方法: `PUT, POST, GET, OPTIONS`
   - 允许的Headers: `*`
   - 暴露的Headers: `ETag`
   - 缓存时间: `3600`

### MinIO 配置（私有化部署）
```
AccessKey: minioadmin
SecretKey: minioadmin
Bucket: playedu
Region: us-east-1
Endpoint: http://localhost:9000
```

### AWS S3 配置
```
AccessKey: 你的AWS AccessKeyId
SecretKey: 你的AWS SecretAccessKey
Bucket: 你的S3存储桶名称
Region: us-west-1
Endpoint: https://s3.amazonaws.com
```

## 配置验证

配置完成后，可以通过以下方式验证：

1. 在管理后台上传文件测试
2. 学员端上传头像测试
3. 系统会自动检查存储配置是否完整

## 注意事项

1. **权限设置**：确保存储空间为私有访问，系统会自动设置文件访问权限
2. **跨域配置**：
   - 阿里云OSS：需要在OSS控制台配置跨域规则
   - 腾讯云COS：需要在COS控制台配置跨域规则
3. **HTTPS支持**：建议使用HTTPS协议，确保数据传输安全
4. **CDN加速**：可以配合CDN服务提升文件访问速度
5. **腾讯云COS**：需要在COS控制台开启S3兼容模式

## 故障排查

### 上传失败常见原因

1. **配置错误**：检查AccessKey、SecretKey、Bucket、Region、Endpoint是否正确
2. **网络问题**：确保服务器能够访问云存储服务
3. **权限问题**：确保AccessKey有足够的权限进行上传操作
4. **Bucket不存在**：确保配置的Bucket已经创建
5. **跨域问题**：确保已正确配置跨域规则（阿里云OSS、腾讯云COS）

## 方案C：后端代理接口（无需修改云存储配置）

### 原理
通过后端Java接口转发前端请求到云存储，绕过浏览器CORS限制。

### 实现步骤

1. **后端代理控制器** (`UploadProxyController.java`)
   - 自动识别腾讯云COS URL
   - 转发PUT/POST/GET/OPTIONS请求
   - 处理请求头和响应头

2. **前端自动切换**
   - 检测到COS URL时自动使用代理路径
   - 非COS URL保持原逻辑不变
   - 支持MinIO、阿里云OSS、AWS S3直连

3. **配置方法**
   - 无需修改云存储控制台配置
   - 无需修改Nginx配置
   - 自动适配现有S3配置

### 优点
- ✅ 完全不改动云存储配置
- ✅ 支持本地开发环境
- ✅ 兼容所有云存储服务
- ✅ 自动识别和切换

### 适用场景
- 无法修改云存储控制台配置
- 本地开发环境调试
- 多存储服务混合使用

## 故障排查

### CORS跨域错误排查

**错误现象**：
```
Access to XMLHttpRequest at 'https://xxx.cos.ap-guangzhou.myqcloud.com/...' 
from origin 'http://localhost:9900' has been blocked by CORS policy: 
Response to preflight request doesn't pass access control check: 
No 'Access-Control-Allow-Origin' header is present on the requested resource.
```

**解决方案**：
1. **使用方案C（后端代理）**：
   - 已自动实现，无需手动配置
   - 前端会自动识别COS URL并使用代理
   - 检查UploadProxyController是否正常运行

2. **手动配置云存储控制台**：
   
   **腾讯云COS**：
   - 登录腾讯云控制台 → COS → 存储桶 → 安全管理 → 跨域访问CORS设置
   - 添加规则：Origin为`http://localhost:9900`，方法包含PUT、OPTIONS

   **阿里云OSS**：
   - 登录阿里云控制台 → OSS → Bucket → 权限管理 → 跨域设置
   - 添加规则：Origin为`http://localhost:9900`，方法包含PUT、OPTIONS

3. **验证配置**：
   - 使用浏览器开发者工具查看网络请求
   - 确认请求URL是否自动切换为代理路径
   - 检查OPTIONS预检请求是否返回200状态码
   - 检查响应头是否包含`Access-Control-Allow-Origin`

如果上传失败，请检查：
- 存储配置参数是否正确
- 网络连接是否正常
- 存储服务是否可访问
- 存储空间权限设置

系统会提示"存储服务未配置"错误，表示配置不完整或有误。