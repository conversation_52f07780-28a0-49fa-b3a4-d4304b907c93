<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="xyz.playedu.common.mapper.LdapDepartmentMapper">

    <resultMap id="BaseResultMap" type="xyz.playedu.common.domain.LdapDepartment">
            <id property="id" column="id" jdbcType="INTEGER"/>
            <result property="uuid" column="uuid" jdbcType="VARCHAR"/>
            <result property="departmentId" column="department_id" jdbcType="INTEGER"/>
            <result property="dn" column="dn" jdbcType="VARCHAR"/>
            <result property="createdAt" column="created_at" jdbcType="TIMESTAMP"/>
            <result property="updatedAt" column="updated_at" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,uuid,department_id,
        dn,created_at,updated_at
    </sql>
</mapper>
