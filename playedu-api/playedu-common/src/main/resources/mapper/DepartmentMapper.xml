<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="xyz.playedu.common.mapper.DepartmentMapper">

    <resultMap id="BaseResultMap" type="xyz.playedu.common.domain.Department">
            <id property="id" column="id" jdbcType="INTEGER"/>
            <result property="name" column="name" jdbcType="VARCHAR"/>
            <result property="parentId" column="parent_id" jdbcType="INTEGER"/>
            <result property="parentChain" column="parent_chain" jdbcType="VARCHAR"/>
            <result property="sort" column="sort" jdbcType="INTEGER"/>
            <result property="createdAt" column="created_at" jdbcType="TIMESTAMP"/>
            <result property="updatedAt" column="updated_at" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,name,parent_id,
        parent_chain,sort,created_at,
        updated_at
    </sql>
    <select id="getDepartmentsUserCount" resultType="xyz.playedu.common.types.mapper.DepartmentsUserCountMapRes">
        SELECT `dep_id`, count(1) AS `total`
        FROM user_department
        GROUP BY dep_id;
    </select>
</mapper>
