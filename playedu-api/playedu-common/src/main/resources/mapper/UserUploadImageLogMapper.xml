<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="xyz.playedu.common.mapper.UserUploadImageLogMapper">

    <resultMap id="BaseResultMap" type="xyz.playedu.common.domain.UserUploadImageLog">
            <id property="id" column="id" jdbcType="INTEGER"/>
            <result property="userId" column="user_id" jdbcType="INTEGER"/>
            <result property="typed" column="typed" jdbcType="VARCHAR"/>
            <result property="scene" column="scene" jdbcType="VARCHAR"/>
            <result property="driver" column="driver" jdbcType="VARCHAR"/>
            <result property="path" column="path" jdbcType="VARCHAR"/>
            <result property="url" column="url" jdbcType="VARCHAR"/>
            <result property="size" column="size" jdbcType="BIGINT"/>
            <result property="name" column="name" jdbcType="VARCHAR"/>
            <result property="createdAt" column="created_at" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,user_id,typed,
        scene,driver,path,
        url,size,name,
        created_at
    </sql>
</mapper>
