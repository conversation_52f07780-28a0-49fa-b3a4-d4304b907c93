<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="xyz.playedu.common.mapper.AdminUserRoleMapper">

    <resultMap id="BaseResultMap" type="xyz.playedu.common.domain.AdminUserRole">
            <result property="adminId" column="admin_id" jdbcType="INTEGER"/>
            <result property="roleId" column="role_id" jdbcType="INTEGER"/>
    </resultMap>

    <sql id="Base_Column_List">
        admin_id,role_id
    </sql>
</mapper>
