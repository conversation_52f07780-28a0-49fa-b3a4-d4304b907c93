<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="xyz.playedu.common.mapper.LdapSyncDepartmentDetailMapper">

    <!-- 通用映射结果集 -->
    <resultMap id="BaseResultMap" type="xyz.playedu.common.domain.LdapSyncDepartmentDetail">
        <id column="id" property="id"/>
        <result column="record_id" property="recordId"/>
        <result column="department_id" property="departmentId"/>
        <result column="uuid" property="uuid"/>
        <result column="dn" property="dn"/>
        <result column="name" property="name"/>
        <result column="action" property="action"/>
        <result column="created_at" property="createdAt"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, record_id, department_id, uuid, dn, name, action, created_at
    </sql>

</mapper> 