/*
 * Copyright (C) 2023 杭州白书科技有限公司
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package xyz.playedu.common.constant;

public class CommonConstant {

    public static final Integer MINUS_ONE = -1;
    public static final Integer ZERO = 0;
    public static final Integer ONE = 1;
    public static final Integer TWO = 2;
    public static final Integer THREE = 3;
    public static final Integer FOUR = 4;
    public static final Integer FIVE = 5;
    public static final Integer SIX = 6;
    public static final Integer SEVEN = 7;
    public static final Integer EIGHT = 8;
    public static final Integer TEN = 10;
    public static final Integer ELEVEN = 11;
    public static final Integer TWELVE = 12;
    public static final Integer FIFTEEN = 15;
    public static final Integer TWENTY_ONE = 21;
    public static final Integer TWENTY_TWO = 22;
    public static final Integer TWENTY_FIVE = 25;
    public static final Integer THIRTY = 30;
    public static final Integer THIRTY_ONE = 31;
    public static final Integer THIRTY_TWO = 32;
    public static final Integer THIRTY_FIVE = 35;
    public static final Integer FORTY_ONE = 41;
    public static final Integer FORTY_TWO = 42;
    public static final Integer FORTY_FIVE = 45;
    public static final Integer SIXTY = 60;
    public static final Integer ONE_HUNDRED = 100;

    public static final String SUCCESS = "0";
    public static final String FAIL = "-1";
    public static final String UTF8 = "UTF-8";

    public static final String GLOBAL = "GLOBAL";
    public static final String COURSE = "COURSE";
    public static final String OFFLINE_COURSE = "OFFLINE_COURSE";
    public static final String REQUIRED_COURSE = "REQUIRED_COURSE";
    public static final String OPTIONAL_COURSE = "OPTIONAL_COURSE";
    public static final String STUDY_TASK = "STUDY_TASK";
    public static final String EXAM_TASK = "EXAM_TASK";
    public static final String LOGIN = "LOGIN";
    public static final String MANUAL = "MANUAL";
    public static final String SHOP = "SHOP";
    public static final String SHOP_ROLLBACK = "SHOP_ROLLBACK";

    public static final String MANUAL_TIP = "手动调整";
    public static final String LOGIN_TIP = "首次登录";
    public static final String SHOP_TIP = "积分商城购物";
    public static final String SHOP_ROLLBACK_TIP = "积分商城退还积分";

    public static final String HOUR_FINISHED_TIP = "课时学习完成";
    public static final String COURSE_FINISHED_TIP = "线上课程学习完成";
    public static final String OFFLINE_COURSE_FINISHED_TIP = "线下课程签到完成";
    public static final String EXAM_FINISHED_TIP = "考试首次合格";
    public static final String TASK_EXAM_FINISHED_TIP = "考试任务首次合格";
    public static final String TASK_STUDY_FINISHED_TIP = "学习任务学习完成";

    public static final String EXAM = "EXAM";
    public static final String EXAM_RANGE_BEGIN = "EXAM_RANGE_BEGIN";
    public static final String EXAM_RANGE_END = "EXAM_RANGE_END";
    public static final String STUDY = "STUDY";
    public static final String STUDY_RANGE = "STUDY_RANGE";
    public static final String RETAKE = "RETAKE";
    public static final String USER_CREATE = "USER_CREATE";

    public static final String OTHER = "OTHER";

    public static final String TRANSCODE = "document-transcode/";
    public static final String SEPARATOR = ".";
    public static final String JSON = "json";

    public static final String OTHER_DEP = "其他（待设置部门）";

    public static final Integer LOGIN_CHANNEL_LOCAL = 0;
    public static final Integer LOGIN_CHANNEL_WORK_WECHAT = 1;
    public static final Integer LOGIN_CHANNEL_FEISHU = 2;
    public static final Integer LOGIN_CHANNEL_DINGTALK = 3;
    public static final Integer LOGIN_CHANNEL_LDAP = 4;
    public static final Integer LOGIN_CHANNEL_YUNZHIJIA = 7;
    public static final Integer LOGIN_CHANNEL_CREC = 8;

    public static final int TYPE_DEP = 0;
    public static final int TYPE_USER = 1;
    public static final int TYPE_GROUP = 2;

    public static final String OIDC_CLIENT_SECRET_POST = "client_secret_post";
    public static final String OIDC_CLIENT_SECRET_BASIC = "client_secret_basic";
    public static final String OIDC_NONE = "none";

    public static final String EXPORT_ALL_USER_RECORD = "export_all_user_record";

    public static final String EXTRA_V = "v1";

    public static final String S3_ACCESS_MODE_PATH = "path";
    public static final String S3_ACCESS_MODE_VIRTUAL = "virtual";
}
