/*
 * Copyright (C) 2023 杭州白书科技有限公司
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package xyz.playedu.common.constant;

/**
 * <AUTHOR>
 *
 * @create 2023/4/11 10:12
 */
public class ConfigConstant {

    public static final String SYSTEM_NAME = "system.name";
    public static final String SYSTEM_LOGO = "system.logo";
    public static final String SYSTEM_API_URL = "system.api_url";
    public static final String SYSTEM_PC_URL = "system.pc_url";
    public static final String SYSTEM_H5_URL = "system.h5_url";

    public static final String MEMBER_DEFAULT_AVATAR = "member.default_avatar";

    public static final String S3_SERVICE = "s3.service";
    public static final String S3_ACCESS_KEY = "s3.access_key";
    public static final String S3_SECRET_KEY = "s3.secret_key";
    public static final String S3_BUCKET = "s3.bucket";
    public static final String S3_REGION = "s3.region";
    public static final String S3_ENDPOINT = "s3.endpoint";
    public static final String S3_DOMAIN = "s3.domain";

    public static final String LDAP_ENABLED = "ldap.enabled";
    public static final String LDAP_URL = "ldap.url";
    public static final String LDAP_ADMIN_USER = "ldap.admin_user";
    public static final String LDAP_ADMIN_PASS = "ldap.admin_pass";
    public static final String LDAP_BASE_DN = "ldap.base_dn";
}
