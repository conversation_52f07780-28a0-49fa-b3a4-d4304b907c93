/*
 * Copyright (C) 2023 杭州白书科技有限公司
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package xyz.playedu.common.types.paginate;

import lombok.Data;

/**
 * <AUTHOR>
 *
 * @create 2023/3/24 16:10
 */
@Data
public class UserCourseRecordPaginateFilter {
    private Integer courseId;
    private String email;
    private String name;
    private String idCard;
    private String sortField;
    private String sortAlgo;
    private Integer pageStart;
    private Integer pageSize;
    private Integer userId;
    private Integer isFinished;
}
