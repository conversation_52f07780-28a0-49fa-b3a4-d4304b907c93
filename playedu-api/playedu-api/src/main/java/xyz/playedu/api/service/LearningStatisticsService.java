package xyz.playedu.api.service;

import java.util.List;
import java.util.Map;

/**
 * 学习统计分析 Service
 */
public interface LearningStatisticsService {

    /**
     * 获取课程维度统计数据
     */
    List<Map<String, Object>> getCourseStatistics(String search);

    /**
     * 获取人员维度统计数据
     */
    List<Map<String, Object>> getUserStatistics(String search);

    /**
     * 获取课程详情 - 学员列表
     */
    List<Map<String, Object>> getCourseUserDetail(Integer courseId, String search);

    /**
     * 获取人员详情 - 课程列表
     */
    List<Map<String, Object>> getUserCourseDetail(Integer userId, String search);
}
