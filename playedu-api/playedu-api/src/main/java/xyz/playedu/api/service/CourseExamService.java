package xyz.playedu.api.service;

import java.util.List;
import java.util.Map;

public interface CourseExamService {

    /**
     * 获取课程关联的考试列表
     */
    List<Map<String, Object>> getExamsByCourseId(Long courseId);

    /**
     * 添加课程-考试关联
     */
    void addExamToCourse(Long courseId, Long examId);

    /**
     * 批量添加课程-考试关联
     */
    void addExamsToCourse(Long courseId, List<Long> examIds);

    /**
     * 删除课程-考试关联
     */
    void removeExamFromCourse(Long courseId, Long examId);

    /**
     * 删除课程的所有考试关联
     */
    void removeAllExamsFromCourse(Long courseId);

    /**
     * 更新课程的考试关联（先删除所有，再添加新的）
     */
    void updateCourseExams(Long courseId, List<Long> examIds);
}
