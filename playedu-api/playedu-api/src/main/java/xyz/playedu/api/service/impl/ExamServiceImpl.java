/*
 * Copyright (C) 2023 杭州白书科技有限公司
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package xyz.playedu.api.service.impl;

import java.time.LocalDateTime;
import java.util.*;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import xyz.playedu.api.service.ExamService;
import xyz.playedu.common.exception.ServiceException;
import xyz.playedu.course.domain.*;
import xyz.playedu.course.mapper.*;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;

@Service
@Slf4j
public class ExamServiceImpl implements ExamService {

    @Autowired
    private ExamMapper examMapper;
    
    @Autowired
    private ExamUserRecordMapper examUserRecordMapper;
    
    @Autowired
    private ExamUserAnswerMapper examUserAnswerMapper;
    
    @Autowired
    private ExamQuestionMapper examQuestionMapper;
    
    @Autowired
    private ExamQuestionOptionMapper examQuestionOptionMapper;

    @Autowired
    private xyz.playedu.course.mapper.CourseExamMapper courseExamMapper;


    @Override
    public List<Map<String, Object>> paginate(
            int pageNo, int pageSize, String title, int[] total) {
        if (title == null) {
            title = "";
        }
        long count = examMapper.countByTitle("%" + title + "%");
        if (total != null && total.length > 0) {
            total[0] = (int) count;
        }
        int offset = (pageNo - 1) * pageSize;
        List<Exam> list = examMapper.selectByPage(offset, pageSize, "%" + title + "%");

        List<Map<String, Object>> result = new ArrayList<>();
        for (Exam exam : list) {
            result.add(convert2Map(exam));
        }
        return result;
    }

    @Override
    public Map<String, Object> detail(Long examId) {
        Exam exam = examMapper.selectById(examId);
        if (exam == null) {
            throw new ServiceException("考试不存在");
        }
        Map<String, Object> result = convert2Map(exam);
        
        // 获取所有题目和选项
        List<xyz.playedu.course.domain.ExamQuestion> questions = 
            examQuestionMapper.selectByExamId(examId, 0, 1000, "", "");
        List<Map<String, Object>> questionsList = new ArrayList<>();
        
        for (xyz.playedu.course.domain.ExamQuestion question : questions) {
            Map<String, Object> questionMap = new HashMap<>();
            questionMap.put("id", question.getId());
            questionMap.put("exam_id", question.getExamId());
            questionMap.put("title", question.getTitle());
            questionMap.put("type", question.getType());
            
            Integer score = question.getScore();
            log.info("📝 [Exam Detail] 题目ID: {}, 标题: {}, 分数: {}", 
                question.getId(), question.getTitle(), score);
            questionMap.put("score", score); // 添加分数字段
            
            questionMap.put("sort", question.getSort());
            questionMap.put("status", question.getStatus());
            
            // 获取选项
            List<xyz.playedu.course.domain.ExamQuestionOption> options = 
                examQuestionOptionMapper.selectByQuestionId(question.getId());
            List<Map<String, Object>> optionsList = new ArrayList<>();
            
            for (xyz.playedu.course.domain.ExamQuestionOption option : options) {
                Map<String, Object> optionMap = new HashMap<>();
                optionMap.put("id", option.getId());
                optionMap.put("question_id", option.getQuestionId());
                optionMap.put("content", option.getContent());
                optionMap.put("option_key", option.getOptionKey());
                optionMap.put("is_correct", option.getIsCorrect());
                optionsList.add(optionMap);
            }
            
            questionMap.put("options", optionsList);
            questionsList.add(questionMap);
        }
        
        log.info("📝 [Exam Detail] 返回题目总数: {}", questionsList.size());
        if (!questionsList.isEmpty()) {
            log.info("📝 [Exam Detail] 第一题完整数据: {}", questionsList.get(0));
        }
        
        result.put("questions", questionsList);
        return result;
    }

    @Override
    public void create(String title, String type, Integer duration, Integer pass_score,
            String description, Long adminId) {
        Exam exam = Exam.builder()
                .title(title)
                .type(type)
                .duration(duration)
                .passScore(pass_score)
                .description(description)
                .adminId(adminId)
                .createdAt(LocalDateTime.now())
                .updatedAt(LocalDateTime.now())
                .build();
        examMapper.insert(exam);
    }

    @Override
    public void update(Long examId, String title, String type, Integer duration, Integer pass_score,
            String description) {
        Exam exam = examMapper.selectById(examId);
        if (exam == null) {
            throw new ServiceException("考试不存在");
        }
        exam.setTitle(title);
        exam.setType(type);
        exam.setDuration(duration);
        exam.setPassScore(pass_score);
        exam.setDescription(description);
        exam.setUpdatedAt(LocalDateTime.now());
        examMapper.updateById(exam);
    }

    @Override
    public void delete(Long examId) {
        Exam exam = examMapper.selectById(examId);
        if (exam == null) {
            throw new ServiceException("考试不存在");
        }
        examMapper.deleteById(examId);
    }

    @Override
    public Exam findById(Long examId) {
        return examMapper.selectById(examId);
    }

    @Override
    public Map<String, Object> submit(Long examId, List<Map<String, Object>> answers, Integer userId) {
        log.info("🔵 开始处理考试提交: examId={}, userId={}, answers.size={}", examId, userId, answers.size());
        
        // 查询题目与分值
        List<xyz.playedu.course.domain.ExamQuestion> questions =
                examQuestionMapper.selectByExamId(examId, 0, 2000, "", "");
        
        log.info("📚 查询到考试题目数量: {}", questions.size());

        // 题目ID -> 正确选项、分值
        Map<Long, List<String>> correctMap = new HashMap<>();
        Map<Long, Integer> scoreMap = new HashMap<>();
        int total = 0;
        for (xyz.playedu.course.domain.ExamQuestion q : questions) {
            List<xyz.playedu.course.domain.ExamQuestionOption> opts =
                    examQuestionOptionMapper.selectByQuestionId(q.getId());
            List<String> correctKeys = new ArrayList<>();
            for (xyz.playedu.course.domain.ExamQuestionOption op : opts) {
                if (op.getIsCorrect() != null && op.getIsCorrect() == 1) {
                    correctKeys.add(op.getOptionKey());
                }
            }
            correctMap.put(q.getId(), correctKeys);
            int score = q.getScore() == null ? 1 : q.getScore();
            scoreMap.put(q.getId(), score);
            total += score;
        }

        int got = 0;
        List<Long> wrong = new ArrayList<>();
        for (Map<String, Object> ans : answers) {
            Long qid = ((Number) ans.get("question_id")).longValue();
            List<String> val = (List<String>) ans.get("answer");
            List<String> correct = correctMap.getOrDefault(qid, new ArrayList<>());
            if (val == null) val = new ArrayList<>();
            val.sort(String::compareTo);
            correct.sort(String::compareTo);
            if (val.equals(correct)) {
                got += scoreMap.getOrDefault(qid, 0);
            } else {
                wrong.add(qid);
            }
        }

        Map<String, Object> exam = convert2Map(examMapper.selectById(examId));
        boolean passed = got >= ((Number) exam.get("pass_score")).intValue();
        
        log.info("📊 计算结果: 得分={}/{}, 通过={}, 错题数={}", got, total, passed, wrong.size());

        // 保存考试记录
        if (userId != null && userId > 0) {
            log.info("💾 保存考试记录: userId={} ", userId);
            Date now = new Date();

            // 1. 查询是否已有记录
            LambdaQueryWrapper<ExamUserRecord> qWrap = new LambdaQueryWrapper<>();
            qWrap.eq(ExamUserRecord::getExamId, examId).eq(ExamUserRecord::getUserId, userId);
            ExamUserRecord record = examUserRecordMapper.selectOne(qWrap);

            boolean isNew = false;
            if (record == null) {
                isNew = true;
                record = new ExamUserRecord();
                record.setExamId(examId);
                record.setUserId(userId);
                record.setUserName("");
                record.setUserEmail("");
                record.setCreatedAt(now);
            }

            // 更新公共字段
            record.setStatus(2); // 已完成
            record.setTotalScore(total);
            record.setScore(got);
            record.setPassStatus(passed ? 1 : 0);
            record.setStartAt(now);
            record.setEndAt(now);
            record.setDuration(0);
            record.setAnswerCount(answers.size());
            record.setCorrectCount(answers.size() - wrong.size());
            record.setUpdatedAt(now);

            if (isNew) {
                examUserRecordMapper.insert(record);
            } else {
                examUserRecordMapper.updateById(record);
            }

            Long recordId = record.getId();

            // 如果不是新记录，删除旧答案
            if (!isNew) {
                examUserAnswerMapper.delete(new LambdaQueryWrapper<ExamUserAnswer>()
                        .eq(ExamUserAnswer::getExamRecordId, recordId));
            }
            // 2. 保存每道题的答题记录
            for (Map<String, Object> ans : answers) {
                Long qid = ((Number) ans.get("question_id")).longValue();
                List<String> userAnswer = (List<String>) ans.get("answer");
                List<String> correctAnswer = correctMap.getOrDefault(qid, new ArrayList<>());
                
                // 找到题目类型
                ExamQuestion question = questions.stream()
                        .filter(q -> q.getId().equals(qid))
                        .findFirst()
                        .orElse(null);
                
                if (question != null) {
                    ExamUserAnswer answerRecord = new ExamUserAnswer();
                    answerRecord.setExamRecordId(recordId);
                    answerRecord.setExamId(examId);
                    answerRecord.setUserId(userId);
                    answerRecord.setQuestionId(qid);
                    answerRecord.setQuestionType(question.getType());
                    answerRecord.setUserAnswer(userAnswer == null ? "" : String.join(",", userAnswer));
                    answerRecord.setCorrectAnswer(String.join(",", correctAnswer));
                    
                    // 判断是否正确
                    List<String> sortedUser = new ArrayList<>(userAnswer == null ? new ArrayList<>() : userAnswer);
                    List<String> sortedCorrect = new ArrayList<>(correctAnswer);
                    sortedUser.sort(String::compareTo);
                    sortedCorrect.sort(String::compareTo);
                    boolean isCorrect = sortedUser.equals(sortedCorrect);
                    
                    answerRecord.setIsCorrect(isCorrect ? 1 : 0);
                    answerRecord.setScore(isCorrect ? scoreMap.getOrDefault(qid, 0) : 0);
                    answerRecord.setAnswerTime(0); // 实际应该记录答题时长
                    answerRecord.setCreatedAt(now);
                    
                    examUserAnswerMapper.insert(answerRecord);
                }
            }
        }

        Map<String, Object> result = new HashMap<>();
        result.put("score", got);
        result.put("total", total);
        result.put("passed", passed);
        result.put("wrong_questions", wrong);
        return result;
    }

    @Override
    public List<Map<String, Object>> getExamsByCourseId(Long courseId) {
        List<Long> examIds = courseExamMapper.selectExamIdsByCourseId(courseId);
        
        List<Map<String, Object>> result = new ArrayList<>();
        for (Long examId : examIds) {
            Exam exam = examMapper.selectById(examId);
            if (exam != null) {
                result.add(convert2Map(exam));
            }
        }
        return result;
    }

    @Override
    public Map<String, Object> validateExamScore(Long examId) {
        Map<String, Object> result = new HashMap<>();
        
        // 1. 查询考试信息
        Exam exam = examMapper.selectById(examId);
        if (exam == null) {
            result.put("valid", false);
            result.put("message", "考试不存在");
            return result;
        }
        
        // 2. 计算题目总分
        List<xyz.playedu.course.domain.ExamQuestion> questions =
                examQuestionMapper.selectByExamId(examId, 0, 1000, "", "");
        int totalScore = 0;
        for (xyz.playedu.course.domain.ExamQuestion q : questions) {
            totalScore += (q.getScore() == null ? 0 : q.getScore());
        }
        
        // 3. 校验
        Integer passScore = exam.getPassScore();
        boolean valid = totalScore > 0 && totalScore >= passScore;
        
        result.put("valid", valid);
        result.put("totalScore", totalScore);
        result.put("passScore", passScore);
        result.put("questionCount", questions.size());
        result.put("examTitle", exam.getTitle());
        
        if (!valid) {
            if (totalScore == 0) {
                result.put("message", String.format(
                    "考试「%s」还没有添加题目，无法进行考试", 
                    exam.getTitle()
                ));
            } else {
                result.put("message", String.format(
                    "考试「%s」配置不合理：题目总分(%d分) < 及格分数(%d分)，学生无法通过考试。请联系管理员调整配置", 
                    exam.getTitle(), totalScore, passScore
                ));
            }
        }
        
        log.info("校验考试分数配置: examId={}, valid={}, totalScore={}, passScore={}", 
            examId, valid, totalScore, passScore);
        
        return result;
    }

    private Map<String, Object> convert2Map(Exam exam) {
        Map<String, Object> map = new HashMap<>();
        map.put("id", exam.getId());
        map.put("title", exam.getTitle());
        map.put("type", exam.getType());
        map.put("duration", exam.getDuration());
        map.put("pass_score", exam.getPassScore());
        map.put("description", exam.getDescription());
        map.put("created_at", exam.getCreatedAt());
        map.put("updated_at", exam.getUpdatedAt());
        return map;
    }
}
