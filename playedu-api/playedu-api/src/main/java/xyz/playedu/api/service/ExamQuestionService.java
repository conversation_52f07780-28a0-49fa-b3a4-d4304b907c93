/*
 * Copyright (C) 2023 杭州白书科技有限公司
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package xyz.playedu.api.service;

import java.util.List;
import java.util.Map;
import org.springframework.web.multipart.MultipartFile;
import xyz.playedu.api.request.backend.ExamQuestionCreateRequest;
import xyz.playedu.api.request.backend.ExamQuestionUpdateRequest;

public interface ExamQuestionService {
    List<Map<String, Object>> paginate(Long examId, int pageNo, int pageSize, String title, String type, int[] total);
    
    Map<String, Object> detail(Long questionId);
    
    void create(Long examId, ExamQuestionCreateRequest req);

    void update(Long questionId, ExamQuestionUpdateRequest req);

    void delete(Long questionId);

    Map<String, Object> importQuestions(Long examId, MultipartFile file) throws Exception;

    Map<String, Object> getImportLogs(Long examId, int page, int size);
}
