package xyz.playedu.api.controller.backend;

import java.util.*;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import xyz.playedu.api.service.LearningStatisticsService;
import xyz.playedu.common.annotation.BackendPermission;
import xyz.playedu.common.annotation.Log;
import xyz.playedu.common.constant.BPermissionConstant;
import xyz.playedu.common.constant.BusinessTypeConstant;
import xyz.playedu.common.types.JsonResponse;

/**
 * 学习统计分析 Controller
 */
@RestController
@Slf4j
@RequestMapping("/backend/v1/learning-statistics")
public class LearningStatisticsController {

    @Autowired
    private LearningStatisticsService learningStatisticsService;

    /**
     * 课程维度统计
     */
    @BackendPermission(slug = BPermissionConstant.COURSE)
    @GetMapping("/course")
    @SneakyThrows
    @Log(title = "学习统计-课程维度", businessType = BusinessTypeConstant.GET)
    public JsonResponse courseLearningStatistics(
            @RequestParam(defaultValue = "1") Integer page,
            @RequestParam(defaultValue = "1000") Integer size,
            @RequestParam(required = false) String search) {
        
        try {
            List<Map<String, Object>> result = learningStatisticsService.getCourseStatistics(search);
            
            Map<String, Object> data = new HashMap<>();
            data.put("data", result);
            
            return JsonResponse.data(data);
        } catch (Exception e) {
            log.error("获取课程统计数据异常", e);
            return JsonResponse.error("获取课程统计数据失败");
        }
    }

    /**
     * 人员维度统计
     */
    @BackendPermission(slug = BPermissionConstant.USER_INDEX)
    @GetMapping("/user")
    @SneakyThrows
    @Log(title = "学习统计-人员维度", businessType = BusinessTypeConstant.GET)
    public JsonResponse userLearningStatistics(
            @RequestParam(defaultValue = "1") Integer page,
            @RequestParam(defaultValue = "1000") Integer size,
            @RequestParam(required = false) String search) {
        
        try {
            List<Map<String, Object>> result = learningStatisticsService.getUserStatistics(search);
            
            Map<String, Object> data = new HashMap<>();
            data.put("data", result);
            
            return JsonResponse.data(data);
        } catch (Exception e) {
            log.error("获取人员统计数据异常", e);
            return JsonResponse.error("获取人员统计数据失败");
        }
    }

    /**
     * 课程详情 - 学员列表
     */
    @BackendPermission(slug = BPermissionConstant.COURSE)
    @GetMapping("/course/{courseId}/users")
    @SneakyThrows
    @Log(title = "学习统计-课程详情", businessType = BusinessTypeConstant.GET)
    public JsonResponse courseUserDetail(
            @PathVariable Integer courseId,
            @RequestParam(defaultValue = "1") Integer page,
            @RequestParam(defaultValue = "1000") Integer size,
            @RequestParam(required = false) String search) {
        
        try {
            List<Map<String, Object>> result = learningStatisticsService.getCourseUserDetail(courseId, search);
            
            Map<String, Object> data = new HashMap<>();
            data.put("data", result);
            
            return JsonResponse.data(data);
        } catch (Exception e) {
            log.error("获取课程详情异常", e);
            return JsonResponse.error("获取课程详情失败");
        }
    }

    /**
     * 人员详情 - 课程列表
     */
    @BackendPermission(slug = BPermissionConstant.USER_INDEX)
    @GetMapping("/user/{userId}/courses")
    @SneakyThrows
    @Log(title = "学习统计-人员详情", businessType = BusinessTypeConstant.GET)
    public JsonResponse userCourseDetail(
            @PathVariable Integer userId,
            @RequestParam(defaultValue = "1") Integer page,
            @RequestParam(defaultValue = "1000") Integer size,
            @RequestParam(required = false) String search) {
        
        try {
            List<Map<String, Object>> result = learningStatisticsService.getUserCourseDetail(userId, search);
            
            Map<String, Object> data = new HashMap<>();
            data.put("data", result);
            
            return JsonResponse.data(data);
        } catch (Exception e) {
            log.error("获取人员课程详情异常", e);
            return JsonResponse.error("获取人员课程详情失败");
        }
    }

    /**
     * 数据导出
     */
    @BackendPermission(slug = BPermissionConstant.COURSE)
    @GetMapping("/export")
    @SneakyThrows
    @Log(title = "学习统计-数据导出", businessType = BusinessTypeConstant.GET)
    public JsonResponse export(
            @RequestParam String type,
            @RequestParam(required = false) String search) {
        
        try {
            if ("course".equals(type)) {
                List<Map<String, Object>> data = learningStatisticsService.getCourseStatistics(search);
                return JsonResponse.data(data);
            } else if ("user".equals(type)) {
                List<Map<String, Object>> data = learningStatisticsService.getUserStatistics(search);
                return JsonResponse.data(data);
            }
            return JsonResponse.error("导出类型错误");
        } catch (Exception e) {
            log.error("导出数据异常", e);
            return JsonResponse.error("导出数据失败");
        }
    }
}
