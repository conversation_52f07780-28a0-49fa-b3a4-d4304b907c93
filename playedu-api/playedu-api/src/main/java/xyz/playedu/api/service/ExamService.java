/*
 * Copyright (C) 2023 杭州白书科技有限公司
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package xyz.playedu.api.service;

import java.util.List;
import java.util.Map;
import xyz.playedu.course.domain.Exam;

public interface ExamService {
    List<Map<String, Object>> paginate(
            int pageNo, int pageSize, String title, int[] total);

    Map<String, Object> detail(Long examId);

    void create(String title, String type, Integer duration, Integer pass_score,
            String description, Long adminId);

    void update(Long examId, String title, String type, Integer duration, Integer pass_score,
            String description);

    void delete(Long examId);

    Exam findById(Long examId);

    /**
     * 获取课程关联的所有考试
     */
    List<Map<String, Object>> getExamsByCourseId(Long courseId);

    /**
     * 提交考试答案并返回结果
     */
    Map<String,Object> submit(Long examId, List<Map<String,Object>> answers, Integer userId);

    /**
     * 校验考试分数配置是否合理（题目总分 >= 及格分数）
     * @param examId 考试ID
     * @return Map 包含 valid(是否合理), totalScore(题目总分), passScore(及格分数), message(错误信息)
     */
    Map<String, Object> validateExamScore(Long examId);
}
