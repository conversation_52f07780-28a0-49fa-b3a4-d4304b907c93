package xyz.playedu.api.service.impl;

import java.util.*;
import java.util.stream.Collectors;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;

import lombok.extern.slf4j.Slf4j;
import xyz.playedu.api.service.LearningStatisticsService;
import xyz.playedu.common.domain.Department;
import xyz.playedu.common.domain.User;
import xyz.playedu.common.domain.UserDepartment;
import xyz.playedu.common.mapper.DepartmentMapper;
import xyz.playedu.common.mapper.UserDepartmentMapper;
import xyz.playedu.common.service.UserService;
import xyz.playedu.course.domain.Course;
import xyz.playedu.course.domain.CourseExam;
import xyz.playedu.course.domain.ExamUserRecord;
import xyz.playedu.course.domain.UserCourseRecord;
import xyz.playedu.course.mapper.CourseExamMapper;
import xyz.playedu.course.mapper.CourseMapper;
import xyz.playedu.course.mapper.ExamUserRecordMapper;
import xyz.playedu.course.mapper.UserCourseRecordMapper;

/**
 * 学习统计分析 Service 实现
 */
@Service
@Slf4j
public class LearningStatisticsServiceImpl implements LearningStatisticsService {

    @Autowired
    private UserService userService;
    
    @Autowired
    private CourseMapper courseMapper;
    
    @Autowired
    private UserCourseRecordMapper userCourseRecordMapper;
    
    @Autowired
    private CourseExamMapper courseExamMapper;
    
    @Autowired
    private ExamUserRecordMapper examUserRecordMapper;
    
    @Autowired
    private UserDepartmentMapper userDepartmentMapper;
    
    @Autowired
    private DepartmentMapper departmentMapper;

    /**
     * 获取课程维度统计数据
     */
    @Override
    public List<Map<String, Object>> getCourseStatistics(String search) {
        // 获取所有课程或按名称搜索
        LambdaQueryWrapper<Course> courseWrapper = new LambdaQueryWrapper<>();
        if (StringUtils.isNotBlank(search)) {
            courseWrapper.like(Course::getTitle, search);
        }
        List<Course> courses = courseMapper.selectList(courseWrapper);
        
        List<Map<String, Object>> result = new ArrayList<>();
        
        for (Course course : courses) {
            Map<String, Object> courseStats = new HashMap<>();
            courseStats.put("id", course.getId());
            courseStats.put("title", course.getTitle());
            
            // 获取该课程的所有学习记录
            List<UserCourseRecord> records = userCourseRecordMapper.selectList(
                new LambdaQueryWrapper<UserCourseRecord>()
                    .eq(UserCourseRecord::getCourseId, course.getId())
            );
            
            int totalUsers = records.size();
            int completedUsers = (int) records.stream()
                .filter(r -> r.getIsFinished() == 1)
                .count();
            
            double completionRate = totalUsers > 0 ? (double) completedUsers / totalUsers : 0;
            
            courseStats.put("total_users", totalUsers);
            courseStats.put("completed_users", completedUsers);
            courseStats.put("completion_rate", completionRate);
            
            // 获取课程关联的考试成绩
            Map<String, Object> examStats = getExamStatistics(course.getId());
            courseStats.putAll(examStats);
            
            result.add(courseStats);
        }
        
        return result;
    }

    /**
     * 获取考试统计信息
     */
    private Map<String, Object> getExamStatistics(Integer courseId) {
        Map<String, Object> stats = new HashMap<>();
        
        // 获取该课程关联的所有考试ID
        List<CourseExam> courseExams = courseExamMapper.selectList(
            new LambdaQueryWrapper<CourseExam>()
                .eq(CourseExam::getCourseId, courseId.longValue())
        );
        
        if (courseExams.isEmpty()) {
            stats.put("avg_score", 0.0);
            stats.put("passing_users", 0);
            stats.put("passing_rate", 0.0);
            return stats;
        }

        List<Long> examIds = courseExams.stream()
            .map(CourseExam::getExamId)
            .collect(Collectors.toList());

        // 查询该课程的考试成绩记录
        List<ExamUserRecord> records = examUserRecordMapper.selectList(
            new LambdaQueryWrapper<ExamUserRecord>()
                .in(ExamUserRecord::getExamId, examIds)
        );

        if (records.isEmpty()) {
            stats.put("avg_score", 0.0);
            stats.put("passing_users", 0);
            stats.put("passing_rate", 0.0);
            return stats;
        }

        // 计算平均分
        double avgScore = records.stream()
            .mapToInt(r -> r.getScore() != null ? r.getScore() : 0)
            .average()
            .orElse(0);
        stats.put("avg_score", Math.round(avgScore * 10.0) / 10.0);

        // 计算及格人数和及格率
        long passingUsers = records.stream()
            .filter(r -> r.getPassStatus() != null && r.getPassStatus() == 1)
            .count();
        stats.put("passing_users", (int) passingUsers);
        stats.put("passing_rate", (double) passingUsers / records.size());

        return stats;
    }

    /**
     * 获取人员维度统计数据
     */
    @Override
    public List<Map<String, Object>> getUserStatistics(String search) {
        // 获取所有用户或按名称搜索
        LambdaQueryWrapper<User> userWrapper = new LambdaQueryWrapper<>();
        if (StringUtils.isNotBlank(search)) {
            userWrapper.like(User::getName, search)
                .or()
                .like(User::getEmail, search);
        }
        List<User> users = userService.list(userWrapper);
        
        List<Map<String, Object>> result = new ArrayList<>();
        
        for (User user : users) {
            Map<String, Object> userStats = new HashMap<>();
            userStats.put("id", user.getId());
            userStats.put("name", user.getName());
            userStats.put("email", user.getEmail());
            
            // 获取用户所属部门
            userStats.put("department_name", getDepartmentName(user.getId()));
            
            // 获取该用户的所有课程学习记录
            List<UserCourseRecord> records = userCourseRecordMapper.selectList(
                new LambdaQueryWrapper<UserCourseRecord>()
                    .eq(UserCourseRecord::getUserId, user.getId())
            );
            
            int totalCourses = records.size();
            int completedCourses = (int) records.stream()
                .filter(r -> r.getIsFinished() == 1)
                .count();
            
            double completionRate = totalCourses > 0 ? (double) completedCourses / totalCourses : 0;
            
            userStats.put("total_courses", totalCourses);
            userStats.put("completed_courses", completedCourses);
            userStats.put("completion_rate", completionRate);
            
            // 获取用户的考试成绩统计
            List<ExamUserRecord> examRecords = examUserRecordMapper.selectList(
                new LambdaQueryWrapper<ExamUserRecord>()
                    .eq(ExamUserRecord::getUserId, user.getId())
            );
            
            if (!examRecords.isEmpty()) {
                double avgScore = examRecords.stream()
                    .mapToInt(r -> r.getScore() != null ? r.getScore() : 0)
                    .average()
                    .orElse(0);
                userStats.put("avg_score", Math.round(avgScore * 10.0) / 10.0);
                
                long passingCourses = examRecords.stream()
                    .filter(r -> r.getPassStatus() != null && r.getPassStatus() == 1)
                    .count();
                userStats.put("passing_courses", (int) passingCourses);
                userStats.put("passing_rate", (double) passingCourses / examRecords.size());
            } else {
                userStats.put("avg_score", 0.0);
                userStats.put("passing_courses", 0);
                userStats.put("passing_rate", 0.0);
            }
            
            // 获取最后学习时间
            if (!records.isEmpty()) {
                Date latestLearnAt = records.stream()
                    .map(UserCourseRecord::getUpdatedAt)
                    .filter(Objects::nonNull)
                    .max(Date::compareTo)
                    .orElse(null);
                userStats.put("latest_learn_at", latestLearnAt);
            } else {
                userStats.put("latest_learn_at", null);
            }
            
            result.add(userStats);
        }
        
        return result;
    }

    /**
     * 获取用户所属部门名称
     */
    private String getDepartmentName(Integer userId) {
        // 查询用户的所有部门
        List<UserDepartment> userDeps = userDepartmentMapper.selectList(
            new LambdaQueryWrapper<UserDepartment>()
                .eq(UserDepartment::getUserId, userId)
        );
        
        if (userDeps.isEmpty()) {
            return "";
        }
        
        // 获取部门名称
        List<Integer> depIds = userDeps.stream()
            .map(UserDepartment::getDepId)
            .collect(Collectors.toList());
        
        List<Department> departments = departmentMapper.selectList(
            new LambdaQueryWrapper<Department>()
                .in(Department::getId, depIds)
        );
        
        return departments.stream()
            .map(Department::getName)
            .collect(Collectors.joining(","));
    }

    /**
     * 获取课程详情 - 学员列表
     */
    @Override
    public List<Map<String, Object>> getCourseUserDetail(Integer courseId, String search) {
        List<Map<String, Object>> result = new ArrayList<>();
        
        // 获取该课程的所有学习记录
        List<UserCourseRecord> records = userCourseRecordMapper.selectList(
            new LambdaQueryWrapper<UserCourseRecord>()
                .eq(UserCourseRecord::getCourseId, courseId)
        );
        
        for (UserCourseRecord record : records) {
            User user = userService.getById(record.getUserId());
            if (user == null) {
                continue;
            }
            
            // 应用搜索过滤
            if (StringUtils.isNotBlank(search)) {
                if (!user.getName().contains(search) && !user.getEmail().contains(search)) {
                    continue;
                }
            }
            
            Map<String, Object> userDetail = new HashMap<>();
            userDetail.put("user_id", user.getId());
            userDetail.put("user_name", user.getName());
            userDetail.put("user_email", user.getEmail());
            userDetail.put("department_name", "");
            userDetail.put("is_completed", record.getIsFinished());
            userDetail.put("completed_at", record.getFinishedAt());
            userDetail.put("learn_duration", record.getFinishedCount() * 45 * 60);
            userDetail.put("highest_score", 0);
            userDetail.put("pass_status", null);
            userDetail.put("latest_learn_at", record.getUpdatedAt());
            
            result.add(userDetail);
        }
        
        return result;
    }

    /**
     * 获取人员详情 - 课程列表
     */
    @Override
    public List<Map<String, Object>> getUserCourseDetail(Integer userId, String search) {
        List<Map<String, Object>> result = new ArrayList<>();
        
        // 获取该用户的所有课程学习记录
        List<UserCourseRecord> records = userCourseRecordMapper.selectList(
            new LambdaQueryWrapper<UserCourseRecord>()
                .eq(UserCourseRecord::getUserId, userId)
        );
        
        for (UserCourseRecord record : records) {
            Course course = courseMapper.selectById(record.getCourseId());
            if (course == null) {
                continue;
            }
            
            // 应用搜索过滤
            if (StringUtils.isNotBlank(search)) {
                if (!course.getTitle().contains(search)) {
                    continue;
                }
            }
            
            Map<String, Object> courseDetail = new HashMap<>();
            courseDetail.put("course_id", course.getId());
            courseDetail.put("course_name", course.getTitle());
            courseDetail.put("course_category", "");
            courseDetail.put("is_completed", record.getIsFinished());
            courseDetail.put("completed_at", record.getFinishedAt());
            courseDetail.put("learn_duration", record.getFinishedCount() * 45 * 60);
            courseDetail.put("exam_score", 0);
            courseDetail.put("pass_status", null);
            courseDetail.put("latest_learn_at", record.getUpdatedAt());
            courseDetail.put("progress", record.getProgress() != null ? record.getProgress() : 0);
            
            result.add(courseDetail);
        }
        
        return result;
    }
}
