/*
 * Copyright (C) 2023 杭州白书科技有限公司
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package xyz.playedu.api.request.backend;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class ExamQuestionImportRequest {

    private List<QuestionData> questions;

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    @Builder
    public static class QuestionData {
        private Integer rowNumber;  // Excel行号
        private String title;       // 题目标题
        private String type;        // 题型
        private String difficulty;  // 难度
        private Integer score;      // 分数
        private String analysis;    // 解析
        private String correctAnswer; // 正确答案
        private String optionA;     // 选项A
        private String optionB;     // 选项B
        private String optionC;     // 选项C
        private String optionD;     // 选项D
        private String optionE;     // 选项E
        private String optionF;     // 选项F

        // 验证数据完整性
        public boolean isValid() {
            return title != null && !title.trim().isEmpty() &&
                   type != null && !type.trim().isEmpty() &&
                   difficulty != null && !difficulty.trim().isEmpty() &&
                   score != null && score > 0 &&
                   correctAnswer != null && !correctAnswer.trim().isEmpty();
        }
    }
}
