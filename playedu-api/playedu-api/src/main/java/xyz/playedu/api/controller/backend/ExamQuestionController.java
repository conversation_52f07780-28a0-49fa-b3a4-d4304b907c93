/*
 * Copyright (C) 2023 杭州白书科技有限公司
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package xyz.playedu.api.controller.backend;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import xyz.playedu.common.annotation.Log;
import xyz.playedu.common.constant.BusinessTypeConstant;
import xyz.playedu.common.types.JsonResponse;
import xyz.playedu.api.request.backend.ExamQuestionCreateRequest;
import xyz.playedu.api.request.backend.ExamQuestionUpdateRequest;
import xyz.playedu.api.request.backend.ExamQuestionImportRequest;
import xyz.playedu.api.service.ExamQuestionService;


@RestController
@RequestMapping("/backend/v1/exams")
@Slf4j
@Validated
public class ExamQuestionController {

    @Autowired
    private ExamQuestionService examQuestionService;

    @GetMapping("/{examId}/questions")
    @Log(title = "考试试题-列表", businessType = BusinessTypeConstant.GET)
    public JsonResponse list(
            @PathVariable Long examId,
            @RequestParam(defaultValue = "1") int page,
            @RequestParam(defaultValue = "10") int size,
            @RequestParam(required = false) String title,
            @RequestParam(required = false) String type
    ) {
        int[] total = new int[1];
        List<Map<String, Object>> data = examQuestionService.paginate(examId, page, size, title, type, total);
        
        Map<String, Object> result = new HashMap<>();
        result.put("data", data);
        result.put("total", total[0]);
        result.put("page", page);
        result.put("size", size);
        return JsonResponse.data(result);
    }

    @PostMapping("/{examId}/questions")
    @Log(title = "考试试题-创建", businessType = BusinessTypeConstant.INSERT)
    public JsonResponse create(
            @PathVariable Long examId,
            @RequestBody ExamQuestionCreateRequest req
    ) {
        examQuestionService.create(examId, req);
        return JsonResponse.success("创建成功");
    }

    @GetMapping("/questions/{id}")
    @Log(title = "考试试题-详情", businessType = BusinessTypeConstant.GET)
    public JsonResponse detail(@PathVariable Long id) {
        Map<String, Object> data = examQuestionService.detail(id);
        return JsonResponse.data(data);
    }

    @PutMapping("/questions/{id}")
    @Log(title = "考试试题-修改", businessType = BusinessTypeConstant.UPDATE)
    public JsonResponse update(
            @PathVariable Long id,
            @RequestBody ExamQuestionUpdateRequest req
    ) {
        examQuestionService.update(id, req);
        return JsonResponse.success("修改成功");
    }

    @DeleteMapping("/questions/{id}")
    @Log(title = "考试试题-删除", businessType = BusinessTypeConstant.DELETE)
    public JsonResponse delete(@PathVariable Long id) {
        examQuestionService.delete(id);
        return JsonResponse.success("删除成功");
    }

    @PostMapping("/{examId}/import")
    @Log(title = "考试-导入试题", businessType = BusinessTypeConstant.INSERT)
    public JsonResponse importQuestions(
            @PathVariable Long examId,
            @RequestParam("file") MultipartFile file
    ) {
        try {
            Map<String, Object> result = examQuestionService.importQuestions(examId, file);
            return JsonResponse.data(result);
        } catch (Exception e) {
            log.error("导入试题失败", e);
            return JsonResponse.error("导入失败: " + e.getMessage());
        }
    }

    @GetMapping("/{examId}/import-logs")
    @Log(title = "考试-导入日志", businessType = BusinessTypeConstant.GET)
    public JsonResponse importLogs(
            @PathVariable Long examId,
            @RequestParam(defaultValue = "1") int page,
            @RequestParam(defaultValue = "10") int size
    ) {
        Map<String, Object> result = examQuestionService.getImportLogs(examId, page, size);
        return JsonResponse.data(result);
    }
}
