/*
 * Copyright (C) 2023 杭州白书科技有限公司
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package xyz.playedu.api.request.backend;

import jakarta.validation.constraints.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class ExamRequest {
    @NotBlank(message = "考试标题不能为空")
    @Size(max = 255, message = "考试标题最多255个字符")
    private String title;

    @NotBlank(message = "考试类型不能为空")
    private String type;

    @NotNull(message = "考试时长不能为空")
    @Min(1)
    @Max(480)
    private Integer duration;

    @NotNull(message = "及格分数不能为空")
    @Min(0)
    @Max(100)
    private Integer pass_score;

    private String description;
}
