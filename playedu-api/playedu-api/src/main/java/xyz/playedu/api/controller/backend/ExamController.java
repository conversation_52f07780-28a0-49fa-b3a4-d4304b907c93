/*
 * Copyright (C) 2023 杭州白书科技有限公司
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package xyz.playedu.api.controller.backend;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import xyz.playedu.api.request.backend.ExamRequest;
import xyz.playedu.api.service.ExamService;
import xyz.playedu.common.annotation.Log;
import xyz.playedu.common.constant.BusinessTypeConstant;
import xyz.playedu.common.context.BCtx;
import xyz.playedu.common.types.JsonResponse;

@RestController
@RequestMapping("/backend/v1/exams")
@Slf4j
@Validated
public class ExamController {

    @Autowired
    private ExamService examService;

    @GetMapping
    @Log(title = "考试-列表", businessType = BusinessTypeConstant.GET)
    public JsonResponse list(
            @RequestParam(defaultValue = "1") int page,
            @RequestParam(defaultValue = "10") int size,
            @RequestParam(required = false) String title
    ) {
        int[] total = new int[1];
        List<Map<String, Object>> data = examService.paginate(page, size, title, total);
        
        Map<String, Object> result = new HashMap<>();
        result.put("data", data);
        result.put("total", total[0]);
        result.put("page", page);
        result.put("size", size);
        
        return JsonResponse.data(result);
    }

    @PostMapping
    @Log(title = "考试-创建", businessType = BusinessTypeConstant.INSERT)
    public JsonResponse create(@RequestBody ExamRequest req) {
        Long adminId = BCtx.getAdminUser().getId().longValue();
        examService.create(req.getTitle(), req.getType(), req.getDuration(), 
                req.getPass_score(), req.getDescription(), adminId);
        return JsonResponse.success("创建成功");
    }

    @GetMapping("/{id}")
    @Log(title = "考试-详情", businessType = BusinessTypeConstant.GET)
    public JsonResponse detail(@PathVariable Long id) {
        Map<String, Object> data = examService.detail(id);
        return JsonResponse.data(data);
    }

    @PutMapping("/{id}")
    @Log(title = "考试-修改", businessType = BusinessTypeConstant.UPDATE)
    public JsonResponse update(
            @PathVariable Long id,
            @RequestBody ExamRequest req
    ) {
        examService.update(id, req.getTitle(), req.getType(), req.getDuration(), 
                req.getPass_score(), req.getDescription());
        return JsonResponse.success("修改成功");
    }

    @DeleteMapping("/{id}")
    @Log(title = "考试-删除", businessType = BusinessTypeConstant.DELETE)
    public JsonResponse delete(@PathVariable Long id) {
        examService.delete(id);
        return JsonResponse.success("删除成功");
    }
}
