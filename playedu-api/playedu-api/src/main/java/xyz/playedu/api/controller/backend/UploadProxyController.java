package xyz.playedu.api.controller.backend;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.*;
import org.springframework.web.bind.annotation.*;

import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.servlet.ServletInputStream;
import jakarta.servlet.annotation.MultipartConfig;
import java.io.IOException;
import java.io.InputStream;
import java.util.List;
import java.util.Map;
import java.util.HashMap;
import java.util.Date;
import java.util.ArrayList;
import java.net.URL;

import xyz.playedu.common.types.JsonResponse;
import xyz.playedu.common.service.AppConfigService;
import xyz.playedu.common.types.config.S3Config;

import com.qcloud.cos.COSClient;
import com.qcloud.cos.auth.BasicCOSCredentials;
import com.qcloud.cos.auth.COSCredentials;
import com.qcloud.cos.model.CompleteMultipartUploadRequest;
import com.qcloud.cos.model.CompleteMultipartUploadResult;
import com.qcloud.cos.model.GeneratePresignedUrlRequest;
import com.qcloud.cos.model.InitiateMultipartUploadRequest;
import com.qcloud.cos.model.InitiateMultipartUploadResult;
import com.qcloud.cos.model.ObjectMetadata;
import com.qcloud.cos.model.PartETag;
import com.qcloud.cos.model.PutObjectRequest;
import com.qcloud.cos.region.Region;
import com.qcloud.cos.ClientConfig;
import com.qcloud.cos.http.HttpMethodName;

@RestController
@RequestMapping("/backend/v1/upload/cos-proxy")
@MultipartConfig
public class UploadProxyController {

    @Autowired
    private AppConfigService appConfigService;

    /**
     * 处理CORS预检请求 - 对所有端点生效
     */
    @RequestMapping(value = "/**", method = RequestMethod.OPTIONS)
    public ResponseEntity<Void> handleCorsPreFlight(HttpServletRequest request, HttpServletResponse response) {
        response.setHeader("Access-Control-Allow-Origin", "*");
        response.setHeader("Access-Control-Allow-Methods", "GET, POST, PUT, DELETE, OPTIONS, HEAD");
        response.setHeader("Access-Control-Allow-Headers", "Content-Type, Authorization, X-Requested-With, X-Original-COS-URL");
        response.setHeader("Access-Control-Max-Age", "3600");
        response.setHeader("Access-Control-Allow-Credentials", "true");
        response.setHeader("Access-Control-Expose-Headers", "ETag, Content-Type");
        return ResponseEntity.ok().build();
    }

    /**
     * 兼容分片上传的COS代理上传接口
     */
    @PutMapping("/upload")
    public ResponseEntity<?> uploadFile(
            @RequestParam("filename") String filename,
            @RequestParam("content-type") String contentType,
            @RequestParam(value = "upload_id", required = false) String uploadId,
            @RequestParam(value = "part_number", required = false) Integer partNumber,
            @RequestHeader(value = "X-Original-COS-URL", required = false) String originalCosUrl,
            HttpServletRequest request,
            HttpServletResponse response) {

        // 添加CORS响应头
        addCorsHeaders(response);
        
        System.out.println("=== COS SDK分片上传代理调试信息 ===");
        System.out.println("文件名: " + filename);
        System.out.println("内容类型: " + contentType);
        System.out.println("上传ID: " + uploadId);
        System.out.println("分片号: " + partNumber);
        System.out.println("原始COS URL: " + originalCosUrl);
        
        COSClient cosClient = null;
        
        try {
            // 获取S3配置
            S3Config s3Config = appConfigService.getS3Config();
            
            System.out.println("S3配置信息:");
            System.out.println("  Endpoint: " + s3Config.getEndpoint());
            System.out.println("  Bucket: " + s3Config.getBucket());
            System.out.println("  Region: " + s3Config.getRegion());
            System.out.println("  AccessKey: " + s3Config.getAccessKey());
            
            // 验证配置
            if (s3Config.getBucket() == null || s3Config.getEndpoint() == null || 
                s3Config.getAccessKey() == null || s3Config.getSecretKey() == null) {
                System.err.println("错误: COS配置不完整");
                return ResponseEntity.status(HttpStatus.BAD_REQUEST)
                    .body(JsonResponse.error("COS配置不完整"));
            }

            // 创建COS客户端
            COSCredentials cred = new BasicCOSCredentials(s3Config.getAccessKey(), s3Config.getSecretKey());
            Region region = new Region(s3Config.getRegion());
            ClientConfig clientConfig = new ClientConfig(region);
            cosClient = new COSClient(cred, clientConfig);
            
            // 如果是分片上传，使用uploadId和partNumber
            if (uploadId != null && partNumber != null && originalCosUrl != null) {
                return handleMultipartUpload(cosClient, s3Config, filename, contentType, uploadId, partNumber, originalCosUrl, request);
            }
            
            // 否则进行单文件上传
            return handleSimpleUpload(cosClient, s3Config, filename, contentType, originalCosUrl, request);
            
        } catch (Exception e) {
            // 更详细的错误处理
            System.err.println("=== 上传代理错误信息 ===");
            System.err.println("错误类型: " + e.getClass().getSimpleName());
            System.err.println("错误信息: " + e.getMessage());
            e.printStackTrace();
            
            String errorMsg = "上传失败: " + e.getMessage();
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                .body(JsonResponse.error(errorMsg));
        } finally {
            // 关闭COS客户端
            if (cosClient != null) {
                cosClient.shutdown();
            }
        }
    }

    /**
     * 处理分片上传
     */
    private ResponseEntity<?> handleMultipartUpload(
            COSClient cosClient,
            S3Config s3Config,
            String filename,
            String contentType,
            String uploadId,
            Integer partNumber,
            String originalCosUrl,
            HttpServletRequest request) throws IOException {
        
        System.out.println("=== 处理分片上传 ===");
        System.out.println("上传ID: " + uploadId);
        System.out.println("分片号: " + partNumber);
        
        // 提取原始对象键（从原始COS URL中）
        String objectKey = extractObjectKeyFromCosUrl(originalCosUrl);
        System.out.println("提取的对象键: " + objectKey);
        
        // 读取文件数据
        byte[] fileData = readRequestBody(request);
        System.out.println("读取的文件数据大小: " + fileData.length + " 字节");
        
        // 创建输入流
        InputStream inputStream = new java.io.ByteArrayInputStream(fileData);
        
        // 设置对象元数据
        ObjectMetadata metadata = new ObjectMetadata();
        metadata.setContentLength(fileData.length);
        metadata.setContentType(contentType);
        
        // 创建分片上传请求
        com.qcloud.cos.model.UploadPartRequest uploadPartRequest = new com.qcloud.cos.model.UploadPartRequest();
        uploadPartRequest.setBucketName(s3Config.getBucket());
        uploadPartRequest.setKey(objectKey);
        uploadPartRequest.setUploadId(uploadId);
        uploadPartRequest.setPartNumber(partNumber);
        uploadPartRequest.setInputStream(inputStream);
        uploadPartRequest.setPartSize(fileData.length);
        
        System.out.println("开始上传分片到COS...");
        System.out.println("Bucket: " + s3Config.getBucket());
        System.out.println("ObjectKey: " + objectKey);
        System.out.println("PartNumber: " + partNumber);
        
        // 执行分片上传
        com.qcloud.cos.model.UploadPartResult uploadPartResult = cosClient.uploadPart(uploadPartRequest);
        
        System.out.println("分片上传成功！");
        System.out.println("ETag: " + uploadPartResult.getETag());
        
        // 关闭输入流
        inputStream.close();

        // 返回成功响应给前端，包含ETag用于后续合并
        return ResponseEntity.ok(JsonResponse.data(
            new java.util.HashMap<String, Object>() {{
                put("objectKey", objectKey);
                put("partNumber", partNumber);
                put("etag", uploadPartResult.getETag());
                put("status", "success");
                put("message", "分片上传成功");
            }}
        ));
    }

    /**
     * 处理单文件上传
     */
    private ResponseEntity<?> handleSimpleUpload(
            COSClient cosClient,
            S3Config s3Config,
            String filename,
            String contentType,
            String originalCosUrl,
            HttpServletRequest request) throws IOException {
        
        System.out.println("=== 处理单文件上传 ===");
        
        // 提取原始对象键（从原始COS URL中）
        String objectKey = extractObjectKeyFromCosUrl(originalCosUrl);
        System.out.println("提取的对象键: " + objectKey);
        
        // 读取文件数据
        byte[] fileData = readRequestBody(request);
        System.out.println("读取的文件数据大小: " + fileData.length + " 字节");
        
        // 创建输入流
        InputStream inputStream = new java.io.ByteArrayInputStream(fileData);
        
        // 设置对象元数据
        ObjectMetadata metadata = new ObjectMetadata();
        metadata.setContentLength(fileData.length);
        metadata.setContentType(contentType);
        
        // 创建上传请求
        PutObjectRequest putObjectRequest = new PutObjectRequest(
            s3Config.getBucket(), 
            objectKey, 
            inputStream, 
            metadata
        );
        
        System.out.println("开始上传文件到COS...");
        System.out.println("Bucket: " + s3Config.getBucket());
        System.out.println("ObjectKey: " + objectKey);
        
        // 执行上传
        cosClient.putObject(putObjectRequest);
        
        System.out.println("文件上传成功！");
        
        // 关闭输入流
        inputStream.close();

        // 返回成功响应给前端
        return ResponseEntity.ok(JsonResponse.data(
            new java.util.HashMap<String, String>() {{
                put("objectKey", objectKey);
                put("status", "success");
                put("message", "文件上传成功");
            }}
        ));
    }

    /**
     * 获取S3/COS配置接口
     */
    @GetMapping("/config")
    public ResponseEntity<?> getS3Config(HttpServletResponse response) {
        addCorsHeaders(response);
        try {
            // 获取S3配置
            S3Config s3Config = appConfigService.getS3Config();
            
            // 验证配置
            if (s3Config.getBucket() == null || s3Config.getEndpoint() == null) {
                return ResponseEntity.status(HttpStatus.BAD_REQUEST)
                    .body(JsonResponse.error("S3/COS配置未设置"));
            }

            // 返回配置信息（不包含敏感信息）
            return ResponseEntity.ok(JsonResponse.data(
                new java.util.HashMap<String, String>() {{
                    put("bucket", s3Config.getBucket());
                    put("endpoint", s3Config.getEndpoint());
                    put("region", s3Config.getRegion());
                }}
            ));

        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                .body(JsonResponse.error("获取配置失败: " + e.getMessage()));
        }
    }

    /**
     * 获取分片上传ID
     */
    @PostMapping("/minio/upload-id")
    public ResponseEntity<?> getUploadId(@RequestBody Map<String, String> params, HttpServletResponse response) {
        addCorsHeaders(response);
        try {
            String extension = params.get("extension");
            
            System.out.println("=== 获取COS分片上传ID ===");
            System.out.println("文件扩展名: " + extension);
            
            if (extension == null || extension.isEmpty()) {
                return ResponseEntity.status(HttpStatus.BAD_REQUEST)
                    .body(JsonResponse.error("extension参数为空"));
            }
            
            // 生成随机文件名
            String randomName = java.util.UUID.randomUUID().toString().replace("-", "");
            String filename = randomName + "." + extension;
            System.out.println("生成的文件名: " + filename);
            
            // 获取S3配置
            S3Config s3Config = appConfigService.getS3Config();
            
            // 创建COS客户端
            COSCredentials cred = new BasicCOSCredentials(s3Config.getAccessKey(), s3Config.getSecretKey());
            Region region = new Region(s3Config.getRegion());
            ClientConfig clientConfig = new ClientConfig(region);
            COSClient cosClient = new COSClient(cred, clientConfig);
            
            // 生成对象键
            String objectKey = generateObjectPath(filename);
            System.out.println("生成的对象键: " + objectKey);
            
            // 初始化分片上传（不设置ContentType，COS会自动识别）
            InitiateMultipartUploadRequest initRequest = new InitiateMultipartUploadRequest(
                s3Config.getBucket(), objectKey);
            
            InitiateMultipartUploadResult initResult = cosClient.initiateMultipartUpload(initRequest);
            String uploadId = initResult.getUploadId();
            
            System.out.println("分片上传ID: " + uploadId);
            
            cosClient.shutdown();
            
            return ResponseEntity.ok(JsonResponse.data(
                new java.util.HashMap<String, String>() {{
                    put("uploadId", uploadId);
                    put("objectKey", objectKey);
                    put("filename", objectKey);
                    put("extension", extension);
                }}
            ));
            
        } catch (Exception e) {
            System.err.println("获取分片上传ID失败: " + e.getMessage());
            e.printStackTrace();
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                .body(JsonResponse.error("获取分片上传ID失败: " + e.getMessage()));
        }
    }

    /**
     * 获取分片预签名URL
     */
    @PostMapping("/minio/pre-sign-url")
    public ResponseEntity<?> getPreSignUrl(@RequestBody Map<String, Object> params, HttpServletResponse response) {
        addCorsHeaders(response);
        try {
            String uploadId = (String) params.get("uploadId");
            String objectKey = (String) params.get("objectKey");
            Integer partNumber = (Integer) params.get("partNumber");
            
            System.out.println("=== 获取COS分片预签名URL ===");
            System.out.println("上传ID: " + uploadId);
            System.out.println("对象键: " + objectKey);
            System.out.println("分片号: " + partNumber);
            
            // 获取S3配置
            S3Config s3Config = appConfigService.getS3Config();
            
            // 创建COS客户端
            COSCredentials cred = new BasicCOSCredentials(s3Config.getAccessKey(), s3Config.getSecretKey());
            Region region = new Region(s3Config.getRegion());
            ClientConfig clientConfig = new ClientConfig(region);
            COSClient cosClient = new COSClient(cred, clientConfig);
            
            // 生成预签名URL（兼容分片上传）
            Date expiration = new Date(System.currentTimeMillis() + 3600 * 1000); // 1小时有效期
            
            // 使用COS SDK生成预签名URL
            GeneratePresignedUrlRequest generatePresignedUrlRequest = 
                new GeneratePresignedUrlRequest(s3Config.getBucket(), objectKey, HttpMethodName.PUT);
            generatePresignedUrlRequest.setExpiration(expiration);
            
            // 添加分片上传参数到URL
            generatePresignedUrlRequest.addRequestParameter("uploadId", uploadId);
            generatePresignedUrlRequest.addRequestParameter("partNumber", partNumber.toString());
            
            URL presignedUrl = cosClient.generatePresignedUrl(generatePresignedUrlRequest);
            String presignedUrlStr = presignedUrl.toString();
            
            System.out.println("生成的预签名URL: " + presignedUrlStr);
            
            cosClient.shutdown();
            
            return ResponseEntity.ok(JsonResponse.data(
                new java.util.HashMap<String, String>() {{
                    put("presignedUrl", presignedUrlStr);
                    put("objectKey", objectKey);
                }}
            ));
            
        } catch (Exception e) {
            System.err.println("获取分片预签名URL失败: " + e.getMessage());
            e.printStackTrace();
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                .body(JsonResponse.error("获取分片预签名URL失败"));
        }
    }

    /**
     * 合并分片文件
     */
    @PostMapping("/minio/merge-file")
    public ResponseEntity<?> mergeFile(@RequestBody Map<String, Object> params, HttpServletResponse response) {
        addCorsHeaders(response);
        try {
            // 兼容前端不同的字段名格式
            String uploadId = (String) (params.get("uploadId") != null ? params.get("uploadId") : params.get("upload_id"));
            String objectKey = (String) params.get("objectKey");
            String filename = (String) params.get("filename");
            List<Map<String, Object>> parts = (List<Map<String, Object>>) params.get("parts");
            
            System.out.println("=== 合并COS分片文件 ===");
            System.out.println("上传ID: " + uploadId);
            System.out.println("对象键: " + objectKey);
            System.out.println("文件名: " + filename);
            System.out.println("分片数量: " + (parts != null ? parts.size() : 0));
            
            // 如果没有提供parts，可能是单文件上传，直接返回成功
            if (parts == null || parts.isEmpty()) {
                System.out.println("没有分片信息，可能是单文件上传，直接返回成功");
                
                // 获取S3配置
                S3Config s3Config = appConfigService.getS3Config();
                
                // 构建访问URL
                String accessUrl = buildAccessUrl(s3Config, objectKey != null ? objectKey : filename);
                
                return ResponseEntity.ok(JsonResponse.data(
                    new java.util.HashMap<String, String>() {{
                        put("objectKey", objectKey != null ? objectKey : filename);
                        put("accessUrl", accessUrl);
                        put("status", "success");
                        put("message", "单文件上传模式，无需合并");
                    }}
                ));
            }
            
            // 获取S3配置
            S3Config s3Config = appConfigService.getS3Config();
            
            // 创建COS客户端
            COSCredentials cred = new BasicCOSCredentials(s3Config.getAccessKey(), s3Config.getSecretKey());
            Region region = new Region(s3Config.getRegion());
            ClientConfig clientConfig = new ClientConfig(region);
            COSClient cosClient = new COSClient(cred, clientConfig);
            
            // 构建分片列表
            List<PartETag> partETags = new ArrayList<>();
            if (parts != null) {
                for (Map<String, Object> part : parts) {
                    Integer partNumber = (Integer) part.get("partNumber");
                    String eTag = (String) part.get("etag");
                    if (partNumber != null && eTag != null) {
                        partETags.add(new PartETag(partNumber, eTag));
                    }
                }
            }
            
            if (partETags.isEmpty()) {
                System.err.println("错误: 没有已上传的分片文件");
                return ResponseEntity.status(HttpStatus.BAD_REQUEST)
                    .body(JsonResponse.error("没有已上传的分片文件"));
            }
            
            // 完成分片上传
            CompleteMultipartUploadRequest compRequest = new CompleteMultipartUploadRequest(
                s3Config.getBucket(), objectKey, uploadId, partETags);
            
            CompleteMultipartUploadResult compResult = cosClient.completeMultipartUpload(compRequest);
            
            System.out.println("分片合并成功！");
            System.out.println("最终对象键: " + compResult.getKey());
            
            // 构建访问URL
            String accessUrl = buildAccessUrl(s3Config, objectKey);
            
            cosClient.shutdown();
            
            return ResponseEntity.ok(JsonResponse.data(
                new java.util.HashMap<String, String>() {{
                    put("objectKey", objectKey);
                    put("accessUrl", accessUrl);
                    put("status", "success");
                }}
            ));
            
        } catch (Exception e) {
            System.err.println("合并分片文件失败: " + e.getMessage());
            e.printStackTrace();
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                .body(JsonResponse.error("合并分片文件失败"));
        }
    }

    /**
     * 生成对象键（路径）
     */
    private String generateObjectPath(String filename) {
        // 使用时间戳和UUID生成唯一路径
        String timestamp = new java.text.SimpleDateFormat("yyyy/MM/dd").format(new java.util.Date());
        String uuid = java.util.UUID.randomUUID().toString().replace("-", "");
        return "uploads/" + timestamp + "/" + uuid + "/" + filename;
    }

    /**
     * 从COS URL中提取对象键
     */
    private String extractObjectKeyFromCosUrl(String cosUrl) {
        try {
            // COS URL格式: https://bucketname.cos.region.myqcloud.com/object-key
            // 或者: https://bucketname.cos.region.myqcloud.com/object-key?参数
            
            if (cosUrl == null || cosUrl.trim().isEmpty()) {
                throw new IllegalArgumentException("COS URL不能为空");
            }
            
            // 移除查询参数
            String urlWithoutParams = cosUrl.split("\\?")[0];
            
            // 提取对象键（移除协议和域名部分）
            String[] parts = urlWithoutParams.split("\\.cos\\.");
            if (parts.length < 2) {
                throw new IllegalArgumentException("无效的COS URL格式");
            }
            
            // 获取bucket之后的部分
            String afterBucket = parts[1];
            String[] pathParts = afterBucket.split("\\.myqcloud\\.com/");
            if (pathParts.length < 2) {
                throw new IllegalArgumentException("无法从COS URL中提取对象键");
            }
            
            return pathParts[1];
            
        } catch (Exception e) {
            System.err.println("提取对象键失败: " + e.getMessage());
            System.err.println("原始URL: " + cosUrl);
            throw new RuntimeException("从COS URL提取对象键失败: " + e.getMessage());
        }
    }

    /**
     * 读取请求体数据
     */
    private byte[] readRequestBody(HttpServletRequest request) throws IOException {
        try (ServletInputStream inputStream = request.getInputStream()) {
            return inputStream.readAllBytes();
        }
    }

    /**
     * 构建对象URL
     */
    private String buildObjectUrl(S3Config s3Config, String objectPath) {
        String endpoint = s3Config.getEndpoint();
        String bucket = s3Config.getBucket();
        
        System.out.println("=== 构建对象URL调试 ===");
        System.out.println("原始endpoint: " + endpoint);
        System.out.println("bucket: " + bucket);
        System.out.println("对象路径: " + objectPath);
        
        // 移除endpoint中的协议前缀（http://或https://）
        String endpointWithoutProtocol = endpoint.replaceFirst("^https?://", "");
        System.out.println("移除协议后的endpoint: " + endpointWithoutProtocol);
        
        // 如果endpoint已经包含bucket（已经是虚拟主机风格），直接使用
        if (endpointWithoutProtocol.startsWith(bucket + ".")) {
            String url = endpoint + "/" + objectPath;
            System.out.println("已经是虚拟主机风格，直接使用: " + url);
            return url;
        }
        
        // 构建虚拟主机风格URL：https://bucket.endpoint/objectPath
        String protocol = endpoint.startsWith("https://") ? "https://" : "http://";
        String url = String.format("%s%s.%s/%s", protocol, bucket, endpointWithoutProtocol, objectPath);
        System.out.println("构建的虚拟主机风格URL: " + url);
        return url;
    }

    /**
     * 构建文件访问URL
     */
    private String buildAccessUrl(S3Config s3Config, String objectPath) {
        String endpoint = s3Config.getEndpoint();
        String bucket = s3Config.getBucket();
        
        System.out.println("=== 构建访问URL调试 ===");
        System.out.println("原始endpoint: " + endpoint);
        System.out.println("bucket: " + bucket);
        System.out.println("对象路径: " + objectPath);
        
        // 移除endpoint中的协议前缀（http://或https://）
        String endpointWithoutProtocol = endpoint.replaceFirst("^https?://", "");
        System.out.println("移除协议后的endpoint: " + endpointWithoutProtocol);
        
        // 如果endpoint已经包含bucket（已经是虚拟主机风格），直接使用
        if (endpointWithoutProtocol.startsWith(bucket + ".")) {
            String url = endpoint + "/" + objectPath;
            System.out.println("已经是虚拟主机风格，直接使用: " + url);
            return url;
        }
        
        // 构建虚拟主机风格URL：https://bucket.endpoint/objectPath
        String protocol = endpoint.startsWith("https://") ? "https://" : "http://";
        String url = String.format("%s%s.%s/%s", protocol, bucket, endpointWithoutProtocol, objectPath);
        System.out.println("构建的访问URL: " + url);
        return url;
    }

    /**
     * 添加CORS响应头
     */
    private void addCorsHeaders(HttpServletResponse response) {
        response.setHeader("Access-Control-Allow-Origin", "*");
        response.setHeader("Access-Control-Allow-Methods", "GET, POST, PUT, DELETE, OPTIONS, HEAD");
        response.setHeader("Access-Control-Allow-Headers", "Content-Type, Authorization, X-Requested-With, X-Original-COS-URL");
        response.setHeader("Access-Control-Max-Age", "3600");
        response.setHeader("Access-Control-Allow-Credentials", "true");
        response.setHeader("Access-Control-Expose-Headers", "ETag, Content-Type");
    }


}