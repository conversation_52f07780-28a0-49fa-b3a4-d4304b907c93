/*
 * Copyright (C) 2023 杭州白书科技有限公司
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package xyz.playedu.api.request.backend;

import java.util.List;

import jakarta.validation.constraints.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class ExamQuestionUpdateRequest {
    @NotBlank(message = "题目不能为空")
    @Size(max = 500, message = "题目最多500个字符")
    private String title;

    @NotBlank(message = "难度不能为空")
    private String difficulty;

    @NotNull(message = "分数不能为空")
    @Min(1)
    @Max(100)
    private Integer score;

    private String analysis;
    private String answer;

    private List<OptionData> options;

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static class OptionData {
        private Long id;
        
        @NotBlank(message = "选项内容不能为空")
        private String content;

        @NotNull(message = "是否正确答案不能为空")
        private Integer is_correct;
    }
}
