package xyz.playedu.api.controller.backend;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import xyz.playedu.api.service.CourseExamService;
import xyz.playedu.common.types.JsonResponse;

import java.util.List;
import java.util.Map;

@Slf4j
@RestController
@RequestMapping("/backend/v1/courses")
public class CourseExamController {

    @Autowired
    private CourseExamService courseExamService;

    /**
     * 获取课程关联的考试列表
     */
    @GetMapping("/{courseId}/exams")
    public JsonResponse getExamsByCourseId(@PathVariable Long courseId) {
        List<Map<String, Object>> exams = courseExamService.getExamsByCourseId(courseId);
        JsonResponse response = JsonResponse.success("获取成功");
        response.setData(exams);
        return response;
    }

    /**
     * 添加考试关联
     */
    @PostMapping("/{courseId}/exams/{examId}")
    public JsonResponse addExam(
            @PathVariable Long courseId,
            @PathVariable Long examId
    ) {
        courseExamService.addExamToCourse(courseId, examId);
        return JsonResponse.success("添加成功");
    }

    /**
     * 批量添加考试关联
     */
    @PostMapping("/{courseId}/exams/batch")
    public JsonResponse addExams(
            @PathVariable Long courseId,
            @RequestBody Map<String, List<Long>> request
    ) {
        List<Long> examIds = request.get("exam_ids");
        courseExamService.addExamsToCourse(courseId, examIds);
        return JsonResponse.success("添加成功");
    }

    /**
     * 删除考试关联
     */
    @DeleteMapping("/{courseId}/exams/{examId}")
    public JsonResponse removeExam(
            @PathVariable Long courseId,
            @PathVariable Long examId
    ) {
        courseExamService.removeExamFromCourse(courseId, examId);
        return JsonResponse.success("删除成功");
    }

    /**
     * 更新课程的考试关联（批量替换）
     */
    @PutMapping("/{courseId}/exams")
    public JsonResponse updateExams(
            @PathVariable Long courseId,
            @RequestBody Map<String, List<Long>> request
    ) {
        List<Long> examIds = request.get("exam_ids");
        courseExamService.updateCourseExams(courseId, examIds);
        return JsonResponse.success("更新成功");
    }
}
