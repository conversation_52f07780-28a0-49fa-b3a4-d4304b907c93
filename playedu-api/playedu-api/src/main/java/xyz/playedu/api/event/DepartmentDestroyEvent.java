/*
 * Copyright (C) 2023 杭州白书科技有限公司
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package xyz.playedu.api.event;

import java.util.Date;
import lombok.Getter;
import lombok.Setter;
import org.springframework.context.ApplicationEvent;

/**
 * <AUTHOR>
 *
 * @create 2023/2/23 15:27
 */
@Getter
@Setter
public class DepartmentDestroyEvent extends ApplicationEvent {

    private Integer depId;
    private Integer adminId;
    private Date createdAt;

    public DepartmentDestroyEvent(Object source, Integer adminId, Integer depId) {
        super(source);
        this.adminId = adminId;
        this.depId = depId;
        this.createdAt = new Date();
    }
}
