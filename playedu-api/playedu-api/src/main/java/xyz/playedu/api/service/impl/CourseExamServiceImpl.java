package xyz.playedu.api.service.impl;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import xyz.playedu.api.service.CourseExamService;
import xyz.playedu.api.service.ExamService;
import xyz.playedu.common.exception.ServiceException;
import xyz.playedu.course.domain.CourseExam;
import xyz.playedu.course.mapper.CourseExamMapper;

import java.time.LocalDateTime;
import java.util.*;

@Service
@Slf4j
public class CourseExamServiceImpl implements CourseExamService {

    @Autowired
    private CourseExamMapper courseExamMapper;

    @Autowired
    private ExamService examService;

    @Override
    public List<Map<String, Object>> getExamsByCourseId(Long courseId) {
        // 获取所有考试ID
        List<Long> examIds = courseExamMapper.selectExamIdsByCourseId(courseId);
        
        if (examIds.isEmpty()) {
            return new ArrayList<>();
        }

        // 获取每个考试的详细信息
        List<Map<String, Object>> exams = new ArrayList<>();
        for (Long examId : examIds) {
            try {
                Map<String, Object> exam = examService.detail(examId);
                if (exam != null) {
                    exams.add(exam);
                }
            } catch (Exception e) {
                log.error("Failed to get exam detail for id: " + examId, e);
            }
        }

        return exams;
    }

    @Override
    public void addExamToCourse(Long courseId, Long examId) {
        if (courseId == null || courseId <= 0) {
            throw new ServiceException("课程ID不能为空");
        }
        if (examId == null || examId <= 0) {
            throw new ServiceException("考试ID不能为空");
        }

        // ⚠️ 校验考试分数配置
        Map<String, Object> validation = examService.validateExamScore(examId);
        if (!(Boolean) validation.get("valid")) {
            String message = (String) validation.get("message");
            throw new ServiceException(message);
        }

        // 检查是否已存在
        CourseExam existing = courseExamMapper.selectOne(
            new com.baomidou.mybatisplus.core.conditions.query.QueryWrapper<CourseExam>()
                .eq("course_id", courseId)
                .eq("exam_id", examId)
        );

        if (existing != null) {
            return; // 已存在，不需要重复添加
        }

        CourseExam courseExam = CourseExam.builder()
                .courseId(courseId)
                .examId(examId)
                .createdAt(LocalDateTime.now())
                .updatedAt(LocalDateTime.now())
                .build();

        courseExamMapper.insert(courseExam);
        
        log.info("✅ 课程关联考试成功: courseId={}, examId={}", courseId, examId);
    }

    @Override
    public void addExamsToCourse(Long courseId, List<Long> examIds) {
        if (examIds == null || examIds.isEmpty()) {
            return;
        }

        for (Long examId : examIds) {
            addExamToCourse(courseId, examId);
        }
    }

    @Override
    public void removeExamFromCourse(Long courseId, Long examId) {
        if (courseId == null || courseId <= 0) {
            throw new ServiceException("课程ID不能为空");
        }
        if (examId == null || examId <= 0) {
            throw new ServiceException("考试ID不能为空");
        }

        courseExamMapper.deleteByCourseIdAndExamId(courseId, examId);
    }

    @Override
    public void removeAllExamsFromCourse(Long courseId) {
        if (courseId == null || courseId <= 0) {
            throw new ServiceException("课程ID不能为空");
        }

        courseExamMapper.deleteByCourseId(courseId);
    }

    @Override
    public void updateCourseExams(Long courseId, List<Long> examIds) {
        if (courseId == null || courseId <= 0) {
            throw new ServiceException("课程ID不能为空");
        }

        // 先删除所有关联
        removeAllExamsFromCourse(courseId);

        // 再添加新的关联
        if (examIds != null && !examIds.isEmpty()) {
            addExamsToCourse(courseId, examIds);
        }
    }
}
