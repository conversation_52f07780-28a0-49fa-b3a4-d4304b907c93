package xyz.playedu.api.controller.frontend;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import xyz.playedu.api.service.ExamService;
import xyz.playedu.common.context.FCtx;
import xyz.playedu.common.types.JsonResponse;
import xyz.playedu.course.domain.ExamQuestion;
import xyz.playedu.course.domain.ExamQuestionOption;
import xyz.playedu.course.domain.ExamUserRecord;
import xyz.playedu.course.domain.ExamUserAnswer;
import xyz.playedu.course.mapper.ExamQuestionMapper;
import xyz.playedu.course.mapper.ExamQuestionOptionMapper;
import xyz.playedu.course.mapper.ExamUserRecordMapper;
import xyz.playedu.course.mapper.ExamUserAnswerMapper;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 前端考试接口 - 用户答题相关
 */
@Slf4j
@RestController
@RequestMapping("/api/v1")
public class ExamController {

    @Autowired
    private ExamService examService;

    @Autowired
    private ExamQuestionMapper examQuestionMapper;

    @Autowired
    private ExamQuestionOptionMapper examQuestionOptionMapper;

    @Autowired
    private ExamUserRecordMapper examUserRecordMapper;

    @Autowired
    private ExamUserAnswerMapper examUserAnswerMapper;

    /**
     * 获取课程关联的考试列表
     */
    @GetMapping("/course/{courseId}/exams")
    public JsonResponse courseExams(@PathVariable Long courseId) {
        List<Map<String, Object>> exams = examService.getExamsByCourseId(courseId);
        JsonResponse response = JsonResponse.success("获取成功");
        response.setData(exams);
        return response;
    }

    /**
     * 获取考试详情（包括所有题目和选项）
     */
    @GetMapping("/exam/{examId}")
    public JsonResponse detail(@PathVariable Long examId) {
        Map<String, Object> exam = examService.detail(examId);
        return JsonResponse.data(exam);
    }

    /**
     * 提交答题
     */
    @PostMapping("/exam/{examId}/submit")
    public JsonResponse submit(
            @PathVariable Long examId,
            @RequestBody(required = false) Map<String, Object> payload
    ) {
        // 从数据库直接查询题目与选项，确保拿到 score 字段
        List<ExamQuestion> questions = examQuestionMapper.selectByExamId(examId, 0, 2000, "", "");

        Map<Long, List<String>> correctMap = new java.util.HashMap<>();
        int totalScore = 0;
        for (ExamQuestion q : questions) {
            Long qid = q.getId();
            List<ExamQuestionOption> opts = examQuestionOptionMapper.selectByQuestionId(qid);
            List<String> correctKeys = new java.util.ArrayList<>();
            for (ExamQuestionOption op : opts) {
                if (op.getIsCorrect() != null && op.getIsCorrect() == 1) {
                    correctKeys.add(op.getOptionKey());
                }
            }
            correctMap.put(qid, correctKeys);
            totalScore += (q.getScore() == null ? 1 : q.getScore());
        }

        List<Map<String, Object>> answerArr = (List<Map<String, Object>>) payload.getOrDefault("answers", new java.util.ArrayList<>());
        
        // 获取当前登录用户ID（使用前端用户上下文FCtx）
        Integer userId = 0;
        try {
            Integer id = FCtx.getId();
            if (id != null) {
                userId = id;
                log.info("✅ 成功获取用户ID: {}", userId);
            } else {
                log.warn("❌ 用户ID为null，答题记录将不会保存");
            }
        } catch (Exception e) {
            log.error("❌ 获取用户ID失败", e);
        }
        
        log.info("📝 准备提交考试: examId={}, userId={}, 答题数量={}", examId, userId, answerArr.size());
        
        Map<String, Object> result = examService.submit(examId, answerArr, userId);
        
        log.info("✅ 考试提交完成: score={}, total={}, passed={}, wrong_questions={}", 
            result.get("score"), result.get("total"), result.get("passed"), result.get("wrong_questions"));
        JsonResponse response = JsonResponse.success("提交成功");
        response.setData(result);
        return response;
    }

    /**
     * 获取考试的个人记录（用于准备页面显示历史成绩）
     */
    @GetMapping("/exam/{examId}/record")
    public JsonResponse record(@PathVariable Long examId) {
        Integer userId = 0;
        try {
            Integer id = FCtx.getId();
            if (id != null) {
                userId = id;
            }
        } catch (Exception e) {
            log.error("获取用户ID失败", e);
        }

        if (userId == null || userId == 0) {
            return JsonResponse.error("用户未登录", 401);
        }

        // 查询用户的考试记录
        LambdaQueryWrapper<ExamUserRecord> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(ExamUserRecord::getExamId, examId)
               .eq(ExamUserRecord::getUserId, userId);
        ExamUserRecord record = examUserRecordMapper.selectOne(wrapper);

        // 没有记录也返回成功，只是data为null（首次参加考试）
        JsonResponse response = JsonResponse.success(record == null ? "首次参加考试" : "获取成功");
        response.setData(record);
        return response;
    }

    /**
     * 校验考试分数配置（用于开始考试前检查）
     */
    @GetMapping("/exam/{examId}/validate")
    public JsonResponse validate(@PathVariable Long examId) {
        Map<String, Object> validation = examService.validateExamScore(examId);
        JsonResponse response = JsonResponse.success("校验完成");
        response.setData(validation);
        return response;
    }

    /**
     * 获取考试作答结果（用于结果页面显示详细信息）
     */
    @GetMapping("/exam/{examId}/result")
    public JsonResponse result(@PathVariable Long examId) {
        Integer userId = 0;
        try {
            Integer id = FCtx.getId();
            if (id != null) {
                userId = id;
            }
        } catch (Exception e) {
            log.error("获取用户ID失败", e);
        }

        if (userId == null || userId == 0) {
            return JsonResponse.error("用户未登录", 401);
        }

        // 查询用户的考试记录
        LambdaQueryWrapper<ExamUserRecord> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(ExamUserRecord::getExamId, examId)
               .eq(ExamUserRecord::getUserId, userId);
        ExamUserRecord record = examUserRecordMapper.selectOne(wrapper);

        if (record == null) {
            return JsonResponse.error("未找到考试记录", 404);
        }

        // 查询错题列表（答题记录中 is_correct = 0 的题目）
        LambdaQueryWrapper<ExamUserAnswer> answerWrapper = new LambdaQueryWrapper<>();
        answerWrapper.eq(ExamUserAnswer::getExamRecordId, record.getId())
                     .eq(ExamUserAnswer::getIsCorrect, 0);
        List<ExamUserAnswer> wrongAnswers = examUserAnswerMapper.selectList(answerWrapper);

        // 构建返回结果
        Map<String, Object> result = new HashMap<>();
        result.put("score", record.getScore());
        result.put("total", record.getTotalScore());
        result.put("passed", record.getPassStatus() == 1);
        result.put("pass_status", record.getPassStatus());
        result.put("correct_count", record.getCorrectCount());
        result.put("answer_count", record.getAnswerCount());
        result.put("start_at", record.getStartAt());
        result.put("end_at", record.getEndAt());
        result.put("duration", record.getDuration());
        
        // 错题ID列表
        List<Long> wrongQuestionIds = wrongAnswers.stream()
                .map(ExamUserAnswer::getQuestionId)
                .collect(Collectors.toList());
        result.put("wrong_questions", wrongQuestionIds);

        JsonResponse response = JsonResponse.success("获取成功");
        response.setData(result);
        return response;
    }
}
