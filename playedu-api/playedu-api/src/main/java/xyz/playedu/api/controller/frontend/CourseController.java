/*
 * Copyright (C) 2023 杭州白书科技有限公司
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package xyz.playedu.api.controller.frontend;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import xyz.playedu.common.context.FCtx;
import xyz.playedu.common.service.AppConfigService;
import xyz.playedu.common.types.JsonResponse;
import xyz.playedu.common.util.IpUtil;
import xyz.playedu.common.util.S3Util;
import xyz.playedu.course.domain.*;
import xyz.playedu.course.service.*;
import xyz.playedu.resource.domain.Resource;
import xyz.playedu.resource.service.ResourceService;

/**
 * <AUTHOR>
 *
 * @create 2023/3/13 16:25
 */
@Slf4j
@RestController
@RequestMapping("/api/v1/course")
public class CourseController {

    @Autowired private CourseService courseService;

    @Autowired private CourseChapterService chapterService;

    @Autowired private CourseHourService hourService;

    @Autowired private CourseAttachmentService attachmentService;

    @Autowired private ResourceService resourceService;

    @Autowired private UserCourseRecordService userCourseRecordService;

    @Autowired private UserCourseHourRecordService userCourseHourRecordService;

    @Autowired private CourseAttachmentDownloadLogService courseAttachmentDownloadLogService;

    @Autowired private AppConfigService appConfigService;

    @GetMapping("/{courseId}/hour/{hourId}/preview")
    @SneakyThrows
    public void preview(
            @PathVariable(name = "courseId") Integer courseId,
            @PathVariable(name = "hourId") Integer hourId,
            jakarta.servlet.http.HttpServletResponse response,
            jakarta.servlet.http.HttpServletRequest request) {

        log.info("📄 [H5 Preview] ========== 开始处理预览请求 ==========");
        log.info("📄 [H5 Preview] courseId: {}, hourId: {}", courseId, hourId);
        log.info("📄 [H5 Preview] 请求路径: {}", request.getRequestURI());
        log.info("📄 [H5 Preview] Authorization 头: {}", request.getHeader("Authorization"));
        log.info("📄 [H5 Preview] User-Agent: {}", request.getHeader("User-Agent"));

        CourseHour hour = hourService.findOrFail(hourId, courseId);
        log.info("📄 [H5 Preview] 找到课时: {}, 资源ID: {}", hour.getTitle(), hour.getRid());
        
        Resource resource = resourceService.findOrFail(hour.getRid());
        log.info("📄 [H5 Preview] 找到资源: {}, 扩展名: {}, 路径: {}", 
            resource.getName(), resource.getExtension(), resource.getPath());

        S3Util s3Util = new S3Util(appConfigService.getS3Config());
        String bucket = appConfigService.getS3Config().getBucket();
        log.info("📄 [H5 Preview] S3 Bucket: {}", bucket);
        
        // 获取 S3 对象
        com.amazonaws.services.s3.model.S3Object s3Object = 
            s3Util.getClient().getObject(bucket, resource.getPath());
        
        long contentLength = s3Object.getObjectMetadata().getContentLength();
        String contentType = getContentType(resource.getExtension());
        
        log.info("📄 [H5 Preview] S3 对象获取成功");
        log.info("📄 [H5 Preview] Content-Type: {}", contentType);
        log.info("📄 [H5 Preview] Content-Length: {} bytes", contentLength);
        
        // 设置响应头
        response.setContentType(contentType);
        response.setHeader("Content-Disposition", "inline; filename=\"" + resource.getName() + "." + resource.getExtension() + "\"");
        response.setContentLengthLong(contentLength);
        
        log.info("📄 [H5 Preview] 开始流式传输数据...");
        
        // 流式传输
        try (java.io.InputStream inputStream = s3Object.getObjectContent();
             java.io.OutputStream outputStream = response.getOutputStream()) {
            byte[] buffer = new byte[8192];
            int bytesRead;
            long totalBytesWritten = 0;
            while ((bytesRead = inputStream.read(buffer)) != -1) {
                outputStream.write(buffer, 0, bytesRead);
                totalBytesWritten += bytesRead;
            }
            outputStream.flush();
            log.info("📄 [H5 Preview] 数据传输完成，总共写入: {} bytes", totalBytesWritten);
        } catch (Exception e) {
            log.error("📄 [H5 Preview] 流式传输失败: {}", e.getMessage(), e);
            throw e;
        }
        
        log.info("📄 [H5 Preview] ========== 预览请求处理完成 ==========");
    }

    private String getContentType(String extension) {
        String ext = extension.toLowerCase();
        switch (ext) {
            case "pdf": return "application/pdf";
            case "txt": return "text/plain; charset=utf-8";
            case "doc": return "application/msword";
            case "docx": return "application/vnd.openxmlformats-officedocument.wordprocessingml.document";
            case "xls": return "application/vnd.ms-excel";
            case "xlsx": return "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet";
            case "ppt": return "application/vnd.ms-powerpoint";
            case "pptx": return "application/vnd.openxmlformats-officedocument.presentationml.presentation";
            default: return "application/octet-stream";
        }
    }

    @GetMapping("/{id}")
    @SneakyThrows
    public JsonResponse detail(@PathVariable(name = "id") Integer id) {
        Course course = courseService.findOrFail(id);

        List<CourseHour> courseHours = hourService.getHoursByCourseId(course.getId());

        List<Integer> rids = new ArrayList<>();
        rids.add(course.getThumb());

        List<CourseAttachment> attachments =
                attachmentService.getAttachmentsByCourseId(course.getId());
        if (null != attachments && !attachments.isEmpty()) {
            List<Integer> attachmentIds =
                    attachments.stream().map(CourseAttachment::getRid).toList();
            rids.addAll(attachmentIds);
            Map<Integer, Resource> resourceMap =
                    resourceService
                            .chunks(attachments.stream().map(CourseAttachment::getRid).toList())
                            .stream()
                            .collect(Collectors.toMap(Resource::getId, Function.identity()));
            attachments.forEach(
                    courseAttachment -> {
                        Resource resource = resourceMap.get(courseAttachment.getRid());
                        if (null != resource) {
                            courseAttachment.setExt(resource.getExtension());
                        }
                    });
        }

        HashMap<String, Object> data = new HashMap<>();
        data.put("course", course);
        data.put("chapters", chapterService.getChaptersByCourseId(course.getId()));
        data.put(
                "hours",
                courseHours.stream().collect(Collectors.groupingBy(CourseHour::getChapterId)));
        data.put("learn_record", userCourseRecordService.find(FCtx.getId(), course.getId()));
        data.put(
                "learn_hour_records",
                userCourseHourRecordService.getRecords(FCtx.getId(), course.getId()).stream()
                        .collect(Collectors.toMap(UserCourseHourRecord::getHourId, e -> e)));
        data.put("attachments", attachments);

        // 获取签名url
        data.put("resource_url", resourceService.chunksPreSignUrlByIds(rids));

        return JsonResponse.data(data);
    }

    @GetMapping("/{courseId}/attach/{id}/download")
    @SneakyThrows
    public JsonResponse attachmentDownload(
            @PathVariable(name = "courseId") Integer courseId,
            @PathVariable(name = "id") Integer id) {
        CourseAttachment attachment = attachmentService.findOrFail(id, courseId);
        Resource resource = resourceService.findOrFail(attachment.getRid());

        HashMap<String, Object> data = new HashMap<>();
        // 获取资源签名url
        data.put("resource_url", resourceService.downloadResById(attachment.getRid()));

        courseAttachmentDownloadLogService.save(
                new CourseAttachmentDownloadLog() {
                    {
                        setUserId(FCtx.getId());
                        setCourseId(attachment.getCourseId());
                        setCourserAttachmentId(attachment.getId());
                        setRid(resource.getId());
                        setTitle(attachment.getTitle());
                        setIp(IpUtil.getIpAddress());
                        setCreatedAt(new Date());
                    }
                });

        return JsonResponse.data(data);
    }
}
