/*
 * Copyright (C) 2023 杭州白书科技有限公司
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package xyz.playedu.api.service.impl;

import java.time.LocalDateTime;
import java.util.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;
import xyz.playedu.api.request.backend.ExamQuestionCreateRequest;
import xyz.playedu.api.request.backend.ExamQuestionUpdateRequest;
import xyz.playedu.api.request.backend.ExamQuestionImportRequest;
import xyz.playedu.api.service.ExamQuestionService;
import xyz.playedu.common.exception.ServiceException;
// import xyz.playedu.course.domain.ExamImportLog;
import xyz.playedu.course.domain.ExamQuestion;
import xyz.playedu.course.domain.ExamQuestionOption;
// import xyz.playedu.course.mapper.ExamImportLogMapper;
import xyz.playedu.course.mapper.ExamQuestionMapper;
import xyz.playedu.course.mapper.ExamQuestionOptionMapper;

@Service
@Slf4j
public class ExamQuestionServiceImpl implements ExamQuestionService {

    @Autowired
    private ExamQuestionMapper questionMapper;

    @Autowired
    private ExamQuestionOptionMapper optionMapper;

    // @Autowired
    // private ExamImportLogMapper importLogMapper;

    @Override
    public List<Map<String, Object>> paginate(Long examId, int pageNo, int pageSize, String title, String type, int[] total) {
        if (title == null) {
            title = "";
        }
        long count = questionMapper.countByExamId(examId, "%" + title + "%", type);
        if (total != null && total.length > 0) {
            total[0] = (int) count;
        }
        int offset = (pageNo - 1) * pageSize;
        List<ExamQuestion> list = questionMapper.selectByExamId(examId, offset, pageSize, "%" + title + "%", type);

        List<Map<String, Object>> result = new ArrayList<>();
        for (ExamQuestion question : list) {
            result.add(convert2Map(question));
        }
        return result;
    }

    @Override
    public Map<String, Object> detail(Long questionId) {
        ExamQuestion question = questionMapper.selectById(questionId);
        if (question == null) {
            throw new ServiceException("试题不存在");
        }
        Map<String, Object> map = convert2Map(question);
        
        // 获取选项
        List<ExamQuestionOption> options = optionMapper.selectByQuestionId(questionId);
        List<Map<String, Object>> optionsList = new ArrayList<>();
        for (ExamQuestionOption option : options) {
            Map<String, Object> optionMap = new HashMap<>();
            optionMap.put("id", option.getId());
            optionMap.put("content", option.getContent());
            optionMap.put("is_correct", option.getIsCorrect());
            optionMap.put("option_key", option.getOptionKey());
            optionsList.add(optionMap);
        }
        map.put("options", optionsList);
        return map;
    }

    @Override
    public void create(Long examId, ExamQuestionCreateRequest req) {
        // 校验选项数量和正确答案
        validateQuestionOptions(req.getType(), req.getOptions());

        ExamQuestion question = ExamQuestion.builder()
                .examId(examId)
                .title(req.getTitle())
                .type(req.getType())
                .difficulty(req.getDifficulty())
                .score(req.getScore())
                .analysis(req.getAnalysis())
                //.answer(req.getAnswer())
                .status(1)
                .sort(0)
                .createdAt(LocalDateTime.now())
                .updatedAt(LocalDateTime.now())
                .build();
        questionMapper.insert(question);

        // 保存选项
        if (req.getOptions() != null && !req.getOptions().isEmpty()) {
            for (int i = 0; i < req.getOptions().size(); i++) {
                ExamQuestionCreateRequest.OptionData optionData = req.getOptions().get(i);
                String optionKey = String.valueOf((char) ('A' + i));
                ExamQuestionOption option = ExamQuestionOption.builder()
                        .questionId(question.getId())
                        .optionKey(optionKey)
                        .content(optionData.getContent())
                        .isCorrect(optionData.getIs_correct())
                        .createdAt(LocalDateTime.now())
                        .build();
                optionMapper.insert(option);
            }
        }
    }

    @Override
    public void update(Long questionId, ExamQuestionUpdateRequest req) {
        ExamQuestion question = questionMapper.selectById(questionId);
        if (question == null) {
            throw new ServiceException("试题不存在");
        }

        // 校验选项数量和正确答案（使用数据库中已存的题型）
        validateQuestionOptions(question.getType(), req.getOptions());
        
        question.setTitle(req.getTitle());
        question.setDifficulty(req.getDifficulty());
        question.setScore(req.getScore());
        question.setAnalysis(req.getAnalysis());
        //question.setAnswer(req.getAnswer());
        question.setUpdatedAt(LocalDateTime.now());
        questionMapper.updateById(question);

        // 删除旧选项
        optionMapper.deleteByQuestionId(questionId);

        // 保存新选项
        if (req.getOptions() != null && !req.getOptions().isEmpty()) {
            for (int i = 0; i < req.getOptions().size(); i++) {
                ExamQuestionUpdateRequest.OptionData optionData = req.getOptions().get(i);
                String optionKey = String.valueOf((char) ('A' + i));
                ExamQuestionOption option = ExamQuestionOption.builder()
                        .questionId(questionId)
                        .optionKey(optionKey)
                        .content(optionData.getContent())
                        .isCorrect(optionData.getIs_correct())
                        .createdAt(LocalDateTime.now())
                        .build();
                optionMapper.insert(option);
            }
        }
    }

    @Override
    public void delete(Long questionId) {
        ExamQuestion question = questionMapper.selectById(questionId);
        if (question == null) {
            throw new ServiceException("试题不存在");
        }
        questionMapper.deleteById(questionId);
        optionMapper.deleteByQuestionId(questionId);
    }

    private Map<String, Object> convert2Map(ExamQuestion question) {
        Map<String, Object> map = new HashMap<>();
        map.put("id", question.getId());
        map.put("exam_id", question.getExamId());
        map.put("title", question.getTitle());
        map.put("type", question.getType());
        map.put("difficulty", question.getDifficulty());
        map.put("score", question.getScore());
        map.put("analysis", question.getAnalysis());
        //map.put("answer", question.getAnswer());
        map.put("created_at", question.getCreatedAt());
        map.put("updated_at", question.getUpdatedAt());
        return map;
    }

    /**
     * 校验试题选项的正确答案数量
     * @param questionType 题型
     * @param options 选项列表
     */
    private void validateQuestionOptions(String questionType, List<?> options) {
        // 计算正确答案数量
        int correctCount = 0;
        if (options != null) {
            for (Object optObj : options) {
                Integer isCorrect = null;
                if (optObj instanceof ExamQuestionCreateRequest.OptionData) {
                    isCorrect = ((ExamQuestionCreateRequest.OptionData) optObj).getIs_correct();
                } else if (optObj instanceof ExamQuestionUpdateRequest.OptionData) {
                    isCorrect = ((ExamQuestionUpdateRequest.OptionData) optObj).getIs_correct();
                }
                
                if (isCorrect != null && isCorrect == 1) {
                    correctCount++;
                }
            }
        }

        // 单选题必须有且仅有一个正确答案
        if ("single_choice".equals(questionType)) {
            if (correctCount != 1) {
                throw new ServiceException("单选题必须有且仅有一个正确答案");
            }
        }

        // 多选题至少有一个正确答案
        if ("multiple_choice".equals(questionType)) {
            if (correctCount == 0) {
                throw new ServiceException("多选题至少要有一个正确答案");
            }
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Map<String, Object> importQuestions(Long examId, MultipartFile file) throws Exception {
        // 验证文件
        if (file == null || file.isEmpty()) {
            throw new ServiceException("请选择要上传的文件");
        }

        String originalFilename = file.getOriginalFilename();
        if (originalFilename == null || (!originalFilename.endsWith(".xlsx") && !originalFilename.endsWith(".xls"))) {
            throw new ServiceException("只支持.xlsx和.xls格式的文件");
        }

        if (file.getSize() > 10 * 1024 * 1024) { // 10MB
            throw new ServiceException("文件大小不能超过10MB");
        }

        // 解析Excel文件
        List<ExamQuestionImportRequest.QuestionData> questions = parseExcelFile(file);

        // 验证数据
        List<Map<String, Object>> errors = new ArrayList<>();
        List<ExamQuestionImportRequest.QuestionData> validQuestions = new ArrayList<>();

        for (int i = 0; i < questions.size(); i++) {
            ExamQuestionImportRequest.QuestionData question = questions.get(i);
            List<String> validationErrors = validateQuestionData(question);
            if (validationErrors.isEmpty()) {
                validQuestions.add(question);
            } else {
                Map<String, Object> error = new HashMap<>();
                error.put("row", question.getRowNumber());
                error.put("field", "数据验证");
                error.put("message", String.join("; ", validationErrors));
                error.put("value", question.getTitle());
                errors.add(error);
            }
        }

        // 批量导入有效数据
        int successCount = 0;
        if (!validQuestions.isEmpty()) {
            successCount = batchInsertQuestions(examId, validQuestions);
        }

        // 构建返回结果
        Map<String, Object> result = new HashMap<>();
        result.put("import_log_id", 0); // 暂时设为0，后续实现日志功能
        result.put("total_count", questions.size());
        result.put("success_count", successCount);
        result.put("error_count", errors.size());
        result.put("errors", errors);

        return result;
    }

    private List<ExamQuestionImportRequest.QuestionData> parseExcelFile(MultipartFile file) throws Exception {
        List<ExamQuestionImportRequest.QuestionData> questions = new ArrayList<>();

        try (Workbook workbook = new XSSFWorkbook(file.getInputStream())) {
            Sheet sheet = workbook.getSheetAt(0);
            if (sheet == null) {
                throw new ServiceException("Excel文件必须包含工作表");
            }

            // 从第2行开始读取（第1行是标题）
            for (int i = 1; i <= sheet.getLastRowNum(); i++) {
                Row row = sheet.getRow(i);
                if (row == null) continue;

                // 检查是否为空行
                boolean isEmptyRow = true;
                for (int j = 0; j < 13; j++) { // 检查前13列
                    Cell cell = row.getCell(j);
                    if (cell != null && cell.getCellType() != CellType.BLANK &&
                        !(cell.getCellType() == CellType.STRING && cell.getStringCellValue().trim().isEmpty())) {
                        isEmptyRow = false;
                        break;
                    }
                }
                if (isEmptyRow) continue;

                ExamQuestionImportRequest.QuestionData question = parseRow(row, i + 1);
                if (question != null) {
                    questions.add(question);
                }
            }
        }

        return questions;
    }

    private ExamQuestionImportRequest.QuestionData parseRow(Row row, int rowNumber) {
        try {
            ExamQuestionImportRequest.QuestionData question = new ExamQuestionImportRequest.QuestionData();
            question.setRowNumber(rowNumber);

            // 序号 (A列)
            question.setTitle(getCellValue(row.getCell(1))); // 题目标题 (B列)
            question.setType(getCellValue(row.getCell(2))); // 题型 (C列)
            question.setDifficulty(getCellValue(row.getCell(3))); // 难度 (D列)

            // 分数 (E列)
            String scoreStr = getCellValue(row.getCell(4));
            if (scoreStr != null && !scoreStr.trim().isEmpty()) {
                try {
                    question.setScore(Integer.parseInt(scoreStr.trim()));
                } catch (NumberFormatException e) {
                    question.setScore(1); // 默认分数
                }
            } else {
                question.setScore(1); // 默认分数
            }

            question.setAnalysis(getCellValue(row.getCell(5))); // 解析 (F列)
            question.setCorrectAnswer(getCellValue(row.getCell(6))); // 正确答案 (G列)
            question.setOptionA(getCellValue(row.getCell(7))); // 选项A (H列)
            question.setOptionB(getCellValue(row.getCell(8))); // 选项B (I列)
            question.setOptionC(getCellValue(row.getCell(9))); // 选项C (J列)
            question.setOptionD(getCellValue(row.getCell(10))); // 选项D (K列)
            question.setOptionE(getCellValue(row.getCell(11))); // 选项E (L列)
            question.setOptionF(getCellValue(row.getCell(12))); // 选项F (M列)

            return question;
        } catch (Exception e) {
            log.error("解析第" + rowNumber + "行数据失败", e);
            return null;
        }
    }

    private String getCellValue(Cell cell) {
        if (cell == null) return null;

        switch (cell.getCellType()) {
            case STRING:
                return cell.getStringCellValue().trim();
            case NUMERIC:
                if (DateUtil.isCellDateFormatted(cell)) {
                    return cell.getDateCellValue().toString();
                } else {
                    return String.valueOf((int) cell.getNumericCellValue());
                }
            case BOOLEAN:
                return String.valueOf(cell.getBooleanCellValue());
            case FORMULA:
                try {
                    return cell.getStringCellValue();
                } catch (Exception e) {
                    return String.valueOf(cell.getNumericCellValue());
                }
            default:
                return null;
        }
    }

    private List<String> validateQuestionData(ExamQuestionImportRequest.QuestionData question) {
        List<String> errors = new ArrayList<>();

        // 必填字段验证
        if (question.getTitle() == null || question.getTitle().trim().isEmpty()) {
            errors.add("题目标题不能为空");
        }
        if (question.getType() == null || question.getType().trim().isEmpty()) {
            errors.add("题型不能为空");
        } else if (!"single_choice".equals(question.getType()) && !"multiple_choice".equals(question.getType())) {
            errors.add("题型只能是single_choice或multiple_choice");
        }
        if (question.getDifficulty() == null || question.getDifficulty().trim().isEmpty()) {
            errors.add("难度不能为空");
        } else if (!"easy".equals(question.getDifficulty()) && !"medium".equals(question.getDifficulty()) && !"hard".equals(question.getDifficulty())) {
            errors.add("难度只能是easy、medium或hard");
        }
        if (question.getCorrectAnswer() == null || question.getCorrectAnswer().trim().isEmpty()) {
            errors.add("正确答案不能为空");
        }

        // 选项验证
        if ("single_choice".equals(question.getType()) || "multiple_choice".equals(question.getType())) {
            List<String> options = Arrays.asList(
                question.getOptionA(), question.getOptionB(), question.getOptionC(), question.getOptionD(),
                question.getOptionE(), question.getOptionF()
            );

            // 检查至少有两个非空选项
            long nonEmptyOptions = options.stream().filter(opt -> opt != null && !opt.trim().isEmpty()).count();
            if (nonEmptyOptions < 2) {
                errors.add("至少需要提供2个选项");
            }

            // 验证正确答案格式
            String correctAnswer = question.getCorrectAnswer().toUpperCase();
            if ("single_choice".equals(question.getType())) {
                if (!correctAnswer.matches("^[A-F]$")) {
                    errors.add("单选题正确答案格式错误，只能是A-F中的一个字母");
                }
            } else if ("multiple_choice".equals(question.getType())) {
                if (!correctAnswer.matches("^[A-F,]+$") || correctAnswer.contains(",,")) {
                    errors.add("多选题正确答案格式错误，应为用逗号分隔的A-F字母");
                }
                // 验证答案对应的选项是否存在
                String[] answers = correctAnswer.split(",");
                for (String ans : answers) {
                    if (ans.trim().isEmpty()) continue;
                    int optionIndex = ans.charAt(0) - 'A';
                    if (optionIndex < 0 || optionIndex >= options.size() ||
                        options.get(optionIndex) == null || options.get(optionIndex).trim().isEmpty()) {
                        errors.add("正确答案" + ans + "对应的选项不存在");
                    }
                }
            }
        }

        return errors;
    }

    private int batchInsertQuestions(Long examId, List<ExamQuestionImportRequest.QuestionData> questions) {
        int successCount = 0;

        for (ExamQuestionImportRequest.QuestionData questionData : questions) {
            try {
                // 创建题目
                ExamQuestion question = new ExamQuestion();
                question.setExamId(examId);
                question.setTitle(questionData.getTitle());
                question.setType(questionData.getType());
                question.setDifficulty(questionData.getDifficulty());
                question.setScore(questionData.getScore());
                question.setAnalysis(questionData.getAnalysis());
                question.setSort(0); // 默认排序
                question.setStatus(1); // 默认启用
                question.setCreatedAt(LocalDateTime.now());
                question.setUpdatedAt(LocalDateTime.now());

                questionMapper.insert(question);

                // 创建选项（仅选择题需要）
                if ("single_choice".equals(questionData.getType()) || "multiple_choice".equals(questionData.getType())) {
                    createQuestionOptions(question.getId(), questionData);
                }

                successCount++;
            } catch (Exception e) {
                log.error("插入题目失败: " + questionData.getTitle(), e);
                // 继续处理其他题目
            }
        }

        return successCount;
    }

    private void createQuestionOptions(Long questionId, ExamQuestionImportRequest.QuestionData questionData) {
        List<String> options = Arrays.asList(
            questionData.getOptionA(), questionData.getOptionB(), questionData.getOptionC(), questionData.getOptionD(),
            questionData.getOptionE(), questionData.getOptionF()
        );

        String correctAnswer = questionData.getCorrectAnswer().toUpperCase();
        String[] correctAnswers = "multiple_choice".equals(questionData.getType()) ?
            correctAnswer.split(",") : new String[]{correctAnswer};

        Set<String> correctAnswerSet = new HashSet<>(Arrays.asList(correctAnswers));

        for (int i = 0; i < options.size(); i++) {
            String optionContent = options.get(i);
            if (optionContent == null || optionContent.trim().isEmpty()) {
                continue; // 跳过空选项
            }

            ExamQuestionOption option = new ExamQuestionOption();
            option.setQuestionId(questionId);
            option.setContent(optionContent);
            option.setOptionKey(String.valueOf((char)('A' + i)));
            option.setIsCorrect(correctAnswerSet.contains(String.valueOf((char)('A' + i))) ? 1 : 0);
            option.setCreatedAt(LocalDateTime.now());

            optionMapper.insert(option);
        }
    }

    @Override
    public Map<String, Object> getImportLogs(Long examId, int page, int size) {
        // TODO: 暂时返回空数据，后续实现完整的导入日志功能
        Map<String, Object> result = new HashMap<>();
        result.put("data", new ArrayList<>());
        result.put("total", 0);
        result.put("page", page);
        result.put("size", size);
        return result;
    }
}
