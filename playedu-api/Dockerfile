FROM registry.cn-hangzhou.aliyuncs.com/hzbs/eclipse-temurin:17

# 这个镜像是基于 Debian/Ubuntu 的，使用 apt-get
RUN apt-get update && apt-get install -y bash netcat && rm -rf /var/lib/apt/lists/*

WORKDIR /app

# 复制等待脚本
COPY wait-for-it.sh /app/wait-for-it.sh
RUN chmod +x /app/wait-for-it.sh

# 假设jar包已经在主机上编译好了
COPY playedu-api/playedu-api/target/playedu-api.jar /app/app.jar

RUN chmod +x /app/app.jar

EXPOSE 9898/tcp

# 使用脚本等待 DB_HOST:DB_PORT 可用后再启动应用
# 超时时间设置为 60 秒
ENTRYPOINT ["/app/wait-for-it.sh", "mysql:3306", "-t", "60", "--", "java", "-jar", "/app/app.jar"]
