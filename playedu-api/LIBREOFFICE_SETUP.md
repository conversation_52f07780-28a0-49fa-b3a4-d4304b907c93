# LibreOffice 安装指南

## 功能说明

系统已集成 **PPT/PPTX 自动转 PDF** 功能：
- 上传 PPT/PPTX 课件时，后端自动转换为 PDF
- 转换后的 PDF 可以精确跟踪学习进度
- 格式保真度约 90-95%

## 安装 LibreOffice

### 方式一：Docker 环境（推荐）

如果您使用Docker部署，在 `Dockerfile` 中添加：

```dockerfile
# 基于官方镜像
FROM openjdk:17-jdk-slim

# 安装 LibreOffice
RUN apt-get update && \
    apt-get install -y libreoffice && \
    apt-get install -y fonts-wqy-microhei fonts-wqy-zenhei && \
    rm -rf /var/lib/apt/lists/*

# 其他构建步骤...
```

### 方式二：Linux 服务器

#### Ubuntu / Debian
```bash
# 更新包列表
sudo apt-get update

# 安装 LibreOffice
sudo apt-get install -y libreoffice

# 安装中文字体（可选，提高中文显示效果）
sudo apt-get install -y fonts-wqy-microhei fonts-wqy-zenhei
sudo apt-get install -y fonts-noto-cjk

# 验证安装
soffice --version
```

#### CentOS / RHEL
```bash
# 安装 LibreOffice
sudo yum install -y libreoffice

# 安装中文字体
sudo yum install -y wqy-microhei-fonts wqy-zenhei-fonts

# 验证安装
soffice --version
```

### 方式三：macOS

```bash
# 使用 Homebrew 安装
brew install --cask libreoffice

# 验证安装
/Applications/LibreOffice.app/Contents/MacOS/soffice --version
```

### 方式四：Windows

1. 访问 [LibreOffice 官网](https://www.libreoffice.org/download/download/)
2. 下载并安装 Windows 版本
3. 默认安装路径：`C:\Program Files\LibreOffice\program\soffice.exe`

## 验证安装

启动应用后，查看日志：

```
✅ 成功：找到 LibreOffice: /usr/bin/soffice
✅ 开始转换PPT到PDF: xxx.pptx
✅ 转换成功: xxx.pptx -> xxx.pdf

❌ 失败：LibreOffice未安装，无法转换PPT，将直接上传原文件
```

## 功能测试

1. 登录管理后台
2. 进入 **资源管理** → **课件**
3. 点击 **上传课件**
4. 选择一个 PPT/PPTX 文件
5. 观察：
   - 上传进度条
   - 后台日志显示转换过程
   - 转换完成后，文件列表显示为 `.pdf` 格式
   - 文件大小可能会变化（通常PDF更小）

## 常见问题

### Q1: 转换失败怎么办？

**A:** 系统会自动降级，直接上传原始PPT文件。日志会显示：
```
LibreOffice未安装，无法转换PPT，将直接上传原文件
```

### Q2: 转换时间多久？

**A:** 
- 小文件（< 5MB）：5-15 秒
- 中等文件（5-20MB）：15-40 秒
- 大文件（> 20MB）：40-120 秒

### Q3: 转换后格式有差异吗？

**A:** 
- 文字、图片、图表：90-95% 保真
- 动画效果：会丢失（PDF不支持动画）
- 嵌入视频/音频：会丢失

### Q4: 如何提高转换质量？

**A:** 
1. 安装常用中文字体
2. PPT中使用系统字体（如：微软雅黑、宋体）
3. 避免使用特殊字体和复杂动画

### Q5: 转换失败的常见原因？

**A:**
- LibreOffice 未安装或未添加到 PATH
- 服务器权限不足
- 磁盘空间不足（需要临时存储）
- PPT文件损坏

### Q6: 如何调试转换问题？

**A:** 查看日志，关键信息：
```bash
# 检查 LibreOffice
log.info("找到 LibreOffice: {}", path)

# 转换开始
log.info("开始转换PPT到PDF: {}", filename)

# 转换成功
log.info("转换成功: {} -> {}", inputFile.getName(), pdfFile.getName())

# 转换失败
log.error("PPT转PDF失败", exception)
```

## 性能优化建议

### 1. 异步转换（未来优化）

当前是同步转换，用户需要等待。未来可以改为：
- 用户上传后立即返回
- 后台异步转换
- 转换完成后通知用户

### 2. 缓存转换结果

如果同一个PPT多次上传：
- 计算文件MD5
- 检查是否已有转换结果
- 复用已转换的PDF

### 3. 限制并发转换

LibreOffice转换占用CPU和内存：
- 建议同时最多3-5个转换任务
- 使用队列管理转换任务

## Docker Compose 示例

```yaml
version: '3'
services:
  playedu-api:
    build: .
    ports:
      - "9898:9898"
    environment:
      - JAVA_OPTS=-Xmx2g
    volumes:
      - ./data:/data
    # LibreOffice 已在 Dockerfile 中安装
```

## 系统要求

- **内存**: 建议至少 2GB（转换需要额外内存）
- **CPU**: 多核处理器（转换会占用CPU）
- **磁盘**: 至少 2GB 空闲空间（临时文件）
- **LibreOffice**: 版本 6.0 或更高

## 技术细节

### 转换流程

```
用户上传 PPT
    ↓
检测文件扩展名 (.ppt/.pptx)
    ↓
检查 LibreOffice 是否安装
    ↓
创建临时文件
    ↓
调用 soffice --headless --convert-to pdf
    ↓
读取转换后的 PDF
    ↓
上传到 S3/MinIO
    ↓
清理临时文件
    ↓
返回成功
```

### 支持的格式

- ✅ `.ppt` (Microsoft PowerPoint 97-2003)
- ✅ `.pptx` (Microsoft PowerPoint 2007+)
- ✅ 自动转换为 `.pdf`

## 故障排查

### 问题：找不到 soffice 命令

```bash
# 检查安装
which soffice
which libreoffice

# 手动指定路径（在代码中配置）
# Linux: /usr/bin/soffice
# macOS: /Applications/LibreOffice.app/Contents/MacOS/soffice
# Windows: C:\Program Files\LibreOffice\program\soffice.exe
```

### 问题：权限不足

```bash
# 确保Java进程有执行权限
sudo chmod +x /usr/bin/soffice

# 确保临时目录可写
sudo mkdir -p /tmp
sudo chmod 1777 /tmp
```

### 问题：中文乱码

```bash
# 安装中文字体
sudo apt-get install fonts-wqy-microhei fonts-wqy-zenhei fonts-noto-cjk

# 更新字体缓存
sudo fc-cache -fv
```

## 联系支持

如有问题，请查看：
- 应用日志：`/var/log/playedu.log`
- LibreOffice 日志：`/tmp/soffice-*.log`
- GitHub Issues: [提交问题](https://github.com/playedu/playedu/issues)

