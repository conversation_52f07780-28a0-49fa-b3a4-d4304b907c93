<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="xyz.playedu.course.mapper.ExamQuestionOptionMapper">
    <select id="selectByQuestionId" resultType="xyz.playedu.course.domain.ExamQuestionOption">
        SELECT * FROM exam_question_options WHERE question_id = #{questionId} ORDER BY option_key ASC
    </select>
    
    <delete id="deleteByQuestionId">
        DELETE FROM exam_question_options WHERE question_id = #{questionId}
    </delete>
</mapper>
