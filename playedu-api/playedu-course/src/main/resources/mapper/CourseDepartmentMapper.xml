<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="xyz.playedu.course.mapper.CourseDepartmentUserMapper">

    <resultMap id="BaseResultMap" type="xyz.playedu.course.domain.CourseDepartmentUser">
        <result property="courseId" column="course_id" jdbcType="INTEGER"/>
        <result property="rangeId" column="range_id" jdbcType="INTEGER"/>
        <result property="type" column="type" jdbcType="INTEGER"/>
    </resultMap>

    <sql id="Base_Column_List">
        course_id,range_id,type
    </sql>
</mapper>
