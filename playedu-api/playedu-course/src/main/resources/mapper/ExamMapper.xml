<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="xyz.playedu.course.mapper.ExamMapper">

    <select id="selectByPage" resultType="xyz.playedu.course.domain.Exam">
        SELECT * FROM exams
        WHERE title LIKE #{title}
        ORDER BY id DESC
        LIMIT #{offset}, #{limit}
    </select>

    <select id="countByTitle" resultType="long">
        SELECT COUNT(*) FROM exams
        WHERE title LIKE #{title}
    </select>

</mapper>
