<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="xyz.playedu.course.mapper.UserLearnDurationStatsMapper">

    <resultMap id="BaseResultMap" type="xyz.playedu.course.domain.UserLearnDurationStats">
            <id property="id" column="id" jdbcType="INTEGER"/>
            <result property="userId" column="user_id" jdbcType="INTEGER"/>
            <result property="duration" column="duration" jdbcType="BIGINT"/>
            <result property="createdDate" column="created_date" jdbcType="DATE"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,user_id,duration,
        created_date
    </sql>
    <select id="getUserDuration" resultType="java.lang.Long">
        SELECT sum(`duration`)
        FROM `user_learn_duration_stats`
        where `user_id` = #{userId};
    </select>
</mapper>
