<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="xyz.playedu.course.mapper.ExamQuestionMapper">

    <select id="selectByExamId" resultType="xyz.playedu.course.domain.ExamQuestion">
        SELECT * FROM exam_questions
        WHERE exam_id = #{examId}
        <if test="title != null and title != ''">
            AND title LIKE #{title}
        </if>
        <if test="type != null and type != ''">
            AND type = #{type}
        </if>
        ORDER BY id DESC
        LIMIT #{offset}, #{limit}
    </select>

    <select id="countByExamId" resultType="long">
        SELECT COUNT(*) FROM exam_questions
        WHERE exam_id = #{examId}
        <if test="title != null and title != ''">
            AND title LIKE #{title}
        </if>
        <if test="type != null and type != ''">
            AND type = #{type}
        </if>
    </select>

</mapper>
