package xyz.playedu.course.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.util.Date;

@Data
@TableName("exam_user_records")
public class ExamUserRecord {
    @TableId(type = IdType.AUTO)
    private Long id;

    @TableField("exam_id")
    private Long examId;

    @TableField("user_id")
    private Integer userId;

    @TableField("user_name")
    private String userName;

    @TableField("user_email")
    private String userEmail;

    private Integer status; // 0:未开始,1:进行中,2:已完成,3:已过期

    @TableField("total_score")
    private Integer totalScore;

    private Integer score;

    @TableField("pass_status")
    private Integer passStatus; // 1:通过,0:未通过,null:未考

    @TableField("start_at")
    private Date startAt;

    @TableField("end_at")
    private Date endAt;

    private Integer duration; // 花费时间(秒)

    @TableField("answer_count")
    private Integer answerCount;

    @TableField("correct_count")
    private Integer correctCount;

    @TableField("created_at")
    private Date createdAt;

    @TableField("updated_at")
    private Date updatedAt;
}

