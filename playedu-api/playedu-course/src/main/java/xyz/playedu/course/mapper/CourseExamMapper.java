package xyz.playedu.course.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Delete;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import xyz.playedu.course.domain.CourseExam;

import java.util.List;

public interface CourseExamMapper extends BaseMapper<CourseExam> {

    /**
     * 查询课程关联的所有考试ID
     */
    @Select("SELECT exam_id FROM course_exam WHERE course_id = #{courseId}")
    List<Long> selectExamIdsByCourseId(@Param("courseId") Long courseId);

    /**
     * 删除课程下的所有考试关联
     */
    @Delete("DELETE FROM course_exam WHERE course_id = #{courseId}")
    void deleteByCourseId(@Param("courseId") Long courseId);

    /**
     * 删除考试的关联
     */
    @Delete("DELETE FROM course_exam WHERE exam_id = #{examId}")
    void deleteByExamId(@Param("examId") Long examId);

    /**
     * 删除特定的课程-考试关联
     */
    @Delete("DELETE FROM course_exam WHERE course_id = #{courseId} AND exam_id = #{examId}")
    void deleteByCourseIdAndExamId(@Param("courseId") Long courseId, @Param("examId") Long examId);
}
