package xyz.playedu.course.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.util.Date;

@Data
@TableName("exam_user_answers")
public class ExamUserAnswer {
    @TableId(type = IdType.AUTO)
    private Long id;

    @TableField("exam_record_id")
    private Long examRecordId;

    @TableField("exam_id")
    private Long examId;

    @TableField("user_id")
    private Integer userId;

    @TableField("question_id")
    private Long questionId;

    @TableField("question_type")
    private String questionType;

    @TableField("user_answer")
    private String userAnswer;

    @TableField("correct_answer")
    private String correctAnswer;

    @TableField("is_correct")
    private Integer isCorrect; // 1:正确,0:错误,null:未批改

    private Integer score;

    @TableField("answer_time")
    private Integer answerTime; // 答题耗时(秒)

    @TableField("created_at")
    private Date createdAt;
}

