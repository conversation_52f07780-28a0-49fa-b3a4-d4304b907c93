<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="xyz.playedu.system.mapper.MigrationMapper">

    <resultMap id="BaseResultMap" type="xyz.playedu.system.domain.Migration">
            <id property="id" column="id" jdbcType="INTEGER"/>
            <result property="migration" column="migration" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,migration
    </sql>
</mapper>
