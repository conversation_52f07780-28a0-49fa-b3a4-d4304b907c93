# 试题导入功能 - 完整代码实现

## 后端实现

### 1. pom.xml - 添加Excel处理依赖

```xml
<!-- Excel处理 -->
<dependency>
    <groupId>org.apache.poi</groupId>
    <artifactId>poi-ooxml</artifactId>
    <version>5.0.0</version>
</dependency>

<!-- EasyExcel (可选，更轻量级) -->
<dependency>
    <groupId>com.alibaba</groupId>
    <artifactId>easyexcel</artifactId>
    <version>3.1.1</version>
</dependency>
```

---

### 2. 数据模型

#### ExamImportRequest.java
```java
package xyz.playedu.api.request;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.web.multipart.MultipartFile;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ExamImportRequest {
    private MultipartFile file;
    private Integer examId;
}
```

#### ExamImportResult.java
```java
package xyz.playedu.api.response;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import java.util.List;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ExamImportResult {
    private Integer importLogId;
    private Integer totalCount;      // 总行数
    private Integer successCount;    // 成功行数
    private Integer errorCount;      // 失败行数
    private List<ExamImportError> errors;  // 错误详情
}
```

#### ExamImportError.java
```java
package xyz.playedu.api.response;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ExamImportError {
    private Integer row;         // 行号
    private String field;        // 字段名
    private String message;      // 错误信息
    private String value;        // 值
}
```

#### QuestionData.java
```java
package xyz.playedu.api.model;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class QuestionData {
    private String title;           // 题目
    private String type;            // 题型
    private String difficulty;      // 难度
    private Integer score;          // 分数
    private String optionA;         // 选项A
    private String optionB;         // 选项B
    private String optionC;         // 选项C
    private String optionD;         // 选项D
    private String correctAnswer;   // 正确答案
    private String analysis;        // 解析
}
```

---

### 3. 验证器

#### ExamQuestionValidator.java
```java
package xyz.playedu.api.validator;

import xyz.playedu.api.model.QuestionData;
import xyz.playedu.api.response.ExamImportError;
import java.util.ArrayList;
import java.util.List;

public class ExamQuestionValidator {
    
    /**
     * 验证题目数据
     */
    public static List<ExamImportError> validate(QuestionData data, int row) {
        List<ExamImportError> errors = new ArrayList<>();
        
        // 验证题目
        if (data.getTitle() == null || data.getTitle().trim().isEmpty()) {
            errors.add(ExamImportError.builder()
                .row(row).field("题目").message("题目不能为空").build());
        } else if (data.getTitle().length() > 255) {
            errors.add(ExamImportError.builder()
                .row(row).field("题目").message("题目不能超过255字").build());
        }
        
        // 验证题型
        if (data.getType() == null || data.getType().trim().isEmpty()) {
            errors.add(ExamImportError.builder()
                .row(row).field("题型").message("题型不能为空").build());
        } else if (!isValidType(data.getType())) {
            errors.add(ExamImportError.builder()
                .row(row).field("题型").message("无效的题型: " + data.getType())
                .value(data.getType()).build());
        }
        
        // 验证难度
        if (data.getDifficulty() == null || data.getDifficulty().trim().isEmpty()) {
            errors.add(ExamImportError.builder()
                .row(row).field("难度").message("难度不能为空").build());
        } else if (!isValidDifficulty(data.getDifficulty())) {
            errors.add(ExamImportError.builder()
                .row(row).field("难度").message("无效的难度: " + data.getDifficulty())
                .value(data.getDifficulty()).build());
        }
        
        // 验证分数
        if (data.getScore() == null) {
            errors.add(ExamImportError.builder()
                .row(row).field("分数").message("分数不能为空").build());
        } else if (data.getScore() < 1 || data.getScore() > 100) {
            errors.add(ExamImportError.builder()
                .row(row).field("分数").message("分数必须是1-100之间的整数")
                .value(data.getScore().toString()).build());
        }
        
        // 验证选项和正确答案
        if (isChoiceQuestion(data.getType())) {
            // 选择题验证选项
            if (isEmpty(data.getOptionA()) || isEmpty(data.getOptionB()) ||
                isEmpty(data.getOptionC()) || isEmpty(data.getOptionD())) {
                errors.add(ExamImportError.builder()
                    .row(row).field("选项").message("选择题必须有4个选项").build());
            }
            
            // 验证正确答案
            if (isEmpty(data.getCorrectAnswer())) {
                errors.add(ExamImportError.builder()
                    .row(row).field("正确答案").message("正确答案不能为空").build());
            } else if (!isValidChoiceAnswer(data.getCorrectAnswer())) {
                errors.add(ExamImportError.builder()
                    .row(row).field("正确答案")
                    .message("正确答案必须是A/B/C/D的组合")
                    .value(data.getCorrectAnswer()).build());
            }
        } else {
            // 填空/论述题验证正确答案
            if (isEmpty(data.getCorrectAnswer())) {
                errors.add(ExamImportError.builder()
                    .row(row).field("正确答案").message("正确答案不能为空").build());
            }
        }
        
        return errors;
    }
    
    private static boolean isValidType(String type) {
        return type.equals("单选") || type.equals("多选") || 
               type.equals("填空") || type.equals("论述");
    }
    
    private static boolean isValidDifficulty(String difficulty) {
        return difficulty.equals("简单") || difficulty.equals("中等") || 
               difficulty.equals("困难");
    }
    
    private static boolean isChoiceQuestion(String type) {
        return type.equals("单选") || type.equals("多选");
    }
    
    private static boolean isValidChoiceAnswer(String answer) {
        // 检查是否只包含A/B/C/D
        return answer.matches("[A-D]+");
    }
    
    private static boolean isEmpty(String str) {
        return str == null || str.trim().isEmpty();
    }
    
    /**
     * 转换题型
     */
    public static String convertType(String type) {
        return switch(type) {
            case "单选" -> "single_choice";
            case "多选" -> "multiple_choice";
            case "填空" -> "fill_blank";
            case "论述" -> "essay";
            default -> null;
        };
    }
    
    /**
     * 转换难度
     */
    public static String convertDifficulty(String difficulty) {
        return switch(difficulty) {
            case "简单" -> "easy";
            case "中等" -> "medium";
            case "困难" -> "hard";
            default -> null;
        };
    }
}
```

---

### 4. Service 业务逻辑

#### ExamImportService.java

```java
package xyz.playedu.api.service;

import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;
import xyz.playedu.api.model.QuestionData;
import xyz.playedu.api.response.ExamImportError;
import xyz.playedu.api.response.ExamImportResult;
import xyz.playedu.api.validator.ExamQuestionValidator;
import java.io.IOException;
import java.util.*;

@Slf4j
@Service
public class ExamImportService {
    
    @Autowired
    private ExamService examService;
    
    @Autowired
    private ExamQuestionService questionService;
    
    @Autowired
    private ExamImportLogService importLogService;
    
    /**
     * 导入Excel文件
     */
    @Transactional
    public ExamImportResult importQuestions(Integer examId, MultipartFile file) throws IOException {
        // 验证文件
        validateFile(file);
        
        // 解析Excel
        List<QuestionData> questions = parseExcel(file);
        
        int successCount = 0;
        int errorCount = 0;
        List<ExamImportError> errors = new ArrayList<>();
        
        // 逐行处理
        for (int i = 0; i < questions.size(); i++) {
            int rowNum = i + 2; // Excel行号从2开始
            QuestionData data = questions.get(i);
            
            // 验证
            List<ExamImportError> rowErrors = ExamQuestionValidator.validate(data, rowNum);
            
            if (!rowErrors.isEmpty()) {
                errors.addAll(rowErrors);
                errorCount++;
            } else {
                // 创建题目
                try {
                    createQuestion(examId, data);
                    successCount++;
                } catch (Exception e) {
                    log.error("创建题目失败: " + data.getTitle(), e);
                    errors.add(ExamImportError.builder()
                        .row(rowNum).field("系统").message("创建失败: " + e.getMessage()).build());
                    errorCount++;
                }
            }
        }
        
        // 记录导入日志
        Integer logId = importLogService.createLog(
            examId, 
            file.getOriginalFilename(),
            questions.size(),
            successCount,
            errorCount,
            errors
        );
        
        return ExamImportResult.builder()
            .importLogId(logId)
            .totalCount(questions.size())
            .successCount(successCount)
            .errorCount(errorCount)
            .errors(errors)
            .build();
    }
    
    /**
     * 解析Excel
     */
    private List<QuestionData> parseExcel(MultipartFile file) throws IOException {
        List<QuestionData> questions = new ArrayList<>();
        
        Workbook workbook = new XSSFWorkbook(file.getInputStream());
        Sheet sheet = workbook.getSheetAt(0);
        
        // 从第2行开始（跳过表头）
        for (int i = 1; i <= sheet.getLastRowNum(); i++) {
            Row row = sheet.getRow(i);
            if (row == null) continue;
            
            QuestionData data = QuestionData.builder()
                .title(getCellValue(row.getCell(0)))
                .type(getCellValue(row.getCell(1)))
                .difficulty(getCellValue(row.getCell(2)))
                .score(getIntValue(row.getCell(3)))
                .optionA(getCellValue(row.getCell(4)))
                .optionB(getCellValue(row.getCell(5)))
                .optionC(getCellValue(row.getCell(6)))
                .optionD(getCellValue(row.getCell(7)))
                .correctAnswer(getCellValue(row.getCell(8)))
                .analysis(getCellValue(row.getCell(9)))
                .build();
            
            questions.add(data);
        }
        
        workbook.close();
        return questions;
    }
    
    /**
     * 创建题目和选项
     */
    private void createQuestion(Integer examId, QuestionData data) {
        String typeCode = ExamQuestionValidator.convertType(data.getType());
        String difficultyCode = ExamQuestionValidator.convertDifficulty(data.getDifficulty());
        
        // 创建题目
        Integer questionId = questionService.create(
            examId,
            data.getTitle(),
            typeCode,
            difficultyCode,
            data.getScore(),
            data.getAnalysis()
        );
        
        // 若是选择题，创建选项
        if (typeCode.equals("single_choice") || typeCode.equals("multiple_choice")) {
            List<String> options = Arrays.asList("A", "B", "C", "D");
            List<String> optionTexts = Arrays.asList(
                data.getOptionA(), data.getOptionB(),
                data.getOptionC(), data.getOptionD()
            );
            
            for (int i = 0; i < 4; i++) {
                boolean isCorrect = data.getCorrectAnswer().contains(options.get(i));
                questionService.createOption(
                    questionId,
                    optionTexts.get(i),
                    options.get(i),
                    isCorrect ? 1 : 0
                );
            }
        }
    }
    
    private void validateFile(MultipartFile file) throws IOException {
        if (file.isEmpty()) {
            throw new RuntimeException("文件不能为空");
        }
        
        String filename = file.getOriginalFilename();
        if (!filename.endsWith(".xlsx")) {
            throw new RuntimeException("只支持.xlsx格式的文件");
        }
    }
    
    private String getCellValue(Cell cell) {
        if (cell == null) return "";
        return switch(cell.getCellType()) {
            case STRING -> cell.getStringCellValue();
            case NUMERIC -> String.valueOf((int)cell.getNumericCellValue());
            default -> "";
        };
    }
    
    private Integer getIntValue(Cell cell) {
        if (cell == null) return null;
        if (cell.getCellType() == CellType.NUMERIC) {
            return (int)cell.getNumericCellValue();
        }
        try {
            return Integer.parseInt(cell.getStringCellValue());
        } catch (Exception e) {
            return null;
        }
    }
}
```

---

### 5. Controller

#### ExamImportController.java

```java
package xyz.playedu.api.controller.backend;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import xyz.playedu.api.response.JsonResponse;
import xyz.playedu.api.service.ExamImportService;
import xyz.playedu.common.annotation.Log;
import xyz.playedu.common.constant.BusinessTypeConstant;

import java.io.IOException;

@RestController
@RequestMapping("/backend/v1/exams")
public class ExamImportController {
    
    @Autowired
    private ExamImportService examImportService;
    
    /**
     * 导入试题
     */
    @PostMapping("/{id}/import")
    @Log(title = "导入试题", businessType = BusinessTypeConstant.INSERT)
    public JsonResponse importQuestions(
            @PathVariable Integer id,
            @RequestParam("file") MultipartFile file) {
        
        try {
            var result = examImportService.importQuestions(id, file);
            return JsonResponse.data(result);
        } catch (IOException e) {
            return JsonResponse.error("导入失败: " + e.getMessage());
        }
    }
}
```

---

## 前端实现

### 1. ImportModal.tsx

```typescript
import React, { useState } from "react";
import {
  Modal,
  Upload,
  Button,
  Progress,
  Table,
  message,
  Spin,
} from "antd";
import { InboxOutlined, DownloadOutlined } from "@ant-design/icons";
import styles from "./import-modal.module.less";
import { exam } from "../../api";
import type { UploadFile } from "antd/es/upload/interface";

interface ImportModalProps {
  visible: boolean;
  examId: number;
  onClose: () => void;
  onSuccess?: () => void;
}

interface ImportError {
  row: number;
  field: string;
  message: string;
  value?: string;
}

interface ImportResult {
  import_log_id: number;
  total_count: number;
  success_count: number;
  error_count: number;
  errors: ImportError[];
}

export const ImportModal: React.FC<ImportModalProps> = ({
  visible,
  examId,
  onClose,
  onSuccess,
}) => {
  const [loading, setLoading] = useState(false);
  const [fileList, setFileList] = useState<UploadFile[]>([]);
  const [result, setResult] = useState<ImportResult | null>(null);
  const [showResult, setShowResult] = useState(false);

  const handleUpload = async () => {
    if (fileList.length === 0) {
      message.warning("请先选择文件");
      return;
    }

    setLoading(true);
    try {
      const formData = new FormData();
      formData.append("file", fileList[0] as any);

      const response = await exam.import(examId, formData);
      setResult(response.data);
      setShowResult(true);

      if (response.data.error_count === 0) {
        message.success("导入成功");
        onSuccess?.();
        setTimeout(() => {
          onClose();
        }, 1000);
      } else if (response.data.success_count > 0) {
        message.warning(
          `导入完成，成功${response.data.success_count}条，失败${response.data.error_count}条`
        );
      } else {
        message.error("全部导入失败，请检查文件内容");
      }
    } catch (error: any) {
      message.error(error.message || "导入失败");
    } finally {
      setLoading(false);
    }
  };

  const handleDownloadTemplate = () => {
    // 这里可以实现模板下载功能
    message.info("模板下载功能开发中");
  };

  const errorColumns = [
    {
      title: "行号",
      dataIndex: "row",
      width: 80,
    },
    {
      title: "字段",
      dataIndex: "field",
      width: 100,
    },
    {
      title: "错误信息",
      dataIndex: "message",
    },
  ];

  return (
    <Modal
      title="导入试题"
      open={visible}
      onCancel={() => {
        onClose();
        setShowResult(false);
        setFileList([]);
        setResult(null);
      }}
      footer={null}
      width={700}
    >
      <Spin spinning={loading}>
        {!showResult ? (
          <div className={styles["import-container"]}>
            <div className={styles["upload-section"]}>
              <h3>第一步：下载导入模板</h3>
              <Button
                icon={<DownloadOutlined />}
                onClick={handleDownloadTemplate}
                style={{ marginBottom: "20px" }}
              >
                下载Excel模板
              </Button>

              <h3>第二步：填写题目数据</h3>
              <p className={styles["help-text"]}>
                按照模板格式填写题目信息，支持单选、多选、填空、论述四种题型
              </p>

              <h3>第三步：选择并上传文件</h3>
              <Upload.Dragger
                name="file"
                multiple={false}
                beforeUpload={() => false}
                onChange={(info) => {
                  setFileList(info.fileList.slice(-1));
                }}
                accept=".xlsx,.xls"
              >
                <p className={styles["ant-upload-drag-icon"]}>
                  <InboxOutlined />
                </p>
                <p className={styles["ant-upload-text"]}>
                  点击或拖拽Excel文件到此处上传
                </p>
                <p className={styles["ant-upload-hint"]}>
                  仅支持.xlsx格式，单个文件不超过10MB
                </p>
              </Upload.Dragger>

              {fileList.length > 0 && (
                <div className={styles["file-info"]}>
                  已选择文件：{fileList[0].name}
                </div>
              )}

              <div className={styles["actions"]}>
                <Button type="primary" onClick={handleUpload} loading={loading}>
                  开始导入
                </Button>
                <Button onClick={onClose}>取消</Button>
              </div>
            </div>
          </div>
        ) : (
          <div className={styles["result-section"]}>
            <div className={styles["statistics"]}>
              <div className={styles["stat-item"]}>
                <div className={styles["stat-label"]}>总条数</div>
                <div className={styles["stat-value"]}>
                  {result?.total_count}
                </div>
              </div>
              <div
                className={[styles["stat-item"], styles["success"]].join(" ")}
              >
                <div className={styles["stat-label"]}>成功</div>
                <div className={styles["stat-value"]}>
                  {result?.success_count}
                </div>
              </div>
              <div className={[styles["stat-item"], styles["error"]].join(" ")}>
                <div className={styles["stat-label"]}>失败</div>
                <div className={styles["stat-value"]}>
                  {result?.error_count}
                </div>
              </div>
            </div>

            {result?.error_count > 0 && (
              <div className={styles["error-section"]}>
                <h4>导入错误详情</h4>
                <Table
                  columns={errorColumns}
                  dataSource={result?.errors}
                  size="small"
                  pagination={false}
                  rowKey={(_, index) => index}
                />
              </div>
            )}

            <div className={styles["actions"]}>
              <Button
                type="primary"
                onClick={() => {
                  onClose();
                  setShowResult(false);
                  setFileList([]);
                  setResult(null);
                  onSuccess?.();
                }}
              >
                完成
              </Button>
            </div>
          </div>
        )}
      </Spin>
    </Modal>
  );
};
```

---

### 2. import-modal.module.less

```less
.import-container {
  .upload-section {
    h3 {
      font-size: 14px;
      font-weight: 600;
      margin-top: 20px;
      margin-bottom: 10px;

      &:first-child {
        margin-top: 0;
      }
    }

    .help-text {
      color: #666;
      font-size: 12px;
      margin-bottom: 10px;
    }

    .file-info {
      margin-top: 10px;
      padding: 8px 12px;
      background: #f5f5f5;
      border-radius: 4px;
      font-size: 12px;
      color: #333;
    }

    .actions {
      display: flex;
      gap: 10px;
      margin-top: 20px;
      justify-content: flex-end;
    }
  }
}

.result-section {
  .statistics {
    display: flex;
    gap: 20px;
    margin-bottom: 20px;

    .stat-item {
      flex: 1;
      padding: 15px;
      background: #fafafa;
      border-radius: 4px;
      text-align: center;

      .stat-label {
        font-size: 12px;
        color: #666;
        margin-bottom: 5px;
      }

      .stat-value {
        font-size: 24px;
        font-weight: bold;
        color: #333;
      }

      &.success {
        background: #f6ffed;
        .stat-value {
          color: #52c41a;
        }
      }

      &.error {
        background: #fff1f0;
        .stat-value {
          color: #ff4d4f;
        }
      }
    }
  }

  .error-section {
    margin-bottom: 20px;

    h4 {
      margin-bottom: 10px;
      font-size: 14px;
    }
  }

  .actions {
    display: flex;
    justify-content: flex-end;
    gap: 10px;
  }
}
```

---

### 3. API 调用 (exam.ts)

```typescript
// 添加到 playedu-admin/src/api/exam.ts

export const exam = {
  // ... 其他方法 ...
  
  /**
   * 导入试题
   */
  import: (examId: number, formData: FormData) => {
    return client.post(
      `/backend/v1/exams/${examId}/import`,
      formData,
      {
        headers: {
          "Content-Type": "multipart/form-data",
        },
      }
    );
  },
  
  /**
   * 获取导入日志
   */
  importLogs: (examId: number) => {
    return client.get(`/backend/v1/exams/${examId}/import-logs`);
  },
};
```

---

### 4. 在考试页面集成导入

在 `playedu-admin/src/pages/exam/index.tsx` 中：

```typescript
// 在顶部添加导入按钮
<Button
  type="primary"
  icon={<UploadOutlined />}
  onClick={() => setImportVisible(true)}
>
  导入试题
</Button>

// 添加ImportModal组件
<ImportModal
  visible={importVisible}
  examId={examId}
  onClose={() => setImportVisible(false)}
  onSuccess={() => {
    setRefresh(!refresh);  // 刷新列表
  }}
/>
```

---

## 总结

这个完整的导入实现包括：

✅ **后端**:
- Excel文件解析
- 数据验证
- 题目和选项创建
- 导入日志记录
- 错误信息收集

✅ **前端**:
- 拖拽上传界面
- 进度显示
- 导入结果展示
- 错误详情表格

✅ **功能特点**:
- 支持4种题型
- 完整的数据验证
- 详细的错误提示
- 原子性操作（成功或全部失败回滚）

