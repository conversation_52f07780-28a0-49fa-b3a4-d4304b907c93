# 试题导入功能 - 快速实施指南

## 📋 总览

已为你提供了完整的试题导入功能实现：

✅ **实施计划** - `EXAM_IMPORT_IMPLEMENTATION.md`
✅ **完整代码** - `EXAM_IMPORT_CODE.md`
✅ **此文件** - 快速指南

---

## 🚀 后端实施步骤

### 1. 添加依赖 (pom.xml)
```xml
<dependency>
    <groupId>org.apache.poi</groupId>
    <artifactId>poi-ooxml</artifactId>
    <version>5.0.0</version>
</dependency>
```

### 2. 创建文件 (复制代码即可)

在后端项目中创建以下文件：

**Model 文件夹** (`playedu-api/playedu-api/src/main/java/xyz/playedu/api/`)
- `request/ExamImportRequest.java`
- `response/ExamImportResult.java`
- `response/ExamImportError.java`
- `model/QuestionData.java`

**Service 文件夹**
- `validator/ExamQuestionValidator.java` - 验证器
- `ExamImportService.java` - 业务逻辑
- `ExamImportLogService.java` - 导入日志（参考 CourseService）

**Controller 文件夹**
- `controller/backend/ExamImportController.java`

### 3. 实现关键 Service 方法

在 `ExamQuestionService` 中实现：
```java
public Integer create(Integer examId, String title, String type, 
                     String difficulty, Integer score, String analysis);

public void createOption(Integer questionId, String content, 
                        String optionKey, Integer isCorrect);
```

### 4. 实现关键 Mapper 方法

在 `ExamQuestionMapper` 中实现：
```java
int insert(ExamQuestion record);
int insertOption(ExamQuestionOption record);
```

---

## 🎨 前端实施步骤

### 1. 创建导入组件

创建文件：
- `playedu-admin/src/pages/exam/compenents/import-modal.tsx`
- `playedu-admin/src/pages/exam/compenents/import-modal.module.less`

### 2. 添加 API 调用

在 `playedu-admin/src/api/exam.ts` 中添加：
```typescript
import: (examId: number, formData: FormData) => {
  return client.post(
    `/backend/v1/exams/${examId}/import`,
    formData,
    { headers: { "Content-Type": "multipart/form-data" } }
  );
}
```

### 3. 集成到考试页面

在 `playedu-admin/src/pages/exam/index.tsx` 中：
```typescript
// 添加导入按钮和弹窗
<Button onClick={() => setImportVisible(true)}>导入试题</Button>
<ImportModal visible={importVisible} examId={examId} onSuccess={() => refresh()} />
```

---

## 📊 数据流

```
用户上传 Excel
    ↓
前端上传到后端 POST /backend/v1/exams/{id}/import
    ↓
后端解析 Excel 文件
    ↓
逐行数据验证
    ├─ 题目不为空
    ├─ 题型有效（单选/多选/填空/论述）
    ├─ 难度有效（简单/中等/困难）
    ├─ 分数 1-100
    ├─ 选项完整（选择题需要4个）
    └─ 正确答案格式正确
    ↓
创建数据库记录
    ├─ INSERT exam_questions
    ├─ INSERT exam_question_options (如果是选择题)
    └─ INSERT exam_import_logs
    ↓
返回结果给前端
    ├─ 成功数
    ├─ 失败数
    └─ 错误详情
    ↓
前端显示结果
```

---

## ✅ 验证规则

| 字段 | 验证规则 | 示例 |
|------|---------|------|
| 题目 | 1-255字符, 必填 | "1+1等于多少?" |
| 题型 | 单选/多选/填空/论述 | "单选" |
| 难度 | 简单/中等/困难 | "中等" |
| 分数 | 1-100整数 | "10" |
| 选项 | 选择题4个，填空/论述留空 | "选项内容" |
| 正确答案 | 选择题填字母，填空题填内容 | "B" 或 "ABC" |

---

## 📝 Excel 模板格式

| 题目 | 题型 | 难度 | 分数 | 选项A | 选项B | 选项C | 选项D | 正确答案 | 解析 |
|------|------|------|------|-------|-------|-------|-------|---------|------|
| 1+1等于多少? | 单选 | 简单 | 1 | 1 | 2 | 3 | 4 | B | 基础运算 |

---

## 🔄 API 接口

### 导入接口
```
POST /backend/v1/exams/{id}/import

请求: 
  Content-Type: multipart/form-data
  file: Excel文件

响应:
{
  "code": 0,
  "data": {
    "import_log_id": 1,
    "total_count": 50,
    "success_count": 48,
    "error_count": 2,
    "errors": [
      { "row": 10, "field": "题型", "message": "无效的题型" }
    ]
  }
}
```

---

## 🧪 测试用例

### 成功案例
```
✅ 导入标准 Excel
✅ 各种题型都导入成功
✅ 成功数 = 总数，失败数 = 0
```

### 失败案例
```
❌ 题型为"判断题"（无效）
❌ 分数为 150（超出范围）
❌ 选择题缺少选项
❌ 正确答案为"E"（无效）
❌ 文件格式为 csv（必须 xlsx）
```

### 混合案例
```
⚠️ 50行中有40行成功，10行失败
   返回: success_count=40, error_count=10
        errors=[详细的10条失败信息]
```

---

## 📚 文件清单

### 后端需要创建的文件

```
playedu-api/src/main/java/xyz/playedu/api/
├── request/
│   └── ExamImportRequest.java
├── response/
│   ├── ExamImportResult.java
│   └── ExamImportError.java
├── model/
│   └── QuestionData.java
├── validator/
│   └── ExamQuestionValidator.java
├── service/
│   ├── ExamImportService.java
│   └── ExamImportLogService.java
└── controller/backend/
    └── ExamImportController.java
```

### 前端需要创建的文件

```
playedu-admin/src/
├── pages/exam/
│   └── compenents/
│       ├── import-modal.tsx
│       └── import-modal.module.less
└── api/
    └── exam.ts (添加import方法)
```

### 前端需要修改的文件

```
playedu-admin/src/pages/exam/
└── index.tsx (集成导入功能)
```

---

## ⏱️ 预计工时

| 任务 | 工时 |
|------|------|
| 后端Model和验证器 | 1小时 |
| 后端Service和Controller | 2小时 |
| 前端组件开发 | 2小时 |
| 前端集成 | 1小时 |
| 测试调试 | 2小时 |
| **总计** | **8小时** |

---

## 🎯 完成标志

✅ 后端能接收Excel文件
✅ 能解析Excel内容
✅ 能验证数据
✅ 能创建题目和选项
✅ 能记录导入日志
✅ 能返回导入结果（成功/失败/错误）
✅ 前端能上传文件
✅ 前端能显示导入结果
✅ 前端能显示错误详情

---

## 💡 关键注意事项

1. **事务处理**
   - 建议整个导入过程使用 `@Transactional` 
   - 避免部分导入成功部分失败的情况

2. **错误收集**
   - 即使遇到错误也要继续处理其他行
   - 返回所有错误让用户了解问题

3. **文件验证**
   - 检查文件是否为 xlsx 格式
   - 检查文件大小（建议 <10MB）

4. **性能优化**
   - 大批量导入时使用批量插入
   - 考虑使用缓存减少数据库查询

5. **权限检查**
   - 检查用户是否有权导入
   - 检查用户是否有权访问该考试

---

## 📞 常见问题

**Q: 导入失败怎么办？**
A: 查看返回的错误详情，根据行号和字段找到问题，修改 Excel 后重新导入。

**Q: 导入时间很长？**
A: 可能是数据太多，建议一次导入不超过 1000 条。

**Q: 导入后看不到题目？**
A: 检查是否有权限，或者刷新页面重新加载。

**Q: 能否支持 CSV？**
A: 目前只支持 xlsx，可以在 Excel 中另存为 xlsx 格式。

---

## 🚀 快速开始

1. 复制 `EXAM_IMPORT_CODE.md` 中的代码到对应文件
2. 实现 Service 中调用的数据库方法
3. 在后端启动类所在 package 添加 @EnableScheduling（如果需要）
4. 前端集成导入按钮
5. 测试上传 Excel 文件

---

**版本**: 1.0
**更新日期**: 2025-10-30

