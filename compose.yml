x-logging: &default-logging
  driver: "json-file"
  options:
    max-size: "10m"
    max-file: "10"

networks:
  playedu:
    driver: bridge

volumes:
  mysql-data:

services:
  playedu:
    image: registry.cn-hangzhou.aliyuncs.com/playedu/light:2.0
    restart: always
    environment:
      - DB_HOST=mysql
      - DB_PORT=3306
      - DB_NAME=playedu
      - DB_USER=root
      - DB_PASS=playeduxyz
      - SA_TOKEN_IS_CONCURRENT=false
      - SA_TOKEN_JWT_SECRET_KEY=${PLAYEDU_JWT_KEY:-playeduxyz}
    ports:
      - "${PLAYEDU_API_PORT:-9700}:9898"
      - "${PLAYEDU_PC_PORT:-9800}:9800"
      - "${PLAYEDU_H5_PORT:-9801}:9801"
      - "${PLAYEDU_ADMIN_PORT:-9900}:9900"
    networks:
      - playedu
    depends_on:
      - mysql
    logging: *default-logging

  mysql:
    build: ./docker/mysql
    restart: always
    environment:
      - MYSQL_DATABASE=playedu
      - MYSQL_ROOT_PASSWORD=playeduxyz
      - TZ=UTC
    volumes:
      - mysql-data:/var/lib/mysql
    ports:
      - "${MYSQL_PORT:-23307}:3306"
    networks:
      - playedu
    logging: *default-logging