# 学习统计分析 - 快速开始

## 📋 文件清单

### 前端文件（已修改）
- ✅ `playedu-admin/src/pages/learning-statistics/index.tsx` - 主页面
- ✅ `playedu-admin/src/pages/learning-statistics/index.module.less` - 样式
- ✅ `playedu-admin/src/api/learning-stats.ts` - API调用

### 后端文件（待创建）
- ⚠️ `playedu-api/playedu-api/src/main/java/xyz/playedu/api/controller/backend/LearningStatisticsController.java` - **需要创建**
- ⚠️ `playedu-api/playedu-api/src/main/java/xyz/playedu/api/service/LearningStatisticsService.java` - **需要创建**
- ⚠️ `playedu-api/playedu-api/src/main/java/xyz/playedu/api/service/impl/LearningStatisticsServiceImpl.java` - **需要创建**

## 🚀 快速部署

### 步骤1: 创建后端Service接口

创建文件：`playedu-api/playedu-api/src/main/java/xyz/playedu/api/service/LearningStatisticsService.java`

```java
package xyz.playedu.api.service;

import java.util.List;
import java.util.Map;

public interface LearningStatisticsService {
    List<Map<String, Object>> getCourseStatistics(String search);
    List<Map<String, Object>> getUserStatistics(String search);
    List<Map<String, Object>> getCourseUserDetail(Integer courseId, String search);
    List<Map<String, Object>> getUserCourseDetail(Integer userId, String search);
}
```

### 步骤2: 创建后端Service实现

创建文件：`playedu-api/playedu-api/src/main/java/xyz/playedu/api/service/impl/LearningStatisticsServiceImpl.java`

（见本文件夹的LEARNING_STATISTICS_OPTIMIZATION.md中的Service实现代码）

### 步骤3: 创建后端Controller

创建文件：`playedu-api/playedu-api/src/main/java/xyz/playedu/api/controller/backend/LearningStatisticsController.java`

（见本文件夹的LEARNING_STATISTICS_OPTIMIZATION.md中的Controller代码）

### 步骤4: 添加权限常量（如果不存在）

在 `xyz.playedu.common.constant.BPermissionConstant` 中添加（或使用现有权限）：

```java
public static final String LEARNING_STATISTICS = "learning-statistics";
```

### 步骤5: 菜单配置（可选）

在系统菜单配置中添加：

```json
{
  "id": 51,
  "name": "学习统计",
  "path": "/learning-statistics",
  "component": "learning-statistics",
  "permission": "learning-statistics"
}
```

### 步骤6: 编译运行

```bash
# 后端
cd playedu-api
mvn clean package

# 前端
cd playedu-admin
npm run build
```

## 🧪 测试

### 课程维度统计
```bash
curl "http://localhost:8000/backend/v1/learning-statistics/course"
```

### 人员维度统计
```bash
curl "http://localhost:8000/backend/v1/learning-statistics/user"
```

### 课程详情
```bash
curl "http://localhost:8000/backend/v1/learning-statistics/course/1/users"
```

### 人员详情
```bash
curl "http://localhost:8000/backend/v1/learning-statistics/user/1/courses"
```

## 📊 页面功能

### 课程维度标签
- 显示所有课程的统计信息
- 支持搜索课程名称
- 可以看到：课程名称、总学员数、已完成、完成率、平均分、及格人数、及格率
- 完成率/及格率用颜色标签表示（>=80%/>=60%绿色，否则红色）

### 人员维度标签
- 显示所有学员的统计信息
- 支持搜索学员姓名或邮箱
- 可以看到：学员姓名、邮箱、部门、学习课程数、完成率、平均分、及格课程数、及格率、最后学习时间

### 操作按钮
- 🔄 **刷新** - 重新加载数据
- 📥 **导出** - 导出当前视图的数据

### 搜索功能
- 实时搜索，无需提交表单
- 课程维度：搜索课程名称
- 人员维度：搜索学员姓名或邮箱

## 🎨 样式特点

- 使用系统统一的 `playedu-main-body` 样式
- 简洁的表格设计，无复杂卡片
- 彩色标签显示百分比（绿色优秀，红色不足）
- 移动友好的响应式布局
- 横向滚动支持大表格

## 🔗 API 端点

| 方法 | 端点 | 描述 |
|-----|------|------|
| GET | `/backend/v1/learning-statistics/course` | 课程维度统计 |
| GET | `/backend/v1/learning-statistics/user` | 人员维度统计 |
| GET | `/backend/v1/learning-statistics/course/{id}/users` | 课程学员详情 |
| GET | `/backend/v1/learning-statistics/user/{id}/courses` | 学员课程详情 |
| GET | `/backend/v1/learning-statistics/export` | 导出数据 |

## 💡 常见问题

### Q: 为什么没有部门信息？
A: 部门信息需要从 `user_department` 关联表查询，可在Service中补充：
```java
// 获取用户部门信息
List<Department> depts = userDepartmentService.getDepartmentsByUserId(user.getId());
userStats.put("department_name", depts.stream().map(Department::getName).collect(Collectors.joining(",")));
```

### Q: 为什么平均分和及格率是0？
A: 需要集成考试成绩表 `exam_user_records`。可在Service中补充：
```java
// 获取考试成绩
List<ExamUserRecord> examRecords = examUserRecordMapper.selectList(
    new LambdaQueryWrapper<ExamUserRecord>()
        .eq(ExamUserRecord::getUserId, user.getId())
);
```

### Q: 如何改变颜色阈值？
A: 在 `index.tsx` 中修改Tag的color条件：
```typescript
// 修改 >= 80 的阈值
render: (rate: number) => {
  const percentage = Math.round(rate * 100);
  return <Tag color={percentage >= 70 ? 'green' : 'red'}>{percentage}%</Tag>;
}
```

### Q: 如何支持按时间范围筛选？
A: 可在搜索框旁添加DateRange组件，然后在API调用时传递参数。

## 📝 下一步

1. [ ] 创建后端三个Java文件
2. [ ] 测试API端点
3. [ ] 在菜单中添加链接
4. [ ] 集成考试成绩数据
5. [ ] 添加部门信息
6. [ ] 性能优化和缓存

## 🎯 功能完成度

- ✅ 课程维度统计（基础完成）
- ✅ 人员维度统计（基础完成）
- ⚠️ 考试成绩集成（待实现）
- ⚠️ 部门信息展示（待实现）
- ⚠️ 时间范围筛选（待实现）
- ⚠️ Excel导出（待实现）

---

**最后更新**: 2024-11-03
**作者**: AI Assistant
