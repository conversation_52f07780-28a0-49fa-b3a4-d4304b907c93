# 三种配置方案对比

## 📊 对比表

| 方面 | 方案A: 快速方案 | 方案B: 占位符 | 方案C: 完整方案 |
|------|----------------|------------|----------------|
| **实现时间** | 中等 (30分钟) | 极快 (1分钟) | 长 (2-3小时) |
| **菜单显示** | ✅ | ✅ | ✅ |
| **页面跳转** | ✅ | ✅ | ✅ |
| **功能完整** | ✅ (高度完整) | ❌ (无功能) | ✅ (完全自定义) |
| **可直接使用** | ✅ | ❌ | ✅ |
| **复制课程代码** | ✅ | ❌ | ❌ |
| **需要后端API** | ✅ | ❌ | ✅ |
| **代码改动量** | 中等 | 极少 | 大量 |
| **错误风险** | 低 | 极低 | 中 |

---

## 🎯 选择建议

### 选方案A（快速方案）如果：
- 你有现成的后端 exam API
- 想要和"线上课"一样的功能
- 想快速上线
- 想复用已有的交互设计

### 选方案B（占位符）如果：
- 只想验证菜单配置是否正确
- 暂时不需要真实功能
- 后端API还未开发
- 想分两步走（先配置，后开发）

### 选方案C（完整方案）如果：
- 想要完全自定义的功能设计
- 没有课程页面的代码可复用
- 想要完全不同的交互体验
- 对设计有特殊要求

---

## 🚀 快速启动 - 方案B

### 第1步：确认当前状态
```bash
# 检查菜单文件
cat /Users/<USER>/pa/codes/playedu/playedu-admin/src/compenents/left-menu/index.tsx | grep "考试测评"
# 预期输出: getItem("考试测评", "/exam", null, null, null, null)

# 检查页面文件
ls -la /Users/<USER>/pa/codes/playedu/playedu-admin/src/pages/exam/
# 预期输出: index.tsx 和 index.module.less

# 检查路由文件
grep -n "ExamPage" /Users/<USER>/pa/codes/playedu/playedu-admin/src/routes/index.tsx
# 预期输出: const ExamPage 和 path: "/exam"
```

### 第2步：重启开发服务器
```bash
# 1. 停止当前服务 (Ctrl + C)

# 2. 重新启动
cd /Users/<USER>/pa/codes/playedu/playedu-admin
npm run dev

# 3. 等待看到 "VITE v4.x.x ready in Xxxx ms"
```

### 第3步：测试菜单
1. 打开浏览器 http://localhost:9900
2. 点击左侧菜单"课程中心"
3. 应该看到子菜单："线上课" 和 "考试测评"
4. 点击"考试测评"，应该能跳转到 `/exam` 页面
5. 页面显示"考试测评"标题和描述文字

---

## 💻 快速启动 - 方案A

### 第1步：复制课程页面代码
```bash
cd /Users/<USER>/pa/codes/playedu/playedu-admin

# 备份当前exam页面
mv src/pages/exam src/pages/exam.backup

# 复制course页面
cp -r src/pages/course src/pages/exam
```

### 第2步：创建exam API
在 `src/api/exam.ts` 添加：

```typescript
import client from "./internal/httpClient";

export const exam = {
  // 获取列表
  list(page: number, size: number, title: string = "") {
    return client.get("/backend/v1/exam/list", {
      params: { page, size, title },
    });
  },
  
  // 创建
  create() {
    return client.post("/backend/v1/exam/create", {});
  },
  
  // 保存
  store(data: any) {
    return client.post("/backend/v1/exam", data);
  },
  
  // 更新
  update(id: number, data: any) {
    return client.put(`/backend/v1/exam/${id}`, data);
  },
  
  // 删除
  delete(id: number) {
    return client.delete(`/backend/v1/exam/${id}`, {});
  },
};

export default exam;
```

### 第3步：更新 API 导出
在 `src/api/index.ts` 添加：
```typescript
export { exam } from "./exam";
// 或
export { default as exam } from "./exam";
```

### 第4步：修改exam页面
在 `src/pages/exam/index.tsx` 中：
- 将 `import { course }` 改为 `import { exam }`
- 将所有 `course.list()` 改为 `exam.list()`
- 修改标题为"考试测评"
- 修改表格列定义适应考试字段

### 第5步：重启服务并测试
```bash
npm run dev
```

---

## 🔧 需要修改的具体配置

### 菜单配置 (`left-menu/index.tsx`)

**当前** (✅ 已完成):
```tsx
getItem("考试测评", "/exam", null, null, null, null)
```

**带权限控制** (如需):
```tsx
getItem("考试测评", "/exam", null, null, null, "exam-menu")
```

### 路由配置 (`routes/index.tsx`)

**当前** (✅ 已完成):
```tsx
const ExamPage = lazy(() => import("../pages/exam/index"));

{
  path: "/exam",
  element: <PrivateRoute Component={<ExamPage />} />,
}
```

**多级路由** (如需):
```tsx
{
  path: "/exam",
  element: <KeepAlive />,
  children: [
    {
      path: "/exam/list",
      element: <PrivateRoute Component={<ExamPage />} />,
    },
    {
      path: "/exam/:id",
      element: <PrivateRoute Component={<ExamDetailPage />} />,
    },
  ],
}
```

---

## ⚡ 立即测试清单

- [ ] 开发服务器正在运行 (`npm run dev`)
- [ ] 菜单项已添加到 `left-menu/index.tsx`
- [ ] 页面文件存在 `pages/exam/index.tsx`
- [ ] 路由已配置 `routes/index.tsx`
- [ ] 浏览器已硬刷新 (Cmd+Shift+R 或 Ctrl+Shift+F5)
- [ ] 菜单能点击
- [ ] 页面能显示
- [ ] 浏览器控制台无红色错误

如果以上都✅，说明配置正确！

