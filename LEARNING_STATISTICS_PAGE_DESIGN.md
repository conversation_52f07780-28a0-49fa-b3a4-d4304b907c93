# PlayEdu 学习统计分析页面设计文档

## 一、项目概述

### 1.1 需求背景
在admin后台管理系统中新增一个学习统计分析页面，支持从**课程维度**和**人员维度**两个角度查看所有人员的课程学习进度情况以及考试分数情况，为管理员提供整体的学习数据分析视图。

### 1.2 核心功能
- **课程维度分析**：按课程统计所有学员的学习进度和考试成绩
- **人员维度分析**：按学员统计所有课程的学习进度和考试成绩
- **数据导出**：支持将统计数据导出为Excel
- **筛选查询**：支持按部门、课程、日期等多维度筛选
- **可视化展示**：使用图表展示学习分布、成绩分布等

---

## 二、功能设计

### 2.1 页面结构

```
┌─────────────────────────────────────────────────────────┐
│  学习统计分析                                     [导出]    │
├─────────────────────────────────────────────────────────┤
│ ┌─ Tabs ─────────────────────────────────────────────┐ │
│ │ [课程维度]  [人员维度]                               │ │
│ └────────────────────────────────────────────────────┘ │
├─────────────────────────────────────────────────────────┤
│ 📊 统计概览（卡片区域）                                   │
│ ┌──────────────┐ ┌──────────────┐ ┌──────────────┐     │
│ │ 总课程数    │ │ 总学员数    │ │ 平均完成率  │     │
│ │    10       │ │   200      │ │   75%      │     │
│ └──────────────┘ └──────────────┘ └──────────────┘     │
├─────────────────────────────────────────────────────────┤
│ 🔍 筛选条件                                               │
│ [部门] [课程类别] [日期范围] [搜索] [重置]                │
├─────────────────────────────────────────────────────────┤
│ 📈 数据表格/图表展示                                      │
│                                                         │
└─────────────────────────────────────────────────────────┘
```

### 2.2 Tab 1：课程维度分析

#### 2.2.1 功能描述
按课程展示所有学员的学习进度统计，包含学习完成情况和考试成绩。

#### 2.2.2 数据展示方式

**表格视图（默认）：**

| 课程名称 | 总学员数 | 已完成 | 未完成 | 完成率 | 平均分 | 及格人数 | 及格率 | 最后更新 | 操作 |
|---------|--------|--------|--------|--------|--------|---------|--------|---------|------|
| React高级特性 | 150 | 120 | 30 | 80% | 82.5 | 108 | 72% | 2024-11-03 | 详情 |
| Vue3.0入门 | 200 | 180 | 20 | 90% | 88.3 | 165 | 82.5% | 2024-11-02 | 详情 |

**图表视图：**
- 左图：各课程完成率柱状图
- 右图：各课程平均分折线图
- 下方：各课程及格率饼图

#### 2.2.3 详情页面（弹窗）
点击"详情"按钮后展示该课程的详细学员信息：

| 学员姓名 | 学员邮箱 | 所属部门 | 完成状态 | 完成时间 | 学习时长 | 最高分 | 及格状态 | 最后学习 |
|---------|---------|---------|---------|---------|---------|--------|---------|---------|
| 张三 | <EMAIL> | 销售部 | 已完成 | 2024-10-15 | 5小时 | 92 | 及格 | 2024-10-15 |
| 李四 | <EMAIL> | 技术部 | 进行中 | - | 2小时 | 78 | 及格 | 2024-11-02 |

**支持操作：**
- 按学员名称、邮箱搜索
- 按完成状态、及格状态筛选
- 按完成时间排序

### 2.3 Tab 2：人员维度分析

#### 2.3.1 功能描述
按学员展示其所有课程的学习进度统计，包含学习完成情况和考试成绩。

#### 2.3.2 数据展示方式

**表格视图（默认）：**

| 学员姓名 | 学员邮箱 | 所属部门 | 已学课程 | 完成课程 | 完成率 | 平均分 | 及格课程 | 及格率 | 最后学习 | 操作 |
|---------|---------|---------|---------|---------|--------|--------|---------|--------|---------|------|
| 张三 | <EMAIL> | 销售部 | 8 | 6 | 75% | 82.5 | 5 | 62.5% | 2024-11-02 | 详情 |
| 李四 | <EMAIL> | 技术部 | 10 | 9 | 90% | 88.3 | 9 | 90% | 2024-11-03 | 详情 |

**图表视图：**
- 左图：各学员完成课程数柱状图
- 右图：各学员平均分分布
- 下方：学员完成率等级分布（优秀/良好/中等/较差）

#### 2.3.3 详情页面（弹窗）
点击"详情"按钮后展示该学员的详细课程学习信息：

| 课程名称 | 课程类别 | 完成状态 | 完成时间 | 学习时长 | 考试分数 | 及格状态 | 最后学习 |
|---------|---------|---------|---------|---------|---------|---------|---------|
| React高级特性 | 技能培训 | 已完成 | 2024-10-15 | 5小时 | 92 | 及格 | 2024-10-15 |
| Vue3.0入门 | 技能培训 | 进行中 | - | 2小时 | 78 | 及格 | 2024-11-02 |

**支持操作：**
- 按课程名称搜索
- 按完成状态、及格状态筛选
- 按完成时间排序

---

## 三、技术设计

### 3.1 前端技术栈

```typescript
// 主要技术
- React 18+ (状态管理、组件化)
- TypeScript (类型安全)
- Ant Design (UI组件库)
- ECharts (数据可视化)
- xlsx (Excel导出)

// 主要依赖
- antd: 表格、选择器、弹窗、卡片
- echarts: 统计图表展示
- xlsx / xlsx-style: Excel导出
- axios: HTTP请求

// 文件结构
playedu-admin/src/pages/
├── learning-statistics/
│   ├── index.tsx (主页面)
│   ├── index.module.less (样式)
│   ├── components/
│   │   ├── CourseView.tsx (课程维度)
│   │   ├── CourseDetailModal.tsx (课程详情弹窗)
│   │   ├── UserView.tsx (人员维度)
│   │   ├── UserDetailModal.tsx (人员详情弹窗)
│   │   ├── StatisticsCard.tsx (统计卡片)
│   │   ├── ChartView.tsx (图表展示)
│   │   └── FilterBar.tsx (筛选条件栏)
```

### 3.2 API 接口设计

#### 3.2.1 课程维度统计 API

```java
// 后端接口
GET /backend/v1/learning-statistics/course
Parameters:
  - page: 页码 (default: 1)
  - size: 每页数量 (default: 10)
  - dep_ids: 部门IDs (可选，逗号分隔)
  - course_ids: 课程IDs (可选，逗号分隔)
  - start_date: 开始日期 (可选)
  - end_date: 结束日期 (可选)
  - search: 搜索关键词 (可选)

Response:
{
  "code": 0,
  "message": "success",
  "data": {
    "total": 100,
    "items": [
      {
        "course_id": 1,
        "course_name": "React高级特性",
        "course_category": "技能培训",
        "total_users": 150,
        "completed_users": 120,
        "incomplete_users": 30,
        "completion_rate": 0.80,
        "average_score": 82.5,
        "passing_users": 108,
        "passing_rate": 0.72,
        "latest_update": "2024-11-03 10:30:00",
        "thumb": "https://..."
      },
      ...
    ],
    "statistics": {
      "total_courses": 10,
      "total_users": 200,
      "avg_completion_rate": 0.75,
      "avg_passing_rate": 0.70
    }
  }
}
```

#### 3.2.2 课程详情API（学员列表）

```java
GET /backend/v1/learning-statistics/course/{courseId}/users
Parameters:
  - page: 页码 (default: 1)
  - size: 每页数量 (default: 10)
  - dep_ids: 部门IDs (可选，逗号分隔)
  - search: 搜索关键词(姓名/邮箱) (可选)
  - status: 完成状态 (可选: all/completed/incomplete)
  - pass_status: 及格状态 (可选: all/passed/failed/not_taken)

Response:
{
  "code": 0,
  "message": "success",
  "data": {
    "total": 150,
    "items": [
      {
        "user_id": 1,
        "user_name": "张三",
        "user_email": "<EMAIL>",
        "department_name": "销售部",
        "is_completed": 1,
        "completed_at": "2024-10-15 14:30:00",
        "learn_duration": 18000, // 秒
        "highest_score": 92,
        "pass_status": 1, // 1:通过, 0:未通过, null:未考
        "latest_learn_at": "2024-10-15 14:30:00"
      },
      ...
    ]
  }
}
```

#### 3.2.3 人员维度统计 API

```java
GET /backend/v1/learning-statistics/user
Parameters:
  - page: 页码 (default: 1)
  - size: 每页数量 (default: 10)
  - dep_ids: 部门IDs (可选，逗号分隔)
  - start_date: 开始日期 (可选)
  - end_date: 结束日期 (可选)
  - search: 搜索关键词 (可选)

Response:
{
  "code": 0,
  "message": "success",
  "data": {
    "total": 200,
    "items": [
      {
        "user_id": 1,
        "user_name": "张三",
        "user_email": "<EMAIL>",
        "department_name": "销售部",
        "total_courses": 8,
        "completed_courses": 6,
        "completion_rate": 0.75,
        "average_score": 82.5,
        "passing_courses": 5,
        "passing_rate": 0.625,
        "latest_learn_at": "2024-11-02 10:30:00",
        "status": 1 // 1:活跃, 0:未活跃
      },
      ...
    ],
    "statistics": {
      "total_users": 200,
      "active_users": 180,
      "avg_completion_rate": 0.75,
      "avg_passing_rate": 0.70
    }
  }
}
```

#### 3.2.4 人员详情API（课程列表）

```java
GET /backend/v1/learning-statistics/user/{userId}/courses
Parameters:
  - page: 页码 (default: 1)
  - size: 每页数量 (default: 10)
  - dep_ids: 部门IDs (可选，逗号分隔)
  - search: 搜索关键词 (可选)
  - status: 完成状态 (可选: all/completed/incomplete)

Response:
{
  "code": 0,
  "message": "success",
  "data": {
    "total": 10,
    "user_name": "张三",
    "user_email": "<EMAIL>",
    "items": [
      {
        "course_id": 1,
        "course_name": "React高级特性",
        "course_category": "技能培训",
        "is_completed": 1,
        "completed_at": "2024-10-15 14:30:00",
        "learn_duration": 18000, // 秒
        "exam_score": 92,
        "pass_status": 1, // 1:通过, 0:未通过, null:未考
        "latest_learn_at": "2024-10-15 14:30:00",
        "progress": 100 // 课程进度百分比
      },
      ...
    ]
  }
}
```

#### 3.2.5 数据导出API

```java
GET /backend/v1/learning-statistics/export
Parameters:
  - type: 导出类型 (course/user) 必需
  - dep_ids: 部门IDs (可选，逗号分隔)
  - course_ids: 课程IDs (可选，逗号分隔)
  - start_date: 开始日期 (可选)
  - end_date: 结束日期 (可选)

Response:
  返回 Excel 二进制数据
```

### 3.3 数据库查询

#### 3.3.1 关键表说明

```sql
-- 课程表
courses (id, title, class_hour, created_at)

-- 学员表
users (id, name, email, status, created_at)

-- 用户课程学习记录
user_course_records (id, user_id, course_id, is_finished, finished_count, hour_count, progress, finished_at)

-- 课程小时
course_hours (id, course_id, duration, title)

-- 用户课程小时记录
user_course_hour_records (id, user_id, hour_id, course_id, is_finished, real_duration, total_duration)

-- 考试表
exams (id, title, pass_score)

-- 课程-考试关联
course_exams (id, course_id, exam_id)

-- 考试用户记录
exam_user_records (id, exam_id, user_id, score, total_score, pass_status, correct_count, answer_count, start_at, end_at)
```

#### 3.3.2 关键查询SQL

```sql
-- 课程统计查询
SELECT 
    c.id,
    c.title,
    COUNT(DISTINCT ucr.user_id) as total_users,
    SUM(IF(ucr.is_finished=1, 1, 0)) as completed_users,
    COUNT(DISTINCT ucr.user_id) - SUM(IF(ucr.is_finished=1, 1, 0)) as incomplete_users,
    IFNULL(AVG(eur.score), 0) as average_score,
    SUM(IF(eur.pass_status=1, 1, 0)) as passing_users
FROM courses c
LEFT JOIN user_course_records ucr ON c.id = ucr.course_id
LEFT JOIN exam_user_records eur ON c.id = (SELECT course_id FROM course_exams WHERE exam_id = eur.exam_id)
GROUP BY c.id
ORDER BY c.created_at DESC;

-- 人员统计查询
SELECT 
    u.id,
    u.name,
    u.email,
    COUNT(DISTINCT ucr.course_id) as total_courses,
    SUM(IF(ucr.is_finished=1, 1, 0)) as completed_courses,
    IFNULL(AVG(eur.score), 0) as average_score,
    SUM(IF(eur.pass_status=1, 1, 0)) as passing_courses
FROM users u
LEFT JOIN user_course_records ucr ON u.id = ucr.user_id
LEFT JOIN exam_user_records eur ON u.id = eur.user_id
GROUP BY u.id
ORDER BY u.created_at DESC;
```

---

## 四、前端实现方案

### 4.1 主页面结构（index.tsx）

```typescript
import React, { useState, useEffect } from 'react';
import { Tabs, Button, Space, Row, Col, Card, Spin, message } from 'antd';
import { DownloadOutlined, ReloadOutlined } from '@ant-design/icons';
import CourseView from './components/CourseView';
import UserView from './components/UserView';
import FilterBar from './components/FilterBar';
import StatisticsCard from './components/StatisticsCard';
import styles from './index.module.less';

const LearningStatisticsPage = () => {
  const [activeTab, setActiveTab] = useState('course');
  const [loading, setLoading] = useState(false);
  const [refreshKey, setRefreshKey] = useState(0);
  
  // 筛选参数
  const [filters, setFilters] = useState({
    dep_ids: [],
    course_ids: [],
    start_date: null,
    end_date: null,
    search: ''
  });

  // 统计概览数据
  const [statistics, setStatistics] = useState({
    total_courses: 0,
    total_users: 0,
    avg_completion_rate: 0,
    avg_passing_rate: 0
  });

  // 导出数据
  const handleExport = async () => {
    try {
      setLoading(true);
      // 调用导出API
      const response = await api.exportLearningStatistics({
        type: activeTab,
        ...filters
      });
      // 触发下载
      // ...
      message.success('导出成功');
    } catch (err) {
      message.error('导出失败');
    } finally {
      setLoading(false);
    }
  };

  // 刷新数据
  const handleRefresh = () => {
    setRefreshKey(prev => prev + 1);
  };

  return (
    <div className={styles.container}>
      <Row className="playedu-main-body">
        <Col span={24}>
          {/* 页面标题 */}
          <div className="float-left mb-24">
            <h2>学习统计分析</h2>
          </div>

          {/* 操作按钮 */}
          <div className="float-left j-b-flex mb-24">
            <div></div>
            <Space>
              <Button 
                icon={<ReloadOutlined />}
                onClick={handleRefresh}
                loading={loading}
              >
                刷新
              </Button>
              <Button 
                type="primary"
                icon={<DownloadOutlined />}
                onClick={handleExport}
                loading={loading}
              >
                导出
              </Button>
            </Space>
          </div>

          {/* 统计概览卡片 */}
          <div className={`${styles.statisticsBox} mb-24`}>
            <Row gutter={16}>
              <Col span={6}>
                <StatisticsCard 
                  title="总课程数" 
                  value={statistics.total_courses}
                  unit=""
                />
              </Col>
              <Col span={6}>
                <StatisticsCard 
                  title="总学员数" 
                  value={statistics.total_users}
                  unit=""
                />
              </Col>
              <Col span={6}>
                <StatisticsCard 
                  title="平均完成率" 
                  value={statistics.avg_completion_rate}
                  unit="%"
                />
              </Col>
              <Col span={6}>
                <StatisticsCard 
                  title="平均及格率" 
                  value={statistics.avg_passing_rate}
                  unit="%"
                />
              </Col>
            </Row>
          </div>

          {/* 筛选条件栏 */}
          <div className="mb-24">
            <FilterBar 
              filters={filters}
              onChange={setFilters}
            />
          </div>

          {/* Tab 页面 */}
          <Card>
            <Tabs
              activeKey={activeTab}
              onChange={setActiveTab}
              items={[
                {
                  key: 'course',
                  label: '课程维度',
                  children: (
                    <Spin spinning={loading}>
                      <CourseView key={`course-${refreshKey}`} filters={filters} />
                    </Spin>
                  )
                },
                {
                  key: 'user',
                  label: '人员维度',
                  children: (
                    <Spin spinning={loading}>
                      <UserView key={`user-${refreshKey}`} filters={filters} />
                    </Spin>
                  )
                }
              ]}
            />
          </Card>
        </Col>
      </Row>
    </div>
  );
};

export default LearningStatisticsPage;
```

### 4.2 课程维度组件（CourseView.tsx）

```typescript
import React, { useState, useEffect } from 'react';
import { Table, Tabs, Button, Space, Popover, message } from 'antd';
import { PlusOutlined, LineChartOutlined } from '@ant-design/icons';
import ChartView from './ChartView';
import CourseDetailModal from './CourseDetailModal';
import { learning_statistics } from '../../api';

interface CourseItem {
  course_id: number;
  course_name: string;
  total_users: number;
  completed_users: number;
  completion_rate: number;
  average_score: number;
  passing_users: number;
  passing_rate: number;
}

const CourseView: React.FC<{ filters: any }> = ({ filters }) => {
  const [viewType, setViewType] = useState('table'); // 'table' | 'chart'
  const [loading, setLoading] = useState(false);
  const [data, setData] = useState<CourseItem[]>([]);
  const [total, setTotal] = useState(0);
  const [page, setPage] = useState(1);
  const [size, setSize] = useState(10);
  const [detailModalVisible, setDetailModalVisible] = useState(false);
  const [selectedCourse, setSelectedCourse] = useState<any>(null);

  useEffect(() => {
    fetchData();
  }, [filters, page, size]);

  const fetchData = async () => {
    try {
      setLoading(true);
      const response = await learning_statistics.courseList(
        page,
        size,
        filters
      );
      setData(response.data.items);
      setTotal(response.data.total);
    } catch (err) {
      message.error('加载数据失败');
    } finally {
      setLoading(false);
    }
  };

  const columns = [
    {
      title: '课程名称',
      dataIndex: 'course_name',
      key: 'course_name',
      width: 200,
      ellipsis: true,
    },
    {
      title: '总学员数',
      dataIndex: 'total_users',
      key: 'total_users',
      width: 100,
      align: 'center',
    },
    {
      title: '已完成',
      dataIndex: 'completed_users',
      key: 'completed_users',
      width: 100,
      align: 'center',
      render: (text: number, record: CourseItem) => 
        `${text} / ${record.total_users}`
    },
    {
      title: '完成率',
      dataIndex: 'completion_rate',
      key: 'completion_rate',
      width: 100,
      align: 'center',
      render: (rate: number) => `${(rate * 100).toFixed(1)}%`
    },
    {
      title: '平均分',
      dataIndex: 'average_score',
      key: 'average_score',
      width: 100,
      align: 'center',
      render: (score: number) => score.toFixed(1)
    },
    {
      title: '及格人数',
      dataIndex: 'passing_users',
      key: 'passing_users',
      width: 100,
      align: 'center',
      render: (text: number, record: CourseItem) => 
        `${text} / ${record.total_users}`
    },
    {
      title: '及格率',
      dataIndex: 'passing_rate',
      key: 'passing_rate',
      width: 100,
      align: 'center',
      render: (rate: number) => `${(rate * 100).toFixed(1)}%`
    },
    {
      title: '操作',
      key: 'action',
      fixed: 'right',
      width: 120,
      render: (_: any, record: CourseItem) => (
        <Space>
          <Button
            type="link"
            size="small"
            onClick={() => {
              setSelectedCourse(record);
              setDetailModalVisible(true);
            }}
          >
            详情
          </Button>
        </Space>
      )
    }
  ];

  return (
    <>
      {/* 视图切换 */}
      <div style={{ marginBottom: 16 }}>
        <Space>
          <Button 
            type={viewType === 'table' ? 'primary' : 'default'}
            onClick={() => setViewType('table')}
          >
            表格视图
          </Button>
          <Button 
            type={viewType === 'chart' ? 'primary' : 'default'}
            onClick={() => setViewType('chart')}
            icon={<LineChartOutlined />}
          >
            图表视图
          </Button>
        </Space>
      </div>

      {/* 表格视图 */}
      {viewType === 'table' && (
        <Table
          loading={loading}
          columns={columns}
          dataSource={data}
          rowKey="course_id"
          pagination={{
            current: page,
            pageSize: size,
            total: total,
            onChange: (p, s) => {
              setPage(p);
              setSize(s);
            }
          }}
        />
      )}

      {/* 图表视图 */}
      {viewType === 'chart' && (
        <ChartView type="course" data={data} />
      )}

      {/* 课程详情弹窗 */}
      {selectedCourse && (
        <CourseDetailModal
          visible={detailModalVisible}
          courseId={selectedCourse.course_id}
          courseName={selectedCourse.course_name}
          onClose={() => {
            setDetailModalVisible(false);
            setSelectedCourse(null);
          }}
        />
      )}
    </>
  );
};

export default CourseView;
```

### 4.3 统计卡片组件（StatisticsCard.tsx）

```typescript
import React from 'react';
import { Card, Statistic, Row } from 'antd';
import styles from './index.module.less';

interface StatisticsCardProps {
  title: string;
  value: number | string;
  unit?: string;
  color?: string;
}

const StatisticsCard: React.FC<StatisticsCardProps> = ({
  title,
  value,
  unit = '',
  color = '#1890ff'
}) => {
  return (
    <Card className={styles.statisticsCard}>
      <Statistic
        title={title}
        value={value}
        suffix={unit}
        valueStyle={{ color }}
      />
    </Card>
  );
};

export default StatisticsCard;
```

---

## 五、后端实现方案

### 5.1 Controller 类

```java
@RestController
@Slf4j
@RequestMapping("/backend/v1/learning-statistics")
public class LearningStatisticsController {

    @Autowired private LearningStatisticsService learningStatisticsService;
    
    @Autowired private UserService userService;
    
    @Autowired private CourseService courseService;
    
    @Autowired private ExamService examService;

    /**
     * 课程维度统计列表
     */
    @BackendPermission(slug = BPermissionConstant.LEARNING_STATISTICS)
    @GetMapping("/course")
    @SneakyThrows
    @Log(title = "学习统计-课程维度", businessType = BusinessTypeConstant.GET)
    public JsonResponse courseStatistics(
            @RequestParam(defaultValue = "1") Integer page,
            @RequestParam(defaultValue = "10") Integer size,
            @RequestParam(required = false) String dep_ids,
            @RequestParam(required = false) String course_ids,
            @RequestParam(required = false) String start_date,
            @RequestParam(required = false) String end_date,
            @RequestParam(required = false) String search) {
        
        Map<String, Object> params = new HashMap<>();
        params.put("dep_ids", dep_ids);
        params.put("course_ids", course_ids);
        params.put("start_date", start_date);
        params.put("end_date", end_date);
        params.put("search", search);
        
        Map<String, Object> data = learningStatisticsService.courseStatistics(
            page, size, params
        );
        
        return JsonResponse.data(data);
    }

    /**
     * 课程详情 - 学员列表
     */
    @BackendPermission(slug = BPermissionConstant.LEARNING_STATISTICS)
    @GetMapping("/course/{courseId}/users")
    @SneakyThrows
    @Log(title = "学习统计-课程详情", businessType = BusinessTypeConstant.GET)
    public JsonResponse courseUserDetail(
            @PathVariable Integer courseId,
            @RequestParam(defaultValue = "1") Integer page,
            @RequestParam(defaultValue = "10") Integer size,
            @RequestParam(required = false) String dep_ids,
            @RequestParam(required = false) String search,
            @RequestParam(required = false) String status,
            @RequestParam(required = false) String pass_status) {
        
        Map<String, Object> params = new HashMap<>();
        params.put("dep_ids", dep_ids);
        params.put("search", search);
        params.put("status", status);
        params.put("pass_status", pass_status);
        
        Map<String, Object> data = learningStatisticsService.courseUserDetail(
            courseId, page, size, params
        );
        
        return JsonResponse.data(data);
    }

    /**
     * 人员维度统计列表
     */
    @BackendPermission(slug = BPermissionConstant.LEARNING_STATISTICS)
    @GetMapping("/user")
    @SneakyThrows
    @Log(title = "学习统计-人员维度", businessType = BusinessTypeConstant.GET)
    public JsonResponse userStatistics(
            @RequestParam(defaultValue = "1") Integer page,
            @RequestParam(defaultValue = "10") Integer size,
            @RequestParam(required = false) String dep_ids,
            @RequestParam(required = false) String start_date,
            @RequestParam(required = false) String end_date,
            @RequestParam(required = false) String search) {
        
        Map<String, Object> params = new HashMap<>();
        params.put("dep_ids", dep_ids);
        params.put("start_date", start_date);
        params.put("end_date", end_date);
        params.put("search", search);
        
        Map<String, Object> data = learningStatisticsService.userStatistics(
            page, size, params
        );
        
        return JsonResponse.data(data);
    }

    /**
     * 人员详情 - 课程列表
     */
    @BackendPermission(slug = BPermissionConstant.LEARNING_STATISTICS)
    @GetMapping("/user/{userId}/courses")
    @SneakyThrows
    @Log(title = "学习统计-人员详情", businessType = BusinessTypeConstant.GET)
    public JsonResponse userCourseDetail(
            @PathVariable Integer userId,
            @RequestParam(defaultValue = "1") Integer page,
            @RequestParam(defaultValue = "10") Integer size,
            @RequestParam(required = false) String search,
            @RequestParam(required = false) String status) {
        
        Map<String, Object> params = new HashMap<>();
        params.put("search", search);
        params.put("status", status);
        
        Map<String, Object> data = learningStatisticsService.userCourseDetail(
            userId, page, size, params
        );
        
        return JsonResponse.data(data);
    }

    /**
     * 数据导出
     */
    @BackendPermission(slug = BPermissionConstant.LEARNING_STATISTICS)
    @GetMapping("/export")
    @SneakyThrows
    @Log(title = "学习统计-数据导出", businessType = BusinessTypeConstant.GET)
    public void export(
            HttpServletResponse response,
            @RequestParam String type,
            @RequestParam(required = false) String dep_ids,
            @RequestParam(required = false) String course_ids,
            @RequestParam(required = false) String start_date,
            @RequestParam(required = false) String end_date) {
        
        Map<String, Object> params = new HashMap<>();
        params.put("dep_ids", dep_ids);
        params.put("course_ids", course_ids);
        params.put("start_date", start_date);
        params.put("end_date", end_date);
        
        learningStatisticsService.export(response, type, params);
    }
}
```

### 5.2 Service 类

```java
@Service
@Slf4j
public class LearningStatisticsService {
    
    @Autowired private UserCourseRecordMapper userCourseRecordMapper;
    @Autowired private ExamUserRecordMapper examUserRecordMapper;
    @Autowired private CourseMapper courseMapper;
    @Autowired private CourseExamMapper courseExamMapper;
    @Autowired private UserMapper userMapper;
    @Autowired private DepartmentMapper departmentMapper;

    /**
     * 课程维度统计
     */
    public Map<String, Object> courseStatistics(Integer page, Integer size, Map<String, Object> params) {
        // 参数处理
        List<Integer> depIds = parseIds((String) params.get("dep_ids"));
        List<Integer> courseIds = parseIds((String) params.get("course_ids"));
        String search = (String) params.get("search");

        // 分页查询
        IPage<Course> coursePage = courseMapper.selectPage(
            new Page<>(page, size),
            new LambdaQueryWrapper<Course>()
                .like(StringUtils.isNotBlank(search), Course::getTitle, search)
                .orderByDesc(Course::getCreatedAt)
        );

        List<Map<String, Object>> items = new ArrayList<>();
        List<Course> courses = coursePage.getRecords();

        for (Course course : courses) {
            // 统计该课程的学习情况
            Map<String, Object> courseStats = new HashMap<>();
            courseStats.put("course_id", course.getId());
            courseStats.put("course_name", course.getTitle());
            
            // 获取学员总数
            long totalUsers = userCourseRecordMapper.selectCount(
                new LambdaQueryWrapper<UserCourseRecord>()
                    .eq(UserCourseRecord::getCourseId, course.getId())
            );
            courseStats.put("total_users", totalUsers);

            // 获取已完成人数
            long completedUsers = userCourseRecordMapper.selectCount(
                new LambdaQueryWrapper<UserCourseRecord>()
                    .eq(UserCourseRecord::getCourseId, course.getId())
                    .eq(UserCourseRecord::getIsFinished, 1)
            );
            courseStats.put("completed_users", completedUsers);
            courseStats.put("incomplete_users", totalUsers - completedUsers);
            
            // 完成率
            double completionRate = totalUsers > 0 ? 
                (double) completedUsers / totalUsers : 0;
            courseStats.put("completion_rate", completionRate);

            // 获取平均分和及格人数
            Map<String, Object> examStats = getExamStatistics(course.getId());
            courseStats.putAll(examStats);

            items.add(courseStats);
        }

        Map<String, Object> result = new HashMap<>();
        result.put("total", coursePage.getTotal());
        result.put("items", items);
        result.put("statistics", getOverallStatistics());

        return result;
    }

    /**
     * 获取考试统计信息
     */
    private Map<String, Object> getExamStatistics(Integer courseId) {
        Map<String, Object> stats = new HashMap<>();
        
        // 获取该课程关联的考试
        List<CourseExam> courseExams = courseExamMapper.selectList(
            new LambdaQueryWrapper<CourseExam>()
                .eq(CourseExam::getCourseId, courseId)
        );
        
        if (courseExams.isEmpty()) {
            stats.put("average_score", 0);
            stats.put("passing_users", 0);
            stats.put("passing_rate", 0);
            return stats;
        }

        List<Long> examIds = courseExams.stream()
            .map(CourseExam::getExamId)
            .collect(Collectors.toList());

        // 查询该课程的考试成绩记录
        List<ExamUserRecord> records = examUserRecordMapper.selectList(
            new LambdaQueryWrapper<ExamUserRecord>()
                .in(ExamUserRecord::getExamId, examIds)
        );

        if (records.isEmpty()) {
            stats.put("average_score", 0);
            stats.put("passing_users", 0);
            stats.put("passing_rate", 0);
            return stats;
        }

        // 计算平均分
        double avgScore = records.stream()
            .mapToInt(r -> r.getScore() != null ? r.getScore() : 0)
            .average()
            .orElse(0);
        stats.put("average_score", avgScore);

        // 计算及格人数和及格率
        long passingUsers = records.stream()
            .filter(r -> r.getPassStatus() != null && r.getPassStatus() == 1)
            .count();
        stats.put("passing_users", passingUsers);
        stats.put("passing_rate", 
            (double) passingUsers / records.size());

        return stats;
    }

    /**
     * 获取总体统计
     */
    private Map<String, Object> getOverallStatistics() {
        Map<String, Object> stats = new HashMap<>();
        
        // 总课程数
        long totalCourses = courseMapper.selectCount(null);
        stats.put("total_courses", totalCourses);
        
        // 总学员数
        long totalUsers = userMapper.selectCount(null);
        stats.put("total_users", totalUsers);
        
        // 平均完成率
        // 平均及格率
        // ... 计算逻辑
        
        return stats;
    }

    // 其他方法...
}
```

---

## 六、权限管理

### 6.1 权限常量

```java
public class BPermissionConstant {
    // 学习统计分析权限
    public static final String LEARNING_STATISTICS = "learning-statistics";
}
```

### 6.2 菜单配置

在系统菜单配置中添加：

```java
{
  "id": 51,
  "name": "学习统计",
  "icon": "chart",
  "path": "/learning-statistics",
  "component": "pages/learning-statistics/index",
  "permissions": [
    {
      "id": 511,
      "name": "查看",
      "slug": "learning-statistics"
    }
  ]
}
```

---

## 七、关键功能细节

### 7.1 数据计算说明

#### 完成率计算
```
完成率 = 已完成课程的学员数 / 该课程总学员数 × 100%
```

#### 及格率计算
```
及格率 = 考试及格的学员数 / 参加考试的学员数 × 100%
```

#### 平均分计算
```
平均分 = 所有学员考试分数之和 / 学员数
```

### 7.2 性能优化建议

1. **数据库索引**
   - 在 `user_course_records(course_id, user_id)` 建立复合索引
   - 在 `exam_user_records(exam_id, user_id)` 建立复合索引
   - 在 `course_exams(course_id)` 建立索引

2. **查询优化**
   - 使用 MyBatis Plus 的 `selectPage` 进行分页查询
   - 避免 N+1 查询问题，使用 LEFT JOIN 关联查询

3. **缓存策略**
   - 对统计数据设置 Redis 缓存（TTL: 1小时）
   - 在数据更新时主动清除相关缓存

4. **异步导出**
   - 大数据量导出使用异步任务处理
   - 通过消息队列发送导出任务
   - 生成Excel后存储到对象存储，返回下载链接

### 7.3 Excel 导出格式

**课程维度导出表：**
```
课程名称 | 总学员数 | 已完成 | 未完成 | 完成率 | 平均分 | 及格人数 | 及格率
```

**人员维度导出表：**
```
学员姓名 | 学员邮箱 | 所属部门 | 已学课程 | 完成课程 | 完成率 | 平均分 | 及格课程 | 及格率
```

---

## 八、路由配置

### 8.1 前端路由

```typescript
// routes/index.tsx
{
  path: '/learning-statistics',
  component: () => import('../pages/learning-statistics/index'),
  meta: {
    title: '学习统计分析',
    permission: 'learning-statistics'
  }
}
```

### 8.2 后端路由

```java
// 已在 Controller 中配置
@RequestMapping("/backend/v1/learning-statistics")
```

---

## 九、安全考虑

1. **权限校验**：所有接口均需验证用户权限
2. **数据隐私**：不同部门的管理员只能看到其部门的数据
3. **SQL 注入防护**：使用 MyBatis Plus 的参数绑定
4. **速率限制**：导出操作添加速率限制，防止滥用

---

## 十、后续扩展方向

1. **更多维度分析**
   - 按考试类型统计
   - 按学习时间段统计
   - 按学员等级统计

2. **高级功能**
   - 数据对比分析（如同比、环比）
   - 异常预警（如学员长期未学习）
   - 学习效果评估模型

3. **集成功能**
   - 与大数据平台集成
   - 机器学习模型支持
   - 移动端查看

---

## 十一、实现时间表

| 阶段 | 任务 | 时间估计 |
|-----|------|---------|
| 1 | 后端API开发 | 5-7天 |
| 2 | 前端页面开发 | 5-7天 |
| 3 | 集成测试 | 3-5天 |
| 4 | 性能优化 | 2-3天 |
| 5 | 用户验收测试 | 2-3天 |
| **总计** | - | **17-25天** |

---

## 十二、代码示例

### API 调用示例

```typescript
// playedu-admin/src/api/learning-statistics.ts
import client from "./internal/httpClient";

export const learning_statistics = {
  // 课程维度统计
  courseList: (page: number, size: number, filters: any) =>
    client.get("/backend/v1/learning-statistics/course", {
      page,
      size,
      ...filters,
    }),

  // 课程详情
  courseUsers: (courseId: number, page: number, size: number, filters: any) =>
    client.get(`/backend/v1/learning-statistics/course/${courseId}/users`, {
      page,
      size,
      ...filters,
    }),

  // 人员维度统计
  userList: (page: number, size: number, filters: any) =>
    client.get("/backend/v1/learning-statistics/user", {
      page,
      size,
      ...filters,
    }),

  // 人员详情
  userCourses: (userId: number, page: number, size: number, filters: any) =>
    client.get(`/backend/v1/learning-statistics/user/${userId}/courses`, {
      page,
      size,
      ...filters,
    }),

  // 导出数据
  export: (type: string, filters: any) =>
    client.get("/backend/v1/learning-statistics/export", {
      type,
      ...filters,
    }),
};
```

