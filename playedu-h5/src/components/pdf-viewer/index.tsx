import { useEffect, useRef, useState } from "react";
import * as pdfjsLib from "pdfjs-dist";
import styles from "./index.module.scss";
import { Toast } from "antd-mobile";
import { getToken } from "../../utils";

// 配置 PDF.js worker
pdfjsLib.GlobalWorkerOptions.workerSrc = '/pdf.worker.min.mjs';

interface PdfViewerProps {
  url: string;
  onProgressChange?: (current: number, total: number, viewedPages: Set<number>) => void;
}

export const PdfViewer: React.FC<PdfViewerProps> = ({ url, onProgressChange }) => {
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const containerRef = useRef<HTMLDivElement>(null);
  const renderTaskRef = useRef<any>(null);
  const renderingRef = useRef(false);
  const onProgressChangeRef = useRef(onProgressChange);
  const [pdf, setPdf] = useState<any>(null);
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(0);
  const [loading, setLoading] = useState(true);
  const [viewedPages, setViewedPages] = useState<Set<number>>(new Set([1]));
  const [scale, setScale] = useState(0);

  // 更新回调引用
  useEffect(() => {
    onProgressChangeRef.current = onProgressChange;
  }, [onProgressChange]);

  // 加载 PDF
  useEffect(() => {
    const loadPdf = async () => {
      try {
        setLoading(true);
        setScale(0);
        
        // 获取 token
        const token = getToken();
        console.log('📄 [H5 PdfViewer] 开始加载 PDF');
        console.log('📄 [H5 PdfViewer] URL:', url);
        console.log('📄 [H5 PdfViewer] Token:', token ? `${token.substring(0, 20)}...` : '无token');
        
        // 使用 fetch 手动下载 PDF，这样可以添加 Authorization header
        const response = await fetch(url, {
          headers: {
            'Authorization': token ? `Bearer ${token}` : ''
          }
        });
        
        console.log('📄 [H5 PdfViewer] 响应状态:', response.status);
        console.log('📄 [H5 PdfViewer] 响应头 Content-Type:', response.headers.get('Content-Type'));
        console.log('📄 [H5 PdfViewer] 响应头 Content-Length:', response.headers.get('Content-Length'));
        
        if (!response.ok) {
          throw new Error(`HTTP error! status: ${response.status}`);
        }
        
        // 将响应转换为 ArrayBuffer
        const arrayBuffer = await response.arrayBuffer();
        console.log('📄 [H5 PdfViewer] ArrayBuffer 大小:', arrayBuffer.byteLength, 'bytes');
        
        // 打印前几个字节，验证是否是 PDF 文件（PDF 文件以 %PDF- 开头）
        const uint8Array = new Uint8Array(arrayBuffer);
        const header = String.fromCharCode(...uint8Array.slice(0, 8));
        console.log('📄 [H5 PdfViewer] 文件头:', header);
        
        // 使用 ArrayBuffer 加载 PDF
        const loadingTask = pdfjsLib.getDocument({ data: arrayBuffer });
        const pdfDoc = await loadingTask.promise;
        
        console.log('📄 [H5 PdfViewer] PDF 加载成功，总页数:', pdfDoc.numPages);
        
        setPdf(pdfDoc);
        setTotalPages(pdfDoc.numPages);
        setLoading(false);
      } catch (error) {
        console.error("📄 [H5 PdfViewer] PDF 加载失败:", error);
        console.error("📄 [H5 PdfViewer] 错误详情:", (error as any).message, (error as any).stack);
        Toast.show({ icon: 'fail', content: 'PDF 加载失败' });
        setLoading(false);
      }
    };

    if (url) {
      loadPdf();
    }
  }, [url]);

  // 通知父组件进度变化
  useEffect(() => {
    if (totalPages > 0 && viewedPages.size > 0 && onProgressChangeRef.current) {
      onProgressChangeRef.current(currentPage, totalPages, viewedPages);
    }
  }, [viewedPages, currentPage, totalPages]);

  // 渲染当前页
  useEffect(() => {
    if (!pdf || !canvasRef.current || !containerRef.current) return;

    const renderPage = async () => {
      try {
        if (renderingRef.current && renderTaskRef.current) {
          try {
            renderTaskRef.current.cancel();
            await new Promise(resolve => setTimeout(resolve, 10));
          } catch (e) {
            // 忽略取消错误
          }
        }

        const canvas = canvasRef.current;
        const container = containerRef.current;
        if (!canvas || !container) return;

        const context = canvas.getContext("2d");
        if (!context) return;
        context.clearRect(0, 0, canvas.width, canvas.height);

        renderingRef.current = true;

        const page = await pdf.getPage(currentPage);
        
        let finalScale = scale;
        if (scale === 0) {
          const originalViewport = page.getViewport({ scale: 1.0 });
          
          // 移动端适配：减去更多边距和工具栏空间
          const containerWidth = container.clientWidth - 20; // 移动端边距更小
          const containerHeight = container.clientHeight - 100; // 留出更多工具栏空间
          
          const scaleByWidth = containerWidth / originalViewport.width;
          const scaleByHeight = containerHeight / originalViewport.height;
          // 移动端使用稍小的缩放范围
          finalScale = Math.max(1.0, Math.min(scaleByWidth, scaleByHeight, 2.0));
          
          setScale(finalScale);
        }
        
        const viewport = page.getViewport({ scale: finalScale });

        canvas.height = viewport.height;
        canvas.width = viewport.width;

        const renderContext = {
          canvasContext: context,
          viewport: viewport,
        };

        renderTaskRef.current = page.render(renderContext);
        
        await renderTaskRef.current.promise;
        
        renderTaskRef.current = null;
        renderingRef.current = false;

        setViewedPages((prev) => {
          const newSet = new Set(prev);
          newSet.add(currentPage);
          return newSet;
        });
      } catch (error: any) {
        renderingRef.current = false;
        renderTaskRef.current = null;
        
        if (error.name !== 'RenderingCancelledException') {
          console.error("页面渲染失败:", error);
        }
      }
    };

    renderPage();
    
    return () => {
      if (renderTaskRef.current) {
        try {
          renderTaskRef.current.cancel();
        } catch (e) {
          // 忽略
        }
      }
      renderingRef.current = false;
    };
  }, [pdf, currentPage, scale]);

  const handlePrevPage = () => {
    if (currentPage > 1) {
      setCurrentPage(currentPage - 1);
    }
  };

  const handleNextPage = () => {
    if (currentPage < totalPages) {
      setCurrentPage(currentPage + 1);
    }
  };

  // 触摸滑动支持
  const touchStartX = useRef(0);
  const handleTouchStart = (e: React.TouchEvent) => {
    touchStartX.current = e.touches[0].clientX;
  };

  const handleTouchEnd = (e: React.TouchEvent) => {
    const touchEndX = e.changedTouches[0].clientX;
    const diff = touchStartX.current - touchEndX;
    
    // 滑动距离超过 50px 才翻页
    if (Math.abs(diff) > 50) {
      if (diff > 0) {
        // 向左滑动，下一页
        handleNextPage();
      } else {
        // 向右滑动，上一页
        handlePrevPage();
      }
    }
  };

  if (loading) {
    return (
      <div className={styles["pdf-loading"]}>
        <div className={styles["loading-spinner"]}></div>
        <div>PDF 加载中...</div>
      </div>
    );
  }

  return (
    <div className={styles["pdf-viewer"]} ref={containerRef}>
      <div className={styles["pdf-toolbar"]}>
        <button
          className={styles["nav-btn"]}
          onClick={handlePrevPage}
          disabled={currentPage === 1}
        >
          ◀
        </button>
        <span className={styles["page-info"]}>
          {currentPage} / {totalPages}
          <span className={styles["viewed-info"]}>
            （已览 {viewedPages.size}）
          </span>
        </span>
        <button
          className={styles["nav-btn"]}
          onClick={handleNextPage}
          disabled={currentPage === totalPages}
        >
          ▶
        </button>
      </div>

      <div 
        className={styles["pdf-content"]} 
        onTouchStart={handleTouchStart}
        onTouchEnd={handleTouchEnd}
      >
        <canvas ref={canvasRef} className={styles["pdf-canvas"]} />
      </div>
    </div>
  );
};

