.pdf-viewer {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  background: #525659;
  position: relative;
}

.pdf-toolbar {
  height: 50px;
  background: #323639;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 12px; // 移动端减小间距
  color: white;
  font-size: 14px;
  flex-shrink: 0;
  position: sticky;
  top: 0;
  z-index: 10;

  .nav-btn {
    min-width: 40px;
    height: 36px;
    padding: 0 12px;
    background: #4a4e51;
    border: none;
    border-radius: 4px;
    color: white;
    cursor: pointer;
    font-size: 18px; // 移动端按钮图标更大
    display: flex;
    align-items: center;
    justify-content: center;
    transition: background 0.3s;
    -webkit-tap-highlight-color: transparent; // 移除点击高亮

    &:active:not(:disabled) {
      background: #5a5e61;
    }

    &:disabled {
      opacity: 0.5;
      cursor: not-allowed;
    }
  }

  .page-info {
    font-size: 14px;
    font-weight: 500;
    flex: 1;
    text-align: center;

    .viewed-info {
      margin-left: 8px;
      font-size: 12px;
      color: #a0a4a8;
      display: inline-block;
      
      // 小屏幕隐藏已浏览信息
      @media (max-width: 360px) {
        display: none;
      }
    }
  }
}

.pdf-content {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: auto; // 移动端允许滚动查看大图
  padding: 10px;
  -webkit-overflow-scrolling: touch; // iOS 平滑滚动

  .pdf-canvas {
    display: block;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
    max-width: 100%;
    height: auto; // 移动端自适应高度
    touch-action: pan-y pinch-zoom; // 支持缩放
  }
}

.pdf-loading {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 16px;
  background: #525659;

  .loading-spinner {
    width: 40px;
    height: 40px;
    border: 4px solid rgba(255, 255, 255, 0.3);
    border-top-color: white;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-bottom: 16px;
  }
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

// 移动端小屏适配
@media (max-width: 375px) {
  .pdf-toolbar {
    height: 44px;
    padding: 0 8px;
    
    .nav-btn {
      min-width: 36px;
      height: 32px;
      font-size: 16px;
    }
    
    .page-info {
      font-size: 13px;
    }
  }
}

