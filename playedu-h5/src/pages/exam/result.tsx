import { useEffect, useState } from "react";
import { useNavigate, useParams, useSearchParams } from "react-router-dom";
import { exam as Exam } from "../../api/index";
import styles from "./result.module.scss";
import { Button, Card, Modal, List, Toast } from "antd-mobile";
import { CheckCircleOutline, CloseCircleOutline } from "antd-mobile-icons";

const ExamResultPage = () => {
  const navigate = useNavigate();
  const params = useParams();
  const [searchParams] = useSearchParams();
  const courseId = searchParams.get("courseId");
  
  const [loading, setLoading] = useState(true);
  const [result, setResult] = useState<any>(null);
  const [exam, setExam] = useState<any>(null);

  useEffect(() => {
    loadResult();
  }, [params.examId]);

  const loadResult = () => {
    Promise.all([
      Exam.examResult(Number(params.examRecordId)),
      Exam.examDetail(Number(params.examId))
    ])
      .then(([resultRes, examRes]: any) => {
        setResult((resultRes as any).data);
        setExam((examRes as any).data);
        setLoading(false);
      })
      .catch(() => {
        setLoading(false);
      });
  };

  const loadWrongQuestions = () => {
    const wrongIds = result.wrong_questions || [];
    const allQuestions = exam.questions || [];
    
    const wrongQs = allQuestions
      .filter((q: any) => wrongIds.includes(q.id))
      .map((q: any, index: number) => ({
        ...q,
        questionNumber: index + 1
      }));
    
    console.log('🔍 [H5 Result] 筛选出的错题:', wrongQs);
    
    if (wrongQs.length > 0) {
      Modal.show({
        title: '错题详情',
        closeOnMaskClick: true,
        content: (
          <List>
            {wrongQs.map((q: any, index: number) => (
              <List.Item key={q.id}>
                <div className={styles["wrong-item"]}>
                  <div className={styles["wrong-header"]}>
                    <span className={styles["wrong-number"]}>第 {index + 1} 题</span>
                    <span className={styles["wrong-type"]}>{getQuestionTypeName(q.type)}</span>
                  </div>
                  <div className={styles["wrong-title"]}>{q.title}</div>
                  {q.options && q.options.length > 0 ? (
                    q.options.map((opt: any) => (
                      <div
                        key={opt.id}
                        className={opt.is_correct ? styles["option-correct"] : styles["option"]}
                      >
                        {opt.option_key}. {opt.content}
                        {opt.is_correct && <span className={styles["correct-mark"]}> ✓ 正确答案</span>}
                      </div>
                    ))
                  ) : (
                    <div style={{ color: '#999' }}>暂无选项数据</div>
                  )}
                </div>
              </List.Item>
            ))}
          </List>
        ),
      });
    } else {
      Toast.show({ content: '暂无错题' });
    }
  };

  const handleBack = () => {
    navigate(`/course/${courseId}`);
  };

  const getQuestionTypeName = (type: any) => {
    const typeMap: any = {
      1: "单选题",
      2: "多选题",
      3: "填空题",
      4: "论述题",
      "single_choice": "单选题",
      "multiple_choice": "多选题",
      "fill_blank": "填空题",
      "essay": "论述题"
    };
    return typeMap[type] || "未知";
  };

  if (loading) {
    return <div className={styles["loading"]}>加载中...</div>;
  }

  const isPassed = result?.passed || result?.pass_status === 1;
  const score = result?.score || 0;  // 得分
  const total = result?.total || 0;  // 总分
  const wrongCount = result?.wrong_questions?.length || 0;

  return (
    <div className={styles["container"]}>
      {/* 结果卡片 */}
      <div className={styles[isPassed ? "result-pass" : "result-fail"]}>
        <div className={styles["result-icon"]}>
          {isPassed ? <CheckCircleOutline fontSize={80} /> : <CloseCircleOutline fontSize={80} />}
        </div>
        <div className={styles["result-status"]}>
          {isPassed ? "恭喜通过！" : "未通过"}
        </div>
        <div className={styles["result-score"]}>{score} 分</div>
        {wrongCount > 0 && (
          <div className={styles["wrong-hint"]}>答错 {wrongCount} 题</div>
        )}
      </div>

      {/* 统计信息 */}
      <div className={styles["stats"]}>
        <Card>
          <div className={styles["stat-grid"]}>
            <div className={styles["stat-item"]}>
              <div className={styles["stat-label"]}>考试得分</div>
              <div className={styles["stat-value"]}>{score}</div>
            </div>
            <div className={styles["stat-divider"]}></div>
            <div className={styles["stat-item"]}>
              <div className={styles["stat-label"]}>考试总分</div>
              <div className={styles["stat-value"]}>{total}</div>
            </div>
            <div className={styles["stat-divider"]}></div>
            <div className={styles["stat-item"]}>
              <div className={styles["stat-label"]}>答对题数</div>
              <div className={styles["stat-value"]}>
                {result?.correct_count || 0}
              </div>
            </div>
            <div className={styles["stat-divider"]}></div>
            <div className={styles["stat-item"]}>
              <div className={styles["stat-label"]}>答错题数</div>
              <div className={styles["stat-value"]}>{wrongCount}</div>
            </div>
          </div>
        </Card>
      </div>

      {/* 操作按钮 */}
      <div className={styles["actions"]}>
        {wrongCount > 0 && (
          <Button block size="large" onClick={loadWrongQuestions}>
            查看错题解析
          </Button>
        )}
        <Button
          block
          color="primary"
          size="large"
          style={{ marginTop: '12px' }}
          onClick={handleBack}
        >
          返回课程
        </Button>
      </div>

    </div>
  );
};

export default ExamResultPage;

