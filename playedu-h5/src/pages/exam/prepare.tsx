import { useEffect, useState } from "react";
import { useNavigate, useParams, useSearchParams } from "react-router-dom";
import { exam as Exam } from "../../api/index";
import styles from "./prepare.module.scss";
import { Button, Card, Toast, Image, List } from "antd-mobile";
import backIcon from "../../assets/images/commen/icon-back-n.png";

const ExamPreparePage = () => {
  const navigate = useNavigate();
  const params = useParams();
  const [searchParams] = useSearchParams();
  const courseId = searchParams.get("courseId");
  const [loading, setLoading] = useState(false);
  const [exam, setExam] = useState<any>(null);
  const [course, setCourse] = useState<any>(null);
  const [examRecord, setExamRecord] = useState<any>(null);
  const [hasPassed, setHasPassed] = useState(false);
  const [examValid, setExamValid] = useState(true);
  const [examError, setExamError] = useState("");
  const [wrongModalVisible, setWrongModalVisible] = useState(false);
  const [wrongQuestions, setWrongQuestions] = useState<any[]>([]);

  useEffect(() => {
    loadData();
  }, [params.examId]);

  const loadData = () => {
    setLoading(true);
    
    // 验证考试配置
    Exam.validateExamScore(Number(params.examId))
      .then((validateRes: any) => {
        if (validateRes.code !== 0) {
          setExamValid(false);
          setExamError(validateRes.msg || "考试配置异常");
          setLoading(false);
          return;
        }
        
        // 获取考试详情
        Exam.examDetail(Number(params.examId))
          .then((examRes: any) => {
            setExam(examRes.data);
            
            // 获取考试记录
            Exam.userExamRecord(Number(params.examId))
              .then((recordRes: any) => {
                if (recordRes.code === 0 && recordRes.data) {
                  setExamRecord(recordRes.data);
                  const passStatus = recordRes.data.passStatus || recordRes.data.pass_status;
                  setHasPassed(passStatus === 1 || passStatus === "1" || passStatus === true);
                }
                setLoading(false);
              })
              .catch(() => {
                setLoading(false);
              });
          })
          .catch(() => {
            Toast.show({ icon: 'fail', content: '加载考试信息失败' });
            setLoading(false);
          });
      })
      .catch(() => {
        Toast.show({ icon: 'fail', content: '验证考试配置失败' });
        setLoading(false);
      });
  };

  const loadWrongQuestions = () => {
    Exam.examResult(Number(params.examId)).then((res: any) => {
      const wrongIds = res.data.wrong_questions || [];
      const allQuestions = exam.questions || [];
      
      const wrongQs = allQuestions
        .filter((q: any) => wrongIds.includes(q.id))
        .map((q: any, index: number) => ({
          ...q,
          questionNumber: index + 1
        }));
      
      console.log('🔍 [H5 Prepare] 筛选出的错题:', wrongQs);
      
      if (wrongQs.length > 0) {
        setWrongQuestions(wrongQs);
        setWrongModalVisible(true);
      } else {
        Toast.show({ content: '暂无错题' });
      }
    }).catch(() => {
      Toast.show({ icon: 'fail', content: '加载错题失败' });
    });
  };

  const handleStartExam = () => {
    navigate(`/exam/${params.examId}/answer?courseId=${courseId}`);
  };

  const handleViewResult = () => {
    navigate(`/exam/${params.examId}/result?courseId=${courseId}`);
  };

  const handleBack = () => {
    navigate(`/course/${courseId}`);
  };

  const getQuestionTypeName = (type: any) => {
    const typeMap: any = {
      1: "单选题",
      2: "多选题",
      3: "填空题",
      4: "论述题",
      "single_choice": "单选题",
      "multiple_choice": "多选题",
      "fill_blank": "填空题",
      "essay": "论述题"
    };
    return typeMap[type] || "未知";
  };

  return (
    <div className={styles["container"]}>
      {/* 顶部导航栏 */}
      <div className={styles["top-bar"]}>
        <div className={styles["back-btn"]} onClick={handleBack}>
          <Image src={backIcon} width={20} height={20} />
          <span>返回课程</span>
        </div>
      </div>

      {/* 主体内容 */}
      <div className={styles["content"]}>
        {!loading && !examValid && (
          <Card className={styles["error-card"]}>
            <div className={styles["error-icon"]}>⚠️</div>
            <div className={styles["error-title"]}>考试配置异常</div>
            <div className={styles["error-msg"]}>{examError}</div>
            <Button block color="primary" onClick={handleBack}>
              返回课程
            </Button>
          </Card>
        )}

        {!loading && examValid && exam && (
          <>
            {/* 考试信息卡片 */}
            <Card className={styles["exam-card"]}>
              <div className={styles["exam-header"]}>
                <div className={styles["exam-icon"]}>📝</div>
                <div className={styles["exam-info"]}>
                  <div className={styles["exam-title"]}>{exam.title}</div>
                  <div className={styles["exam-meta"]}>
                    <span>共 {exam.questions?.length || 0} 题</span>
                    <span className={styles["divider"]}>|</span>
                    <span>及格 {exam.pass_score} 分</span>
                  </div>
                </div>
              </div>
            </Card>

            {/* 考试记录卡片 */}
            {examRecord && (
              <Card className={hasPassed ? styles["pass-card"] : styles["record-card"]}>
                <div className={styles["record-header"]}>
                  {hasPassed ? "✅ 您已通过此考试" : "📝 考试记录"}
                </div>
                <div className={styles["record-content"]}>
                  <div className={styles["record-item"]}>
                    <span className={styles["label"]}>得分：</span>
                    <span className={styles["value"]}>{examRecord.totalScore || examRecord.total_score || 0} 分</span>
                  </div>
                  <div className={styles["record-item"]}>
                    <span className={styles["label"]}>状态：</span>
                    <span className={hasPassed ? styles["pass"] : styles["fail"]}>
                      {hasPassed ? "通过" : "未通过"}
                    </span>
                  </div>
                  <div className={styles["record-item"]}>
                    <span className={styles["label"]}>正确率：</span>
                    <span className={styles["value"]}>
                      {examRecord.correctCount || examRecord.correct_count || 0} / {examRecord.answerCount || examRecord.answer_count || 0}
                    </span>
                  </div>
                </div>
              </Card>
            )}

            {/* 操作按钮 */}
            <div className={styles["actions"]}>
              {!hasPassed && (
                <Button
                  block
                  color="primary"
                  size="large"
                  onClick={handleStartExam}
                >
                  {examRecord ? "重新考试" : "开始考试"}
                </Button>
              )}
              
              {hasPassed && (
                <Button
                  block
                  size="large"
                  style={{ marginTop: '24px' }}
                  onClick={loadWrongQuestions}
                >
                  查看错题详情
                </Button>
              )}
            </div>
          </>
        )}

        {loading && (
          <div className={styles["loading"]}>
            <div className={styles["loading-spinner"]}></div>
            <div>加载中...</div>
          </div>
        )}
      </div>

      {/* 错题详情弹窗 */}
      {wrongModalVisible && wrongQuestions.length > 0 && (
        <div className={styles["wrong-modal-overlay"]}>
          <div className={styles["wrong-modal-content"]}>
            <h3>错题详情</h3>
        <List>
          {wrongQuestions.map((q: any, index: number) => (
            <List.Item key={q.id}>
              <div className={styles["wrong-item"]}>
                <div className={styles["wrong-header"]}>
                  <span className={styles["wrong-number"]}>第 {index + 1} 题</span>
                  <span className={styles["wrong-type"]}>{getQuestionTypeName(q.type)}</span>
                </div>
                <div className={styles["wrong-title"]}>{q.title}</div>
                {q.options && q.options.map((opt: any) => (
                  <div
                    key={opt.id}
                    className={opt.is_correct ? styles["option-correct"] : styles["option"]}
                  >
                    {opt.option_key}. {opt.content}
                    {opt.is_correct && <span className={styles["correct-mark"]}> ✓ 正确答案</span>}
                  </div>
                ))}
              </div>
            </List.Item>
          ))}
        </List>
            <Button block color="primary" onClick={() => setWrongModalVisible(false)}>
              关闭
            </Button>
          </div>
        </div>
      )}
    </div>
  );
};

export default ExamPreparePage;

