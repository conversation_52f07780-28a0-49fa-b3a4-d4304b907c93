.container {
  min-height: 100vh;
  background: #f5f7fa;
  display: flex;
  flex-direction: column;
}

.top-bar {
  height: 50px;
  background: #ffffff;
  display: flex;
  align-items: center;
  padding: 0 16px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  position: sticky;
  top: 0;
  z-index: 100;

  .back-btn {
    display: flex;
    align-items: center;
    gap: 6px;
    color: #333;
    font-size: 15px;
    cursor: pointer;
    -webkit-tap-highlight-color: transparent;

    &:active {
      opacity: 0.7;
    }
  }
}

.content {
  flex: 1;
  padding: 12px 16px;
  overflow-y: auto;
  -webkit-overflow-scrolling: touch;
}

.exam-card {
  margin-bottom: 12px;
  border-radius: 12px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);

  .exam-header {
    display: flex;
    align-items: center;
    gap: 12px;

    .exam-icon {
      width: 50px;
      height: 50px;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 32px;
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      border-radius: 12px;
    }

    .exam-info {
      flex: 1;

      .exam-title {
        font-size: 17px;
        font-weight: 600;
        color: #333;
        margin-bottom: 6px;
        line-height: 1.4;
      }

      .exam-meta {
        font-size: 13px;
        color: #999;
        display: flex;
        align-items: center;
        gap: 8px;

        .divider {
          color: #ddd;
        }
      }
    }
  }
}

.pass-card {
  margin-bottom: 12px;
  border-radius: 12px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
  background: linear-gradient(135deg, #f0fff4 0%, #c6f6d5 100%);
  border-left: 4px solid #48bb78;
}

.record-card {
  margin-bottom: 12px;
  border-radius: 12px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
  background: #fff;
}

.record-header {
  font-size: 16px;
  font-weight: 600;
  color: #333;
  margin-bottom: 12px;
}

.record-content {
  .record-item {
    display: flex;
    justify-content: space-between;
    padding: 8px 0;
    border-bottom: 1px solid #f0f0f0;

    &:last-child {
      border-bottom: none;
    }

    .label {
      font-size: 14px;
      color: #666;
    }

    .value {
      font-size: 15px;
      font-weight: 600;
      color: #333;
    }

    .pass {
      font-size: 15px;
      font-weight: 600;
      color: #48bb78;
    }

    .fail {
      font-size: 15px;
      font-weight: 600;
      color: #f56565;
    }
  }
}

.error-card {
  margin-bottom: 12px;
  border-radius: 12px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
  text-align: center;
  padding: 24px 16px;

  .error-icon {
    font-size: 48px;
    margin-bottom: 12px;
  }

  .error-title {
    font-size: 18px;
    font-weight: 600;
    color: #f56565;
    margin-bottom: 8px;
  }

  .error-msg {
    font-size: 14px;
    color: #999;
    margin-bottom: 20px;
    line-height: 1.6;
  }
}

.actions {
  margin-top: 24px;
}

.loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60px 0;
  color: #999;
  font-size: 14px;

  .loading-spinner {
    width: 40px;
    height: 40px;
    border: 3px solid #f0f0f0;
    border-top-color: #1677ff;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-bottom: 12px;
  }
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

// 错题详情样式
.wrong-item {
  padding: 8px 0;

  .wrong-header {
    display: flex;
    justify-content: space-between;
    margin-bottom: 8px;

    .wrong-number {
      font-size: 14px;
      font-weight: 600;
      color: #f56565;
    }

    .wrong-type {
      font-size: 13px;
      color: #999;
      background: #f0f0f0;
      padding: 2px 8px;
      border-radius: 4px;
    }
  }

  .wrong-title {
    font-size: 15px;
    color: #333;
    margin-bottom: 10px;
    line-height: 1.6;
  }

  .option {
    font-size: 14px;
    color: #666;
    padding: 8px 12px;
    margin-bottom: 6px;
    background: #f9f9f9;
    border-radius: 6px;
  }

  .option-correct {
    font-size: 14px;
    color: #48bb78;
    padding: 8px 12px;
    margin-bottom: 6px;
    background: #f0fff4;
    border: 1px solid #c6f6d5;
    border-radius: 6px;
    font-weight: 500;

    .correct-mark {
      color: #48bb78;
      font-weight: 600;
    }
  }
}

