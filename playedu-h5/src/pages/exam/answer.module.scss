.container {
  min-height: 100vh;
  background: #f5f7fa;
  display: flex;
  flex-direction: column;
}

.header {
  height: 50px;
  background: #ffffff;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 16px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  position: sticky;
  top: 0;
  z-index: 100;

  .timer {
    display: flex;
    align-items: center;
    gap: 6px;
    font-size: 15px;
    font-weight: 600;
    color: #f56565;
  }

  .progress {
    font-size: 14px;
    color: #666;
  }
}

.progress-bar {
  height: 3px;
  background: #e5e7eb;
  position: sticky;
  top: 50px;
  z-index: 99;

  .progress-fill {
    height: 100%;
    background: linear-gradient(90deg, #10b981 0%, #059669 100%);
    transition: width 0.3s;
  }
}

.question-section {
  flex: 1;
  padding: 16px;
  overflow-y: auto;
  -webkit-overflow-scrolling: touch;

  .question-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;

    .question-type {
      font-size: 13px;
      color: #1677ff;
      background: #e6f7ff;
      padding: 4px 12px;
      border-radius: 12px;
    }

    .question-score {
      font-size: 15px;
      font-weight: 600;
      color: #f59e0b;
    }
  }

  .question-title {
    font-size: 16px;
    line-height: 1.6;
    color: #333;
    margin-bottom: 20px;
    font-weight: 500;
  }

  .options {
    .option-item {
      display: block;
      padding: 12px;
      margin-bottom: 10px;
      background: #f9fafb;
      border: 1px solid #e5e7eb;
      border-radius: 8px;
      font-size: 15px;
      line-height: 1.5;
      transition: all 0.2s;

      &:active {
        background: #eff6ff;
        border-color: #3b82f6;
      }
    }
  }
}

.footer {
  height: 60px;
  background: #ffffff;
  border-top: 1px solid #e5e7eb;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 16px;
  gap: 12px;
  position: sticky;
  bottom: 0;

  button {
    flex: 1;
    height: 44px;
    font-size: 15px;
  }
}

.loading {
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 100vh;
  font-size: 16px;
  color: #999;
}

