import { useEffect, useState } from "react";
import { useNavigate, useParams, useSearchParams } from "react-router-dom";
import { exam as Exam } from "../../api/index";
import styles from "./answer.module.scss";
import { Button, Card, Toast, Checkbox, Radio, Modal } from "antd-mobile";
import { ClockCircleOutline } from "antd-mobile-icons";

const ExamAnswerPage = () => {
  const navigate = useNavigate();
  const params = useParams();
  const [searchParams] = useSearchParams();
  const courseId = searchParams.get("courseId");
  
  const [loading, setLoading] = useState(true);
  const [exam, setExam] = useState<any>(null);
  const [questions, setQuestions] = useState<any[]>([]);
  const [currentIndex, setCurrentIndex] = useState(0);
  const [answers, setAnswers] = useState<{ [key: number]: any }>({});
  const [timeLeft, setTimeLeft] = useState(0);

  useEffect(() => {
    loadExam();
  }, [params.examId]);

  useEffect(() => {
    if (timeLeft > 0) {
      const timer = setTimeout(() => setTimeLeft(timeLeft - 1), 1000);
      return () => clearTimeout(timer);
    } else if (timeLeft === 0 && exam) {
      handleSubmit();
    }
  }, [timeLeft]);

  const loadExam = () => {
    Exam.examDetail(Number(params.examId))
      .then((res: any) => {
        console.log('📝 [H5 Answer] 考试数据:', res.data);
        console.log('📝 [H5 Answer] 题目列表:', res.data.questions);
        if (res.data.questions && res.data.questions.length > 0) {
          console.log('📝 [H5 Answer] 第一题完整数据:', JSON.stringify(res.data.questions[0]));
          console.log('📝 [H5 Answer] 第一题所有字段:', Object.keys(res.data.questions[0]));
        }
        setExam(res.data);
        setQuestions(res.data.questions || []);
        setTimeLeft((res.data.duration || 60) * 60);
        setLoading(false);
      })
      .catch(() => {
        Toast.show({ icon: 'fail', content: '加载考试失败' });
        setLoading(false);
      });
  };

  const handleAnswer = (questionId: number, value: any) => {
    setAnswers({ ...answers, [questionId]: value });
  };

  const handleSubmit = () => {
    Modal.confirm({
      title: '确认提交',
      content: '提交后将无法修改答案，确定要提交吗？',
      onConfirm: () => {
        const answerList = Object.keys(answers).map((qid) => ({
          question_id: Number(qid),
          answer: Array.isArray(answers[Number(qid)]) ? answers[Number(qid)] : [answers[Number(qid)]]
        }));

        Exam.submitExam(Number(params.examId), { answers: answerList })
          .then((res: any) => {
            Toast.show({ icon: 'success', content: '提交成功' });
            navigate(`/exam/${params.examId}/result?courseId=${courseId}`);
          })
          .catch(() => {
            Toast.show({ icon: 'fail', content: '提交失败' });
          });
      }
    });
  };

  const currentQuestion = questions[currentIndex];
  const progress = ((currentIndex + 1) / questions.length) * 100;

  const formatTime = (seconds: number) => {
    const h = Math.floor(seconds / 3600);
    const m = Math.floor((seconds % 3600) / 60);
    const s = seconds % 60;
    return `${h.toString().padStart(2, '0')}:${m.toString().padStart(2, '0')}:${s.toString().padStart(2, '0')}`;
  };

  if (loading) {
    return <div className={styles["loading"]}>加载中...</div>;
  }

  return (
    <div className={styles["container"]}>
      {/* 顶部状态栏 */}
      <div className={styles["header"]}>
        <div className={styles["timer"]}>
          <ClockCircleOutline />
          <span>{formatTime(timeLeft)}</span>
        </div>
        <div className={styles["progress"]}>
          {currentIndex + 1} / {questions.length}
        </div>
      </div>

      {/* 进度条 */}
      <div className={styles["progress-bar"]}>
        <div className={styles["progress-fill"]} style={{ width: `${progress}%` }}></div>
      </div>

      {/* 题目内容 */}
      {currentQuestion && (
        <div className={styles["question-section"]}>
          <Card>
            <div className={styles["question-header"]}>
              <span className={styles["question-type"]}>
                {currentQuestion.type === 1 ? "单选题" : "多选题"}
              </span>
              <span className={styles["question-score"]}>{currentQuestion.score} 分</span>
            </div>
            <div className={styles["question-title"]}>
              {currentQuestion.title}
            </div>

            <div className={styles["options"]}>
              {currentQuestion.type === 1 ? (
                <Radio.Group
                  value={answers[currentQuestion.id]}
                  onChange={(val) => handleAnswer(currentQuestion.id, val)}
                >
                  {currentQuestion.options?.map((opt: any) => (
                    <Radio key={opt.id} value={opt.option_key} className={styles["option-item"]}>
                      {opt.option_key}. {opt.content}
                    </Radio>
                  ))}
                </Radio.Group>
              ) : (
                <Checkbox.Group
                  value={answers[currentQuestion.id] || []}
                  onChange={(val) => handleAnswer(currentQuestion.id, val)}
                >
                  {currentQuestion.options?.map((opt: any) => (
                    <Checkbox key={opt.id} value={opt.option_key} className={styles["option-item"]}>
                      {opt.option_key}. {opt.content}
                    </Checkbox>
                  ))}
                </Checkbox.Group>
              )}
            </div>
          </Card>
        </div>
      )}

      {/* 底部操作栏 */}
      <div className={styles["footer"]}>
        <Button
          disabled={currentIndex === 0}
          onClick={() => setCurrentIndex(currentIndex - 1)}
        >
          上一题
        </Button>
        {currentIndex < questions.length - 1 ? (
          <Button
            color="primary"
            onClick={() => setCurrentIndex(currentIndex + 1)}
          >
            下一题
          </Button>
        ) : (
          <Button color="success" onClick={handleSubmit}>
            提交答案
          </Button>
        )}
      </div>
    </div>
  );
};

export default ExamAnswerPage;

