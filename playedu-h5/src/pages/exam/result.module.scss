.container {
  min-height: 100vh;
  background: #f5f7fa;
  padding: 20px 16px;
}

.result-pass {
  text-align: center;
  padding: 40px 20px;
  background: linear-gradient(135deg, #10b981 0%, #059669 100%);
  border-radius: 16px;
  color: white;
  margin-bottom: 20px;
  box-shadow: 0 4px 16px rgba(16, 185, 129, 0.3);

  .result-icon {
    margin-bottom: 16px;
    animation: scaleIn 0.5s;
  }

  .result-status {
    font-size: 24px;
    font-weight: 700;
    margin-bottom: 12px;
  }

  .result-score {
    font-size: 48px;
    font-weight: 700;
    margin-bottom: 8px;
  }

  .wrong-hint {
    font-size: 14px;
    opacity: 0.9;
  }
}

.result-fail {
  text-align: center;
  padding: 40px 20px;
  background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
  border-radius: 16px;
  color: white;
  margin-bottom: 20px;
  box-shadow: 0 4px 16px rgba(239, 68, 68, 0.3);

  .result-icon {
    margin-bottom: 16px;
    animation: shake 0.5s;
  }

  .result-status {
    font-size: 24px;
    font-weight: 700;
    margin-bottom: 12px;
  }

  .result-score {
    font-size: 48px;
    font-weight: 700;
    margin-bottom: 8px;
  }

  .wrong-hint {
    font-size: 14px;
    opacity: 0.9;
  }
}

.stats {
  margin-bottom: 20px;

  .stat-grid {
    display: flex;
    align-items: center;
    justify-content: space-around;

    .stat-item {
      flex: 1;
      text-align: center;

      .stat-label {
        font-size: 13px;
        color: #999;
        margin-bottom: 8px;
      }

      .stat-value {
        font-size: 24px;
        font-weight: 700;
        color: #333;
      }
    }

    .stat-divider {
      width: 1px;
      height: 40px;
      background: #e5e7eb;
    }
  }
}

.actions {
  margin-top: 24px;
}

.loading {
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 100vh;
  font-size: 16px;
  color: #999;
}

// 错题详情样式
.wrong-item {
  padding: 8px 0;

  .wrong-header {
    display: flex;
    justify-content: space-between;
    margin-bottom: 8px;

    .wrong-number {
      font-size: 14px;
      font-weight: 600;
      color: #f56565;
    }

    .wrong-type {
      font-size: 13px;
      color: #999;
      background: #f0f0f0;
      padding: 2px 8px;
      border-radius: 4px;
    }
  }

  .wrong-title {
    font-size: 15px;
    color: #333;
    margin-bottom: 10px;
    line-height: 1.6;
  }

  .option {
    font-size: 14px;
    color: #666;
    padding: 8px 12px;
    margin-bottom: 6px;
    background: #f9f9f9;
    border-radius: 6px;
  }

  .option-correct {
    font-size: 14px;
    color: #48bb78;
    padding: 8px 12px;
    margin-bottom: 6px;
    background: #f0fff4;
    border: 1px solid #c6f6d5;
    border-radius: 6px;
    font-weight: 500;

    .correct-mark {
      color: #48bb78;
      font-weight: 600;
    }
  }
}

@keyframes scaleIn {
  from {
    opacity: 0;
    transform: scale(0.5);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

@keyframes shake {
  0%, 100% {
    transform: translateX(0);
  }
  25% {
    transform: translateX(-10px);
  }
  75% {
    transform: translateX(10px);
  }
}

