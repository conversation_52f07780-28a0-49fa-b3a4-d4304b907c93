import client from "./internal/httpClient";

/**
 * 获取课程关联的考试列表
 */
export function courseExams(courseId: number) {
  return client.get(`/api/v1/course/${courseId}/exams`, {});
}

/**
 * 获取考试详情（包括所有问题和选项）
 */
export function examDetail(examId: number) {
  return client.get(`/api/v1/exam/${examId}`, {});
}

/**
 * 提交考试答题
 */
export function submitExam(examId: number, answers: any) {
  return client.post(`/api/v1/exam/${examId}/submit`, answers);
}

/**
 * 获取考试的个人记录（历史答题）
 */
export function userExamRecord(examId: number) {
  return client.get(`/api/v1/exam/${examId}/record`, {});
}

/**
 * 获取考试作答结果
 */
export function examResult(examId: number) {
  return client.get(`/api/v1/exam/${examId}/result`, {});
}

/**
 * 校验考试分数配置（题目总分 vs 及格分数）
 */
export function validateExamScore(examId: number) {
  return client.get(`/api/v1/exam/${examId}/validate`, {});
}

