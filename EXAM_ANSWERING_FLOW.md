# 考试答题功能完整流程文档

## 📋 功能概述

实现了从课程完成 → 考试准备 → 答题 → 结果的完整学员考试答题流程。

## 🏗️ 系统架构

### 后端实现

#### 1. 前端用户 API 接口 (`ExamController` - Frontend)
**路径**: `/api/v1`

| 方法 | 端点 | 说明 |
|------|------|------|
| GET | `/course/{courseId}/exams` | 获取课程关联的所有考试 |
| GET | `/exam/{examId}` | 获取考试详情（包括所有题目和选项）|
| POST | `/exam/{examId}/submit` | 提交答题（TODO：需完整实现） |
| GET | `/exam/{examId}/record` | 获取用户考试记录（TODO） |
| GET | `/exam/{examId}/result` | 获取考试成绩和结果（TODO） |

#### 2. 核心服务层改进
- **ExamService**: 新增 `getExamsByCourseId()` 方法
- **ExamServiceImpl**: 
  - 重写 `detail()` 方法，现在返回考试详情 + 所有题目 + 题目选项
  - 新增 `getExamsByCourseId()` 实现，获取课程关联的所有考试

#### 3. 数据模型
```
Exam (考试)
├── id
├── title
├── type (single_choice, multiple_choice)
├── duration (分钟)
├── pass_score
├── questions[] (ExamQuestion[])
│   ├── id
│   ├── title
│   ├── type
│   ├── sort
│   ├── status
│   └── options[] (ExamQuestionOption[])
│       ├── id
│       ├── content
│       ├── option_key (A, B, C, D...)
│       └── is_correct (0/1)
```

### 前端实现

#### 1. API 服务层 (`api/exam.ts`)
```typescript
courseExams(courseId: number)          // 获取课程考试列表
examDetail(examId: number)             // 获取考试详情
submitExam(examId: number, answers)    // 提交答题
userExamRecord(examId: number)         // 获取答题记录
examResult(examId: number)             // 获取考试结果
```

#### 2. 页面组件

##### A. 课程详情页改进 (`pages/course/index.tsx`)
- **新增状态**: `exams[]` - 存储课程关联的考试
- **新增方法**: 
  - `loadCourseExams()` - 加载课程关联的考试
  - `handleStartExam()` - 开始考试（导航到准备页）
- **UI变化**: 课程完成后，显示"开始测试"按钮

**流程**:
```
课程完成 (100%)
   ↓
显示"恭喜你学完此课程!"
   ↓
检查是否有关联考试
   ↓
有 → 显示"开始测试"按钮
   ↓
点击 → 跳转到 /exam/{courseId}/prepare/{examId}
```

##### B. 考试准备页 (`pages/exam/prepare.tsx`)
**路由**: `/exam/:courseId/prepare/:examId`

**功能**:
- 显示课程信息（缩略图、课程名）
- 显示考试信息（考试名、时长、及格分数、题目数量）
- 显示考试须知（4条警告）
- "开始考试"按钮 → 跳转到答题页

**样式文件**: `prepare.module.scss`

##### C. 答题页面 (`pages/exam/answer.tsx`)
**路由**: `/exam/:examId/answer`

**核心功能**:

1. **计时器**
   - 根据考试时长初始化倒计时
   - 自动保存到 `timeRemaining` 状态
   - 时间到自动提交答题

2. **题目导航**
   - 右侧固定导航栏显示所有题目编号
   - **状态表示**:
     - 未作答: 灰色边框
     - 已作答: 绿色背景
     - 当前题: 蓝色背景
   - 点击任意题号直接跳转

3. **答题区**
   - **单选题**: 使用 `Radio.Group`
   - **多选题**: 使用 `Checkbox.Group`
   - 自动保存到 `answers` 对象

4. **上一题/下一题按钮**
   - 边界判断（首题/末题禁用）
   - 快速导航

5. **提交答题**
   - 验证未作答的题目数
   - 如有未作答题目，弹出确认对话框
   - 确认提交后，调用 `submitExam` API
   - 提交成功后导航到结果页 `/exam/{examId}/result`

**样式文件**: `answer.module.scss` (响应式布局)

#### 3. 路由配置 (`routes/index.tsx`)
```typescript
// 在 WithoutHeaderWithoutFooter 布局下添加
{
  path: "/exam/:courseId/prepare/:examId",
  element: <PrivateRoute Component={<ExamPreparePage />} />,
},
{
  path: "/exam/:examId/answer",
  element: <PrivateRoute Component={<ExamAnswerPage />} />,
},
```

## 📱 完整的学员答题流程

```
1. 学员登录并进入课程学习
   ↓
2. 课程学完 (progress = 100%)
   ↓
3. 课程详情页显示"开始测试"按钮
   ↓
4. 点击按钮 → 跳转到考试准备页
   ├─ 显示课程信息和考试详情
   ├─ 显示考试规则
   └─ 点击"开始考试"按钮
   ↓
5. 进入答题页面
   ├─ 顶部显示进度条和倒计时
   ├─ 左侧显示当前题目（单选/多选）
   ├─ 右侧导航栏显示所有题号
   ├─ 作答题目（自动保存）
   └─ 导航到其他题目
   ↓
6. 提交答题
   ├─ 验证是否有未作答的题目
   ├─ 有未作答 → 确认对话框
   └─ 无未作答或用户确认 → 提交
   ↓
7. 提交成功
   ├─ 显示加载中...
   └─ 跳转到结果页 /exam/{examId}/result
   ↓
8. 结果页面 (TODO：后续实现)
   ├─ 显示考试成绩
   ├─ 显示是否及格
   └─ 显示错误题目分析
```

## 🔌 API 调用顺序

### 后端接口
1. **`GET /backend/v1/courses/{courseId}/exams`** 
   - 在课程详情页加载时，获取关联的考试

2. **`GET /api/v1/course/{courseId}/exams`** 
   - 前端用户端点（从前端课程页调用）

3. **`GET /api/v1/exam/{examId}`**
   - 进入准备页和答题页时获取考试详情（包括所有题目和选项）

4. **`POST /api/v1/exam/{examId}/submit`** (TODO)
   - 提交答题时调用（需保存答题记录、计算分数）

5. **`GET /api/v1/exam/{examId}/result`** (TODO)
   - 获取考试结果（成绩、是否及格等）

## 📝 后续任务

### 高优先级
- [ ] 实现 `ExamController.submit()` 方法
  - 保存学员答题记录到数据库
  - 根据答案计算得分
  - 判断是否及格
  - 返回成绩和通过/失败状态

- [ ] 实现 `ExamController.result()` 方法
  - 返回学员的考试成绩
  - 返回是否及格
  - 返回错题分析（可选）

- [ ] 创建表：`exam_records` 或 `exam_answers`
  - 存储学员的答题记录
  - 存储答题时间、分数等

### 中优先级
- [ ] 前端考试结果页面
  - 显示成绩、及格/未及格状态
  - 显示题目和错题分析
  - 提供重新考试按钮（如允许）

- [ ] 考试记录查询
  - 学员查看历次考试成绩
  - 后台管理员查看所有学员的成绩

### 低优先级
- [ ] 单题分析功能
  - 显示正确答案
  - 显示题目解析

- [ ] 考试时间超限时的优雅处理
  - 自动保存进度
  - 自动提交

## 🔧 开发环境

- **前端**: React 18 + TypeScript + Vite
- **后端**: Spring Boot 3.3.4 + MyBatis-Plus
- **前端服务端口**: 9797
- **后端服务端口**: 9898

## 🧪 测试步骤

1. **启动后端**
   ```bash
   cd playedu-api
   java -jar playedu-api/target/playedu-api.jar
   ```

2. **启动前端**
   ```bash
   cd playedu-pc
   npm run dev
   ```

3. **测试流程**
   - 登录 playedu-pc (http://localhost:9797)
   - 进入已关联考试的课程
   - 完成课程学习 (100%)
   - 点击"开始测试"按钮
   - 进入准备页，点击"开始考试"
   - 作答所有题目
   - 点击"提交答题"提交

## 📊 数据库表关系

```
courses (课程)
    ↓
course_exam (课程-考试关联)
    ↓
exams (考试)
    ↓
exam_questions (考试题目)
    ↓
exam_question_options (题目选项)
    ↓
exam_answers (学员答题记录) - TODO: 需要创建
```

## ✅ 当前完成状态

- ✅ 后端 API 框架
- ✅ 前端页面框架
- ✅ 路由配置
- ✅ 考试数据获取
- ✅ 答题UI和交互
- ✅ 倒计时功能
- ⏳ 答题提交处理 (TODO)
- ⏳ 成绩计算 (TODO)
- ⏳ 结果显示页面 (TODO)
