FROM registry.cn-hangzhou.aliyuncs.com/hzbs/node:20-alpine AS builder

WORKDIR /app

COPY playedu-h5 .

RUN npm install --legacy-peer-deps

ENV VITE_APP_URL=http://localhost:9700

RUN npm run build

FROM registry.cn-hangzhou.aliyuncs.com/hzbs/node:20-alpine

WORKDIR /app

RUN npm install -g http-server

COPY --from=builder /app/dist /app/dist

EXPOSE 80

CMD ["http-server", "/app/dist", "-p", "80", "--cors"]
